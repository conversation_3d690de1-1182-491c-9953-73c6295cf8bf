{"update_url": "https://clients2.google.com/service/update2/crx", "manifest_version": 3, "name": "Aliexpress Analyzer", "version": "1.7.9", "description": "This extension can help you view information and historical data trends of AliExpress stores and their products.", "author": "ixspy", "default_locale": "en", "icons": {"48": "img/smt.png", "128": "img/smt.png"}, "devtools_page": "html/devtools.html", "permissions": ["tabs", "downloads", "cookies", "storage", "notifications"], "host_permissions": ["http://ali.com/*", "http://ixspy.local.test/*", "https://*.ixspy.com/*", "https://*.ixspy.cn/*", "https://ixspy.com/*", "http://aliexpress.int.ixspy.com/*", "https://user.ixspy.com/*", "https://www.aliexpress.com/*", "https://*.aliexpress.com/*", "https://*.aliexpress.ru/*", "https://*.aliexpress.us/*", "*://*.ixspy.com/*", "https://sycm.aliexpress.com/*", "https://csp.aliexpress.com/*"], "action": {"default_icon": "img/smt.png"}, "content_scripts": [{"exclude_matches": ["https://dchain-web.aliexpress.com/*", "https://so.aliexpress.com/*", "https://gsp.aliexpress.com/apps/order/*", "https://seller.aliexpress.com/*"], "matches": ["https://www.aliexpress.com/*", "https://aliexpress.ru/*", "https://www.aliexpress.ru/*", "https://aliexpress.us/*", "https://www.aliexpress.us/*", "https://*.aliexpress.com/store/*", "https://*.aliexpress.ru/store/*", "https://*.aliexpress.us/store/*", "https://*.aliexpress.com/*", "https://fr.aliexpress.com/*", "https://es.aliexpress.com/*", "https://pt.aliexpress.com/*", "https://de.aliexpress.com/*", "https://it.aliexpress.com/*", "https://nl.aliexpress.com/*", "https://tr.aliexpress.com/*", "https://ja.aliexpress.com/*", "https://ko.aliexpress.com/*", "https://th.aliexpress.com/*", "https://vi.aliexpress.com/*", "https://ar.aliexpress.com/*", "https://he.aliexpress.com/*", "https://pl.aliexpress.com/*", "https://id.aliexpress.com/*", "https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/*", "https://sycm.aliexpress.com/*", "https://gsp.aliexpress.com/apps/sycm/keyword/*", "https://csp.aliexpress.com/*"], "js": ["js/category.js", "js/jquery.js", "js/echarts.min.js", "js/words.js", "js/common_func.js", "js/content_script_convey.js", "js/content_script_dom.js", "js/appendSmAndTj.js", "js/longWords.js"], "run_at": "document_end"}], "background": {"service_worker": "background.js", "type": "module"}, "web_accessible_resources": [{"resources": ["js/jquery.js", "js/xhr.js", "js/exten.cookie.js", "js/xlsx.full.min.js", "js/common_func.js", "js/bootstrap.min.js", "js/words.js", "js/echarts.min.js", "js/jszip.js", "js/filesaver.js", "css/bootstrap.min.css", "css/plugin.css", "img/*.png", "img/loading_gif.gif", "fonts/glyphicons-halflings-regular.ttf", "fonts/glyphicons-halflings-regular.woff2", "fonts/glyphicons-halflings-regular.woff", "js/inject_web.js"], "matches": ["https://www.aliexpress.com/*", "https://aliexpress.ru/*", "https://www.aliexpress.ru/*", "https://aliexpress.us/*", "https://www.aliexpress.us/*", "https://*.aliexpress.com/*", "https://*.aliexpress.ru/*", "https://fr.aliexpress.com/*", "https://es.aliexpress.com/*", "https://pt.aliexpress.com/*", "https://de.aliexpress.com/*", "https://it.aliexpress.com/*", "https://nl.aliexpress.com/*", "https://tr.aliexpress.com/*", "https://ja.aliexpress.com/*", "https://ko.aliexpress.com/*", "https://th.aliexpress.com/*", "https://vi.aliexpress.com/*", "https://ar.aliexpress.com/*", "https://he.aliexpress.com/*", "https://pl.aliexpress.com/*"], "extension_ids": []}]}