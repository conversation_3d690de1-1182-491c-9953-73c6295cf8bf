define(["../core","../var/rcssNum"],function(d,m){"use strict";return function(r,e,n,s){var t,u,c=20,i=s?function(){return s.cur()}:function(){return d.css(r,e,"")},f=i(),o=n&&n[3]||(d.cssNumber[e]?"":"px"),
// Starting value computation is required for potential unit mismatches
a=r.nodeType&&(d.cssNumber[e]||"px"!==o&&+f)&&m.exec(d.css(r,e));if(a&&a[3]!==o){for(
// Support: Firefox <=54
// Halve the iteration target value to prevent interference from CSS upper bounds (gh-2144)
// Trust units reported by jQuery.css
o=o||a[3],
// Iteratively approximate from a nonzero starting point
a=+(f/=2)||1;c--;)
// Evaluate and update our best guess (doubling guesses that zero out).
// Finish if the scale equals or crosses 1 (making the old*new product non-positive).
d.style(r,e,a+o),(1-u)*(1-(u=i()/f||.5))<=0&&(c=0),a/=u;d.style(r,e,(a*=2)+o),
// Make sure we update the tween properties later on
n=n||[]}return n&&(a=+a||+f||0,
// Apply relative offset (+=/-=) if specified
t=n[1]?a+(n[1]+1)*n[2]:+n[2],s)&&(s.unit=o,s.start=a,s.end=t),t}});