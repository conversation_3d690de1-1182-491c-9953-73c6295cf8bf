define(["../core","../var/support","../ajax"],function(e,t){"use strict";e.ajaxSettings.xhr=function(){try{return new window.XMLHttpRequest}catch(e){}};var i={
// File protocol always yields status code 0, assume 200
0:200,
// Support: IE <=9 only
// #1450: sometimes IE returns 1223 when it should be 204
1223:204},r=e.ajaxSettings.xhr();t.cors=!!r&&"withCredentials"in r,t.ajax=r=!!r,e.ajaxTransport(function(o){var s,a;
// Cross domain only allowed if supported through XMLHttpRequest
if(t.cors||r&&!o.crossDomain)return{send:function(e,t){var r,n=o.xhr();
// Apply custom fields if provided
if(n.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(r in o.xhrFields)n[r]=o.xhrFields[r];
// Override mime type if needed
// Set headers
for(r in o.mimeType&&n.overrideMimeType&&n.overrideMimeType(o.mimeType),
// X-Requested-With header
// For cross-domain requests, seeing as conditions for a preflight are
// akin to a jigsaw puzzle, we simply never set it to be sure.
// (it can always be set on a per-request basis or even using ajaxSetup)
// For same-domain requests, won't change header if already provided.
o.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)n.setRequestHeader(r,e[r]);
// Callback
s=function(e){return function(){s&&(s=a=n.onload=n.onerror=n.onabort=n.ontimeout=n.onreadystatechange=null,"abort"===e?n.abort():"error"===e?
// Support: IE <=9 only
// On a manual native abort, IE9 throws
// errors on any property access that is not readyState
"number"!=typeof n.status?t(0,"error"):t(
// File: protocol always yields status 0; see #8605, #14207
n.status,n.statusText):t(i[n.status]||n.status,n.statusText,
// Support: IE <=9 only
// IE9 has no XHR2 but throws on binary (trac-11426)
// For XHR2 non-text, let the caller handle it (gh-2498)
"text"!==(n.responseType||"text")||"string"!=typeof n.responseText?{binary:n.response}:{text:n.responseText},n.getAllResponseHeaders()))}},
// Listen to events
n.onload=s(),a=n.onerror=n.ontimeout=s("error"),
// Support: IE 9 only
// Use onreadystatechange to replace onabort
// to handle uncaught aborts
void 0!==n.onabort?n.onabort=a:n.onreadystatechange=function(){
// Check readyState before timeout as it changes
4===n.readyState&&
// Allow onerror to be called first,
// but that will not handle a native abort
// Also, save errorCallback to a variable
// as xhr.onerror cannot be accessed
window.setTimeout(function(){s&&a()})},
// Create the abort callback
s=s("abort");try{
// Do send the request (this may raise an exception)
n.send(o.hasContent&&o.data||null)}catch(e){
// #14683: Only rethrow if this hasn't been notified as an error yet
if(s)throw e}},abort:function(){s&&s()}}})});