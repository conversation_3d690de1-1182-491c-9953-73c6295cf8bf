define(["../core","../core/stripAndCollapse","../var/isFunction","../core/parseHTML","../ajax","../traversing","../manipulation","../selector"],function(c,l,p){"use strict";
/**
 * Load a url into a page
 */c.fn.load=function(e,n,t){var a,i,o,s=this,r=e.indexOf(" ");return-1<r&&(a=l(e.slice(r)),e=e.slice(0,r)),
// If it's a function
p(n)?(
// We assume that it's the callback
t=n,n=void 0):n&&"object"==typeof n&&(i="POST"),
// If we have elements to modify, make the request
0<s.length&&c.ajax({url:e,
// If "type" variable is undefined, then "GET" method will be used.
// Make value of this field explicit since
// user can override it through ajaxSetup method
type:i||"GET",dataType:"html",data:n}).done(function(e){
// Save response for use in complete callback
o=arguments,s.html(a?
// If a selector was specified, locate the right elements in a dummy div
// Exclude scripts to avoid IE 'Permission Denied' errors
c("<div>").append(c.parseHTML(e)).find(a):
// Otherwise use the full result
e)}).always(t&&function(e,n){s.each(function(){t.apply(this,o||[e.responseText,n,e])})}),this}});