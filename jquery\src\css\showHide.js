define(["../core","../data/var/dataPriv","../css/var/isHiddenWithinTree"],function(h,u,c){"use strict";var y={};function e(e,n){
// Determine new display value for elements that need to change
for(var t,i,s,o,r,d=[],l=0,a=e.length;l<a;l++)(i=e[l]).style&&(t=i.style.display,n?(
// Since we force visibility upon cascade-hidden elements, an immediate (and slow)
// check is required in this first loop unless we have a nonempty display value (either
// inline or about-to-be-restored)
"none"===t&&(d[l]=u.get(i,"display")||null,d[l]||(i.style.display="")),""===i.style.display&&c(i)&&(d[l]=(r=o=void 0,o=(s=i).ownerDocument,s=s.nodeName,(r=y[s])||(o=o.body.appendChild(o.createElement(s)),r=h.css(o,"display"),o.parentNode.removeChild(o),y[s]=r="none"===r?"block":r),r))):"none"!==t&&(d[l]="none",
// Remember what we're overwriting
u.set(i,"display",t)));
// Set the display of the elements in a second loop to avoid constant reflow
for(l=0;l<a;l++)null!=d[l]&&(e[l].style.display=d[l]);return e}return h.fn.extend({show:function(){return e(this,!0)},hide:function(){return e(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){c(this)?h(this).show():h(this).hide()})}}),e});