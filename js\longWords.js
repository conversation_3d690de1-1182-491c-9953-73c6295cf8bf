//该js功能： 主要用来处理长尾词信息内容
// 长尾词收集
//首次加载的词
//入口
function longWords(){if(0!=(
//1.获取当前的url 是否有  SearchText 的字段
content=getQueryVariable("SearchText"))){
//保存当前的搜索词=
saveLongWords(content);
//获取搜索的长尾词提交到后台数据
var o=localStorage.getItem("long_words_like");if(!pub_isEmpty(o)){objInfo=JSON.parse(o);for(var e in content=content.replaceAll("+","-"),objInfo)content,objInfo[e];
// localStorage.setItem('long_words_like',JSON.stringify(temp))
// injectSendParamsToContentScript('', {data:JSON.stringify(temp),cmd:'long_words_add'}, cmd = 'long_words_add', type = 'web_event')
}}}
//保存内容，已存在就不加入了
function saveLongWords(o){var e,n;pub_isEmpty(o)||(e=localStorage.getItem("long_words"),n=[],-1===(n=pub_isEmpty(e)?n:JSON.parse(e)).indexOf(o)&&(n.push(o),localStorage.setItem("long_words",JSON.stringify(n))))}longWords();