define(["../core","../var/document","../ajax"],function(e,n){"use strict";
// Prevent auto-execution of scripts when no explicit dataType was provided (See gh-2432)
e.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),
// Install script dataType
e.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return e.globalEval(t),t}}}),
// Handle cache's special case and crossDomain
e.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),
// Bind script tag hack transport
e.ajaxTransport("script",function(c){
// This transport only deals with cross domain or forced-by-attrs requests
var a,i;if(c.crossDomain||c.scriptAttrs)return{send:function(t,r){a=e("<script>").attr(c.scriptAttrs||{}).prop({charset:c.scriptCharset,src:c.url}).on("load error",i=function(t){a.remove(),i=null,t&&r("error"===t.type?404:200,t.type)}),
// Use native DOM manipulation to avoid our domManip AJAX trickery
n.head.appendChild(a[0])},abort:function(){i&&i()}}})});