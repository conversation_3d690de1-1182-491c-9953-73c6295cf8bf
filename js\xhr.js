//c插入到页面的脚本
console.log("重写请求接口");let insertContent=`<script>
(function () {
    //console.log("执行了重写请求接口");
    var XHR = XMLHttpRequest.prototype;
// Remember references to original methods
    var open = XHR.open;
    var send = XHR.send;

// Overwrite native methods
// Collect data:
    XHR.open = function(method, url) {
        //console.log(method,url)
        this._method = method;
        this._url = url;
        return open.apply(this, arguments);
    };

// Implement "ajaxSuccess" functionality
    XHR.send = function(postData) {
        //console.log(this._url.toString())

        this.addEventListener('load', function() {
            //这里是监听获取以后的操作
            
            // if (this._url.toString().indexOf("product/detail/freight")!== -1){
            //     console.log(this.responseText)
            //     window.postMessage({"responseText":this.responseText,"url":this._url},"*");
            // }

            // /* Request y  */ postData
        });
        return send.apply(this, arguments);
    };
})();
</script>`,fetchNew=`
    <script>
    window.au_fetch=window.fetch;
    window.fetch=function(url){
        return window.au_fetch.apply(window,arguments).then((response) => {
            const reader = response.body.getReader();
            const stream = new ReadableStream({
                start(controller) {
                    function push() {
                        // "done"是一个布尔型，"value"是一个Unit8Array
                        reader.read().then((e) => {
                            let { done, value }=e;
                            // 判断是否还有可读的数据？
                            
                            if (done) {
                                
                                // 告诉浏览器已经结束数据发送
                                controller.close();
                                return;
                            }
                            // 取得数据并将它通过controller发送给浏览器
                            var objV = {}
                       
                            if(url.indexOf('product/detail/freight') !== -1){
                                var valueNew = new TextDecoder("utf-8").decode(value)
                                objV = JSON.parse(valueNew)
                                localStorage.setItem('ships_from',valueNew)
                            }
                            if(url.indexOf('/searchSuggest/1') !== -1){
                                console.log('搜索词建议',url)
                                var valueNew = new TextDecoder("utf-8").decode(value)
                                objV = JSON.parse(valueNew)
                                var valueName = $('#searchInput').attr('value')
                                if(valueName === undefined) valueName = $('#SearchText').val()
                                var params = {query:valueName}
                                var url1 = "https://"+window.location.host+url
                                console.log('url1-我是Fetch',url1)
                                setTimeout(()=>{
                                    injectSendParamsToContentScript('', {"url":url1,cmd:'long_words_url',params:params}, cmd = 'long_words_url', type = 'web_event')

                                },1000)
                            }
                        
                            controller.enqueue(value);
                            push();
                        });
                    }
                    push();
                }
            });
            let ret=new Response(stream, { headers: { "Content-Type": "text/html" } })
            
            return ret;
        });
    };
    
    </script>

`,jsonP=`<script>
  (function () {
  var originalCreateElement = document.createElement
  function changeReqLink (script) {
    
    var src
    Object.defineProperty(script, 'src', {
      get: function () {
        return src
      },
      set: function (newVal) {
        src = newVal
        script.setAttribute('src', newVal)
      },
    })
    var originalSetAttribute = script.setAttribute
    script.setAttribute = function () {

      var args = Array.prototype.slice.call(arguments)
      if (args[0] === 'src' && args[1].includes('callback=')) {
        console.log('请求地址: ' + args[1])
      }
      let url = args[1]
      
      //长尾词的接口
      if(url != undefined && url.indexOf('mtop.relationrecommend.aliexpressrecommend.recommend') !== -1){
        //var temp = $("#search-key").val()
        var temp = $("input[id='search-key']").val()
        //新版长尾词
        if(temp == "" || temp == undefined) temp = $('#search-words').val()

        if(temp == ""){
            originalSetAttribute.call(script, ...args)
            return
        }
       
        var objStr = temp.split(" ")

        if(objStr[objStr.length - 1] != ""){
            console.log('我是JSONP SCript ')

            let inter = setInterval(()=>{
                if($('.search--active--20ny0_q').length > 0){
                    clearInterval(inter)
                    injectSendParamsToContentScript('', {"url":url,cmd:'long_words_url',params:{}}, cmd = 'long_words_url', type = 'web_event')
                }
            },1000) 
        }
   
      }

      originalSetAttribute.call(script, ...args)
    }
  }
  document.createElement = function (tagName) {
    var dom = originalCreateElement.call(document, tagName)
    tagName.toLowerCase() === 'script' && (changeReqLink(dom))
    return dom
  }
})()


</script>`;$("body").after(fetchNew),$("body").after(jsonP),$("body").after(insertContent),window.addEventListener("message",e=>{e.data.type});