define(["./core","./core/isAttached","./var/flat","./var/isFunction","./var/push","./var/rcheckableType","./core/access","./manipulation/var/rtagName","./manipulation/var/rscriptType","./manipulation/wrapMap","./manipulation/getAll","./manipulation/setGlobalEval","./manipulation/buildFragment","./manipulation/support","./data/var/dataPriv","./data/var/dataUser","./data/var/acceptData","./core/DOMEval","./core/nodeName","./core/init","./traversing","./selector","./event"],function(v,h,y,m,c,f,t,i,g,o,T,d,C,x,D,a,s,b,n){"use strict";var
// Support: IE <=10 - 11, Edge 12 - 13 only
// In IE/Edge using regex groups here causes severe slowdowns.
// See https://connect.microsoft.com/IE/feedback/details/1736512/
l=/<script|<style|<link/i,
// checked="checked" or checked
N=/checked\s*(?:[^=]|=\s*.checked.)/i,A=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;
// Prefer a tbody over its parent table for containing new rows
function r(e,t){return n(e,"table")&&n(11!==t.nodeType?t:t.firstChild,"tr")&&v(e).children("tbody")[0]||e}
// Replace/restore the type attribute of script elements for safe DOM manipulation
function k(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function w(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function L(e,t){var n,r,i,o;if(1===t.nodeType){
// 1. Copy private data: events, handlers, etc.
if(D.hasData(e)&&(o=D.get(e).events))for(i in D.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)v.event.add(t,i,o[i][n]);
// 2. Copy user data
a.hasData(e)&&(e=a.access(e),e=v.extend({},e),a.set(t,e))}}
// Fix IE bugs, see support tests
function M(n,r,i,o){
// Flatten any nested arrays
r=y(r);var e,t,a,c,s,l,p=0,u=n.length,h=u-1,f=r[0],d=m(f);
// We can't cloneNode fragments that contain checked, in WebKit
if(d||1<u&&"string"==typeof f&&!x.checkClone&&N.test(f))return n.each(function(e){var t=n.eq(e);d&&(r[0]=f.call(this,e,t.html())),M(t,r,i,o)});if(u&&(t=(e=C(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){
// Use the original fragment for the last item
// instead of the first because it can end up
// being emptied incorrectly in certain situations (#8070).
for(c=(a=v.map(T(e,"script"),k)).length;p<u;p++)s=e,p!==h&&(s=v.clone(s,!0,!0),c)&&
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
v.merge(a,T(s,"script")),i.call(n[p],s,p);if(c)
// Evaluate executable scripts on first document insertion
for(l=a[a.length-1].ownerDocument,
// Reenable scripts
v.map(a,w),p=0;p<c;p++)s=a[p],g.test(s.type||"")&&!D.access(s,"globalEval")&&v.contains(l,s)&&(s.src&&"module"!==(s.type||"").toLowerCase()?
// Optional AJAX dependency, but won't run scripts if not present
v._evalUrl&&!s.noModule&&v._evalUrl(s.src,{nonce:s.nonce||s.getAttribute("nonce")},l):b(s.textContent.replace(A,""),s,l))}return n}function p(e,t,n){for(var r,i=t?v.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||v.cleanData(T(r)),r.parentNode&&(n&&h(r)&&d(T(r,"script")),r.parentNode.removeChild(r));return e}return v.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,c,s,l,p=e.cloneNode(!0),u=h(e);
// Fix IE cloning issues
if(!(x.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||v.isXMLDoc(e)))for(
// We eschew Sizzle here for performance reasons: https://jsperf.com/getall-vs-sizzle/2
a=T(p),r=0,i=(o=T(e)).length;r<i;r++)c=o[r],s=a[r],l=void 0,
// Fails to persist the checked state of a cloned checkbox or radio button.
"input"===(l=s.nodeName.toLowerCase())&&f.test(c.type)?s.checked=c.checked:"input"!==l&&"textarea"!==l||(s.defaultValue=c.defaultValue);
// Copy the events from the original to the clone
if(t)if(n)for(o=o||T(e),a=a||T(p),r=0,i=o.length;r<i;r++)L(o[r],a[r]);else L(e,p);
// Preserve script evaluation history
// Return the cloned set
return 0<(a=T(p,"script")).length&&d(a,!u&&T(e,"script")),p},cleanData:function(e){for(var t,n,r,i=v.event.special,o=0;void 0!==(n=e[o]);o++)if(s(n)){if(t=n[D.expando]){if(t.events)for(r in t.events)i[r]?v.event.remove(n,r):v.removeEvent(n,r,t.handle);
// Support: Chrome <=35 - 45+
// Assign undefined instead of using delete, see Data#remove
n[D.expando]=void 0}n[a.expando]&&(
// Support: Chrome <=35 - 45+
// Assign undefined instead of using delete, see Data#remove
n[a.expando]=void 0)}}}),v.fn.extend({detach:function(e){return p(this,e,!0)},remove:function(e){return p(this,e)},text:function(e){return t(this,function(e){return void 0===e?v.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return M(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||r(this,e).appendChild(e)})},prepend:function(){return M(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=r(this,e)).insertBefore(e,t.firstChild)})},before:function(){return M(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return M(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(
// Prevent memory leaks
v.cleanData(T(e,!1)),
// Remove any remaining nodes
e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return v.clone(this,e,t)})},html:function(e){return t(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;
// See if we can take a shortcut and just use innerHTML
if("string"==typeof e&&!l.test(e)&&!o[(i.exec(e)||["",""])[1].toLowerCase()]){e=v.htmlPrefilter(e);try{for(;n<r;n++)
// Remove element nodes and prevent memory leaks
1===(t=this[n]||{}).nodeType&&(v.cleanData(T(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];
// Make the changes, replacing each non-ignored context element with the new content
return M(this,arguments,function(e){var t=this.parentNode;v.inArray(this,n)<0&&(v.cleanData(T(this)),t)&&t.replaceChild(e,this);
// Force callback invocation
},n)}}),v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){v.fn[e]=function(e){for(var t,n=[],r=v(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),v(r[o])[a](t),
// Support: Android <=4.0 only, PhantomJS 1 only
// .get() because push.apply(_, arraylike) throws on ancient WebKit
c.apply(n,t.get());return this.pushStack(n)}}),v});