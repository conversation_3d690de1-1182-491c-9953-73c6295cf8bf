/*!
 * Sizzle CSS Selector Engine v2.3.6
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://js.foundation/
 *
 * Date: 2021-02-16
 */
!function(n){function f(e,t){return e="0x"+e.slice(1)-65536,t||(
// Replace a hexadecimal escape sequence with the encoded Unicode code point
// Support: IE <=11+
// For values outside the Basic Multilingual Plane (BMP), manually construct a
// surrogate pair
e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function M(e,t){return t?
// U+0000 NULL becomes U+FFFD REPLACEMENT CHARACTER
"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e;
// Other potentially-special ASCII characters get backslash-escaped
}function
// Used for iframes
// See setDocument()
// Removing the function wrapper causes a "Permission Denied"
// error in IE
P(){C()}var e,d,w,i,z,p,F,k,N,a,c,
// Local document vars
C,x,r,E,h,o,u,g,
// Instance-specific data
A="sizzle"+ +new Date,s=n.document,S=0,O=0,j=B(),G=B(),U=B(),m=B(),V=function(e,t){return e===t&&(c=!0),0},
// Instance methods
X={}.hasOwnProperty,t=[],J=t.pop,K=t.push,D=t.push,Q=t.slice,
// Use a stripped-down indexOf as it's faster than native
// https://jsperf.com/thor-indexof-vs-for/5
v=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},W="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",
// Regular expressions
// http://www.w3.org/TR/css3-selectors/#whitespace
l="[\\x20\\t\\r\\n\\f]",
// https://www.w3.org/TR/css-syntax-3/#ident-token-diagram
y="(?:\\\\[\\da-fA-F]{1,6}"+l+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",
// Attribute selectors: http://www.w3.org/TR/selectors/#attribute-selectors
Y="\\["+l+"*("+y+")(?:"+l+
// Operator (capture 2)
"*([*^$|!~]?=)"+l+
// "Attribute values must be CSS identifiers [capture 5]
// or strings [capture 3 or capture 4]"
"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+y+"))|)"+l+"*\\]",Z=":("+y+
// 2. simple (capture 6)
")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+Y+")*)|.*)\\)|)",
// Leading and non-escaped trailing whitespace, capturing some non-whitespace characters preceding the latter
_=new RegExp(l+"+","g"),b=new RegExp("^"+l+"+|((?:^|[^\\\\])(?:\\\\.)*)"+l+"+$","g"),ee=new RegExp("^"+l+"*,"+l+"*"),te=new RegExp("^"+l+"*([>+~]|"+l+")"+l+"*"),ne=new RegExp(l+"|>"),re=new RegExp(Z),oe=new RegExp("^"+y+"$"),T={ID:new RegExp("^#("+y+")"),CLASS:new RegExp("^\\.("+y+")"),TAG:new RegExp("^("+y+"|[*])"),ATTR:new RegExp("^"+Y),PSEUDO:new RegExp("^"+Z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+l+"*(even|odd|(([+-]|)(\\d*)n|)"+l+"*(?:([+-]|)"+l+"*(\\d+)|))"+l+"*\\)|)","i"),bool:new RegExp("^(?:"+W+")$","i"),
// For use in libraries implementing .is()
// We use this for POS matching in `select`
needsContext:new RegExp("^"+l+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+l+"*((?:-\\d)?\\d*)"+l+"*\\)|)(?=[^-]|$)","i")},ie=/HTML$/i,ue=/^(?:input|select|textarea|button)$/i,le=/^h\d$/i,L=/^[^{]+\{\s*\[native \w/,
// Easily-parseable/retrievable ID or TAG or CLASS selectors
ae=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ce=/[+~]/,
// CSS escapes
// http://www.w3.org/TR/CSS21/syndata.html#escaped-characters
q=new RegExp("\\\\[\\da-fA-F]{1,6}"+l+"?|\\\\([^\\r\\n\\f])","g"),
// CSS string/identifier serialization
// https://drafts.csswg.org/cssom/#common-serializing-idioms
se=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,fe=ve(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});
// Optimize for push.apply( _, NodeList )
try{D.apply(t=Q.call(s.childNodes),s.childNodes),
// Support: Android<4.0
// Detect silently failing push.apply
// eslint-disable-next-line no-unused-expressions
t[s.childNodes.length].nodeType}catch(e){D={apply:t.length?
// Leverage slice if possible
function(e,t){K.apply(e,Q.call(t))}:
// Support: IE<9
// Otherwise append directly
function(e,t){
// Can't trust NodeList.length
for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function I(t,e,n,r){var o,i,u,l,a,c,s=e&&e.ownerDocument,
// nodeType defaults to 9, since context defaults to document
f=e?e.nodeType:9;
// Return early from calls with invalid selector or context
if(n=n||[],"string"!=typeof t||!t||1!==f&&9!==f&&11!==f)return n;
// Try to shortcut find operations (as opposed to filters) in HTML documents
if(!r&&(C(e),e=e||x,E)){
// If the selector is sufficiently simple, try using a "get*By*" DOM method
// (excepting DocumentFragment context, where the methods don't exist)
if(11!==f&&(l=ae.exec(t)))
// ID selector
if(o=l[1]){
// Document context
if(9===f){if(!(c=e.getElementById(o)))return n;
// Element context
// Support: IE, Opera, Webkit
// TODO: identify versions
// getElementById can match elements by name instead of ID
if(c.id===o)return n.push(c),n}else
// Support: IE, Opera, Webkit
// TODO: identify versions
// getElementById can match elements by name instead of ID
if(s&&(c=s.getElementById(o))&&g(e,c)&&c.id===o)return n.push(c),n;
// Type selector
}else{if(l[2])return D.apply(n,e.getElementsByTagName(t)),n;
// Class selector
if((o=l[3])&&d.getElementsByClassName&&e.getElementsByClassName)return D.apply(n,e.getElementsByClassName(o)),n}
// Take advantage of querySelectorAll
if(d.qsa&&!m[t+" "]&&(!h||!h.test(t))&&(
// Support: IE 8 only
// Exclude object elements
1!==f||"object"!==e.nodeName.toLowerCase())){
// qSA considers elements outside a scoping root when evaluating child or
// descendant combinators, which is not what we want.
// In such cases, we work around the behavior by prefixing every selector in the
// list with an ID selector referencing the scope context.
// The technique has to be used as well when a leading combinator is used
// as such selectors are not recognized by querySelectorAll.
// Thanks to Andrew Dupont for this technique.
if(c=t,s=e,1===f&&(ne.test(t)||te.test(t))){for(
// Expand context for sibling selectors
// We can use :scope instead of the ID hack if the browser
// supports it & if we're not changing the context.
(s=ce.test(t)&&ge(e.parentNode)||e)===e&&d.scope||(
// Capture the context ID, setting it first if necessary
(u=e.getAttribute("id"))?u=u.replace(se,M):e.setAttribute("id",u=A)),i=(
// Prefix every selector in the list
a=p(t)).length;i--;)a[i]=(u?"#"+u:":scope")+" "+ye(a[i]);c=a.join(",")}try{return D.apply(n,s.querySelectorAll(c)),n}catch(e){m(t,!0)}finally{u===A&&e.removeAttribute("id")}}}
// All others
return k(t.replace(b,"$1"),e,n,r)}
/**
 * Create key-value caches of limited size
 * @returns {function(string, object)} Returns the Object data after storing it on itself with
 *	property name the (space-suffixed) string and (if the cache is larger than Expr.cacheLength)
 *	deleting the oldest entry
 */function B(){var n=[];function r(e,t){
// Use (key + " ") to avoid collision with native prototype properties (see Issue #157)
return n.push(e+" ")>w.cacheLength&&
// Only keep the most recent entries
delete r[n.shift()],r[e+" "]=t}return r}
/**
 * Mark a function for special use by Sizzle
 * @param {Function} fn The function to mark
 */function R(e){return e[A]=!0,e}
/**
 * Support testing using an element
 * @param {Function} fn Passed the created element and returns a boolean result
 */function $(e){var t=x.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{
// Remove from its parent by default
t.parentNode&&t.parentNode.removeChild(t);
// release memory in IE
}}
/**
 * Adds the same handler for all of the specified attrs
 * @param {String} attrs Pipe-separated list of attributes
 * @param {Function} handler The method that will be applied
 */function de(e,t){for(var n=e.split("|"),r=n.length;r--;)w.attrHandle[n[r]]=t}
/**
 * Checks document order of two siblings
 * @param {Element} a
 * @param {Element} b
 * @returns {Number} Returns less than 0 if a precedes b, greater than 0 if a follows b
 */function pe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;
// Use IE sourceIndex if available on both nodes
if(r)return r;
// Check if b follows a
if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}
/**
 * Returns a function to use in pseudos for input types
 * @param {String} type
 */
/**
 * Returns a function to use in pseudos for :enabled/:disabled
 * @param {Boolean} disabled true for :disabled; false for :enabled
 */
function he(t){
// Known :disabled false positives: fieldset[disabled] > legend:nth-of-type(n+2) :can-disable
return function(e){
// Only certain elements can match :enabled or :disabled
// https://html.spec.whatwg.org/multipage/scripting.html#selector-enabled
// https://html.spec.whatwg.org/multipage/scripting.html#selector-disabled
return"form"in e?
// Check for inherited disabledness on relevant non-disabled elements:
// * listed form-associated elements in a disabled fieldset
//   https://html.spec.whatwg.org/multipage/forms.html#category-listed
//   https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled
// * option elements in a disabled optgroup
//   https://html.spec.whatwg.org/multipage/forms.html#concept-option-disabled
// All such elements have a "form" property.
e.parentNode&&!1===e.disabled?
// Option elements defer to a parent optgroup if present
"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||
// Where there is no isDisabled, check manually
/* jshint -W018 */
e.isDisabled!==!t&&fe(e)===t:e.disabled===t:"label"in e&&e.disabled===t;
// Remaining elements are neither :enabled nor :disabled
}}
/**
 * Returns a function to use in pseudos for positionals
 * @param {Function} fn
 */function H(u){return R(function(i){return i=+i,R(function(e,t){
// Match elements found at the specified indexes
for(var n,r=u([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}
/**
 * Checks a node for validity as a Sizzle context
 * @param {Element|Object=} context
 * @returns {Element|Object|Boolean} The input node if acceptable, otherwise a falsy value
 */function ge(e){return e&&void 0!==e.getElementsByTagName&&e}
// Expose support vars for convenience
// Add button/input type pseudos
for(e in d=I.support={},
/**
 * Detects XML nodes
 * @param {Element|Object} elem An element or a document
 * @returns {Boolean} True iff elem is a non-HTML XML node
 */
z=I.isXML=function(e){var t=e&&e.namespaceURI,e=e&&(e.ownerDocument||e).documentElement;
// Support: IE <=8
// Assume HTML when documentElement doesn't yet exist, such as inside loading iframes
// https://bugs.jquery.com/ticket/4833
return!ie.test(t||e&&e.nodeName||"HTML")},
/**
 * Sets document-related variables once based on the current document
 * @param {Element|Object} [doc] An element or document object to use to set the document
 * @returns {Object} Returns the current document
 */
C=I.setDocument=function(e){var e=e?e.ownerDocument||e:s;
// Return early if doc is invalid or already selected
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
return e!=x&&9===e.nodeType&&e.documentElement&&(
// Update global variables
r=(x=e).documentElement,E=!z(x),
// Support: IE 9 - 11+, Edge 12 - 18+
// Accessing iframe documents after unload throws "permission denied" errors (jQuery #13936)
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
s!=x&&(e=x.defaultView)&&e.top!==e&&(
// Support: IE 11, Edge
e.addEventListener?e.addEventListener("unload",P,!1):e.attachEvent&&e.attachEvent("onunload",P)),
// Support: IE 8 - 11+, Edge 12 - 18+, Chrome <=16 - 25 only, Firefox <=3.6 - 31 only,
// Safari 4 - 5 only, Opera <=11.6 - 12.x only
// IE/Edge & older browsers don't support the :scope pseudo-class.
// Support: Safari 6.0 only
// Safari 6.0 supports :scope but it's an alias of :root there.
d.scope=$(function(e){return r.appendChild(e).appendChild(x.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),
/* Attributes
	---------------------------------------------------------------------- */
// Support: IE<8
// Verify that getAttribute really returns attributes and not properties
// (excepting IE8 booleans)
d.attributes=$(function(e){return e.className="i",!e.getAttribute("className")}),
/* getElement(s)By*
	---------------------------------------------------------------------- */
// Check if getElementsByTagName("*") returns only elements
d.getElementsByTagName=$(function(e){return e.appendChild(x.createComment("")),!e.getElementsByTagName("*").length}),
// Support: IE<9
d.getElementsByClassName=L.test(x.getElementsByClassName),
// Support: IE<10
// Check if getElementById returns elements by name
// The broken getElementById methods don't pick up programmatically-set names,
// so use a roundabout getElementsByName test
d.getById=$(function(e){return r.appendChild(e).id=A,!x.getElementsByName||!x.getElementsByName(A).length}),
// ID filter and find
d.getById?(w.filter.ID=function(e){var t=e.replace(q,f);return function(e){return e.getAttribute("id")===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&E)return(t=t.getElementById(e))?[t]:[]}):(w.filter.ID=function(e){var t=e.replace(q,f);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},
// Support: IE 6 - 7 only
// getElementById is not reliable as a find shortcut
w.find.ID=function(e,t){if(void 0!==t.getElementById&&E){var n,r,o,i=t.getElementById(e);if(i){if((
// Verify the id attribute
n=i.getAttributeNode("id"))&&n.value===e)return[i];
// Fall back on getElementsByName
for(o=t.getElementsByName(e),r=0;i=o[r++];)if((n=i.getAttributeNode("id"))&&n.value===e)return[i]}return[]}}),
// Tag
w.find.TAG=d.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):d.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],o=0,
// By happy coincidence, a (broken) gEBTN appears on DocumentFragment nodes too
i=t.getElementsByTagName(e);
// Filter out possible comments
if("*"!==e)return i;for(;n=i[o++];)1===n.nodeType&&r.push(n);return r},
// Class
w.find.CLASS=d.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&E)return t.getElementsByClassName(e)},
/* QSA/matchesSelector
	---------------------------------------------------------------------- */
// QSA and matchesSelector support
// matchesSelector(:active) reports false when true (IE9/Opera 11.5)
o=[],
// qSa(:focus) reports false when true (Chrome 21)
// We allow this because of a bug in IE8/9 that throws an error
// whenever `document.activeElement` is accessed on an iframe
// So, we allow :focus to pass through QSA all the time to avoid the IE error
// See https://bugs.jquery.com/ticket/13378
h=[],(d.qsa=L.test(x.querySelectorAll))&&(
// Build QSA regex
// Regex strategy adopted from Diego Perini
$(function(e){var t;
// Select is set to empty string on purpose
// This is to test IE's treatment of not explicitly
// setting a boolean content attribute,
// since its presence should be enough
// https://bugs.jquery.com/ticket/12359
r.appendChild(e).innerHTML="<a id='"+A+"'></a><select id='"+A+"-\r\\' msallowcapture=''><option selected=''></option></select>",
// Support: IE8, Opera 11-12.16
// Nothing should be selected when empty strings follow ^= or $= or *=
// The test attribute must be unknown in Opera but "safe" for WinRT
// https://msdn.microsoft.com/en-us/library/ie/hh465388.aspx#attribute_section
e.querySelectorAll("[msallowcapture^='']").length&&h.push("[*^$]="+l+"*(?:''|\"\")"),
// Support: IE8
// Boolean attributes and "value" are not treated correctly
e.querySelectorAll("[selected]").length||h.push("\\["+l+"*(?:value|"+W+")"),
// Support: Chrome<29, Android<4.4, Safari<7.0+, iOS<7.0+, PhantomJS<1.9.8+
e.querySelectorAll("[id~="+A+"-]").length||h.push("~="),(
// Support: IE 11+, Edge 15 - 18+
// IE 11/Edge don't find elements on a `[name='']` query in some cases.
// Adding a temporary attribute to the document before the selection works
// around the issue.
// Interestingly, IE 10 & older don't seem to have the issue.
t=x.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||h.push("\\["+l+"*name"+l+"*="+l+"*(?:''|\"\")"),
// Webkit/Opera - :checked should return selected option elements
// http://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked
// IE8 throws error here and will not see later tests
e.querySelectorAll(":checked").length||h.push(":checked"),
// Support: Safari 8+, iOS 8+
// https://bugs.webkit.org/show_bug.cgi?id=136851
// In-page `selector#id sibling-combinator selector` fails
e.querySelectorAll("a#"+A+"+*").length||h.push(".#.+[+~]"),
// Support: Firefox <=3.6 - 5 only
// Old Firefox doesn't throw on a badly-escaped identifier.
e.querySelectorAll("\\\f"),h.push("[\\r\\n\\f]")}),$(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";
// Support: Windows 8 Native Apps
// The type and name attributes are restricted during .innerHTML assignment
var t=x.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),
// Support: IE8
// Enforce case-sensitivity of name attribute
e.querySelectorAll("[name=d]").length&&h.push("name"+l+"*[*^$|!~]?="),
// FF 3.5 - :enabled/:disabled and hidden elements (hidden elements are still enabled)
// IE8 throws error here and will not see later tests
2!==e.querySelectorAll(":enabled").length&&h.push(":enabled",":disabled"),
// Support: IE9-11+
// IE's :disabled selector does not pick up the children of disabled fieldsets
r.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),
// Support: Opera 10 - 11 only
// Opera 10-11 does not throw on post-comma invalid pseudos
e.querySelectorAll("*,:x"),h.push(",.*:")})),(d.matchesSelector=L.test(u=r.matches||r.webkitMatchesSelector||r.mozMatchesSelector||r.oMatchesSelector||r.msMatchesSelector))&&$(function(e){
// Check to see if it's possible to do matchesSelector
// on a disconnected node (IE 9)
d.disconnectedMatch=u.call(e,"*"),
// This should fail with an exception
// Gecko does not error, returns false instead
u.call(e,"[s!='']:x"),o.push("!=",Z)}),h=h.length&&new RegExp(h.join("|")),o=o.length&&new RegExp(o.join("|")),
/* Contains
	---------------------------------------------------------------------- */
e=L.test(r.compareDocumentPosition),
// Element contains another
// Purposefully self-exclusive
// As in, an element does not contain itself
g=e||L.test(r.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},
/* Sorting
	---------------------------------------------------------------------- */
// Document order sorting
V=e?function(e,t){
// Flag for duplicate removal
var n;
// Sort on method existence if only one input has compareDocumentPosition
return e===t?(c=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(
// Disconnected nodes
1&(
// Calculate position if both inputs belong to the same document
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):
// Otherwise we know they are disconnected
1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?
// Choose the first element that is related to our preferred document
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
e==x||e.ownerDocument==s&&g(s,e)?-1:
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
t==x||t.ownerDocument==s&&g(s,t)?1:a?v(a,e)-v(a,t):0:4&n?-1:1)}:function(e,t){
// Exit early if the nodes are identical
if(e===t)return c=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,u=[e],l=[t];
// Parentless nodes are either documents or disconnected
if(!o||!i)
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
/* eslint-disable eqeqeq */
return e==x?-1:t==x?1:
/* eslint-enable eqeqeq */
o?-1:i?1:a?v(a,e)-v(a,t):0;
// If the nodes are siblings, we can do a quick check
// Otherwise we need full lists of their ancestors for comparison
if(o===i)return pe(e,t);for(n=e;n=n.parentNode;)u.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);
// Walk down the tree looking for a discrepancy
for(;u[r]===l[r];)r++;return r?
// Do a sibling check if the nodes have a common ancestor
pe(u[r],l[r]):
// Otherwise nodes in our document sort first
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
/* eslint-disable eqeqeq */
u[r]==s?-1:l[r]==s?1:
/* eslint-enable eqeqeq */
0}),x},I.matches=function(e,t){return I(e,null,null,t)},I.matchesSelector=function(e,t){if(C(e),d.matchesSelector&&E&&!m[t+" "]&&(!o||!o.test(t))&&(!h||!h.test(t)))try{var n=u.call(e,t);
// IE 9's matchesSelector returns false on disconnected nodes
if(n||d.disconnectedMatch||
// As well, disconnected nodes are said to be in a document
// fragment in IE 9
e.document&&11!==e.document.nodeType)return n}catch(e){m(t,!0)}return 0<I(t,x,null,[e]).length},I.contains=function(e,t){
// Set document vars if needed
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
return(e.ownerDocument||e)!=x&&C(e),g(e,t)},I.attr=function(e,t){
// Set document vars if needed
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
(e.ownerDocument||e)!=x&&C(e);var n=w.attrHandle[t.toLowerCase()],
// Don't get fooled by Object.prototype properties (jQuery #13807)
n=n&&X.call(w.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==n?n:d.attributes||!E?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},I.escape=function(e){return(e+"").replace(se,M)},I.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},
/**
 * Document sorting and removing duplicates
 * @param {ArrayLike} results
 */
I.uniqueSort=function(e){var t,n=[],r=0,o=0;
// Unless we *know* we can detect duplicates, assume their presence
if(c=!d.detectDuplicates,a=!d.sortStable&&e.slice(0),e.sort(V),c){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}
// Clear input after sorting to release objects
// See https://github.com/jquery/sizzle/pull/225
return a=null,e},
/**
 * Utility function for retrieving the text value of an array of DOM nodes
 * @param {Array|Element} elem
 */
i=I.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){
// Use textContent for elements
// innerText usage removed for consistency of new lines (jQuery #11153)
if("string"==typeof e.textContent)return e.textContent;
// Traverse its children
for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue;
// Do not include comment or processing instruction nodes
}else
// If no nodeType, this is expected to be an array
for(;t=e[r++];)
// Do not traverse comment nodes
n+=i(t);return n},(w=I.selectors={
// Can be adjusted by the user
cacheLength:50,createPseudo:R,match:T,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(q,f),
// Move the given value to match[3] whether quoted or unquoted
e[3]=(e[3]||e[4]||e[5]||"").replace(q,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){
/* matches from matchExpr["CHILD"]
				1 type (only|nth|...)
				2 what (child|of-type)
				3 argument (even|odd|\d*|\d*n([+-]\d+)?|...)
				4 xn-component of xn+y argument ([+-]?\d*n|)
				5 sign of xn-component
				6 x of xn-component
				7 sign of y-component
				8 y of y-component
			*/
return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(
// nth-* requires argument
e[3]||I.error(e[0]),
// numeric x and y parameters for Expr.filter.CHILD
// remember that false/true cast respectively to 0/1
e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&I.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return T.CHILD.test(e[0])?null:(
// Accept quoted arguments as-is
e[3]?e[2]=e[4]||e[5]||"":n&&re.test(n)&&(
// Get excess from tokenize (recursively)
t=(t=p(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(
// excess is a negative index
e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(q,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=j[e+" "];return t||(t=new RegExp("(^|"+l+")"+e+"("+l+"|$)"))&&j(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=I.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(_," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,g,m){var y="nth"!==h.slice(0,3),v="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===m?
// Shortcut for :nth-*(n)
function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,u,l,a,c=y!=v?"nextSibling":"previousSibling",s=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b,p=!1;if(s){
// :(first|last|only)-(child|of-type)
if(y){for(;c;){for(u=e;u=u[c];)if(b?u.nodeName.toLowerCase()===f:1===u.nodeType)return!1;
// Reverse direction for :only-* (if we haven't yet done so)
a=c="only"===h&&!a&&"nextSibling"}return!0}
// non-xml :nth-child(...) stores cache data on `parent`
if(a=[v?s.firstChild:s.lastChild],v&&d){for(
// Seek `elem` from a previously-cached index
// ...in a gzip-friendly way
p=(l=(r=(
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
o=(i=(u=s)[A]||(u[A]={}))[u.uniqueID]||(i[u.uniqueID]={}))[h]||[])[0]===S&&r[1])&&r[2],u=l&&s.childNodes[l];u=++l&&u&&u[c]||(
// Fallback to seeking `elem` from the start
p=l=0,a.pop());)
// When found, cache indexes on `parent` and break
if(1===u.nodeType&&++p&&u===e){o[h]=[S,l,p];break}}else
// xml :nth-child(...)
// or :nth-last-child(...) or :nth(-last)?-of-type(...)
if(!1===(
// Use previously-cached element index if available
p=d?l=(r=(
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
o=(i=(
// ...in a gzip-friendly way
u=e)[A]||(u[A]={}))[u.uniqueID]||(i[u.uniqueID]={}))[h]||[])[0]===S&&r[1]:p))
// Use the same loop as above to seek `elem` from the start
for(;(u=++l&&u&&u[c]||(p=l=0,a.pop()))&&((b?u.nodeName.toLowerCase()!==f:1!==u.nodeType)||!++p||(
// Cache the index of each encountered element
d&&((
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
o=(i=u[A]||(u[A]={}))[u.uniqueID]||(i[u.uniqueID]={}))[h]=[S,p]),u!==e)););
// Incorporate the offset, then check against cycle size
return(p-=m)===g||p%g==0&&0<=p/g}}},PSEUDO:function(e,i){
// pseudo-class names are case-insensitive
// http://www.w3.org/TR/selectors/#pseudo-classes
// Prioritize by case sensitivity in case custom pseudos are added with uppercase letters
// Remember that setFilters inherits from pseudos
var t,u=w.pseudos[e]||w.setFilters[e.toLowerCase()]||I.error("unsupported pseudo: "+e);
// The user may use createPseudo to indicate that
// arguments are needed to create the filter function
// just as Sizzle does
return u[A]?u(i):
// But maintain support for old signatures
1<u.length?(t=[e,e,"",i],w.setFilters.hasOwnProperty(e.toLowerCase())?R(function(e,t){for(var n,r=u(e,i),o=r.length;o--;)e[n=v(e,r[o])]=!(t[n]=r[o])}):function(e){return u(e,0,t)}):u}},pseudos:{
// Potentially complex pseudos
not:R(function(e){
// Trim the selector passed to compile
// to avoid treating leading and trailing
// spaces as combinators
var r=[],o=[],l=F(e.replace(b,"$1"));return l[A]?R(function(e,t,n,r){
// Match elements unmatched by `matcher`
for(var o,i=l(e,null,r,[]),u=e.length;u--;)(o=i[u])&&(e[u]=!(t[u]=o))}):function(e,t,n){return r[0]=e,l(r,null,n,o),
// Don't keep the element (issue #299)
r[0]=null,!o.pop()}}),has:R(function(t){return function(e){return 0<I(t,e).length}}),contains:R(function(t){return t=t.replace(q,f),function(e){return-1<(e.textContent||i(e)).indexOf(t)}}),
// "Whether an element is represented by a :lang() selector
// is based solely on the element's language value
// being equal to the identifier C,
// or beginning with the identifier C immediately followed by "-".
// The matching of C against the element's language value is performed case-insensitively.
// The identifier C does not have to be a valid language name."
// http://www.w3.org/TR/selectors/#lang-pseudo
lang:R(function(n){
// lang value must be a valid identifier
return oe.test(n||"")||I.error("unsupported lang: "+n),n=n.replace(q,f).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),
// Miscellaneous
target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===x.activeElement&&(!x.hasFocus||x.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},
// Boolean properties
enabled:he(!1),disabled:he(!0),checked:function(e){
// In CSS3, :checked should return both checked and selected elements
// http://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked
var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){
// Accessing this property makes selected-by-default
// options in Safari work properly
return e.parentNode&&
// eslint-disable-next-line no-unused-expressions
e.parentNode.selectedIndex,!0===e.selected},
// Contents
empty:function(e){
// http://www.w3.org/TR/selectors/#empty-pseudo
// :empty is negated by element (1) or content nodes (text: 3; cdata: 4; entity ref: 5),
//   but not by others (comment: 8; processing instruction: 7; etc.)
// nodeType < 6 works because attributes (2) do not appear as children
for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},
// Element/input types
header:function(e){return le.test(e.nodeName)},input:function(e){return ue.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(
// Support: IE<8
// New HTML5 attribute values (e.g., "search") appear with elem.type === "text"
null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},
// Position-in-collection
first:H(function(){return[0]}),last:H(function(e,t){return[t-1]}),eq:H(function(e,t,n){return[n<0?n+t:n]}),even:H(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:H(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:H(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:H(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}
/**
 * Returns a function to use in pseudos for buttons
 * @param {String} type
 */(e);for(e in{submit:!0,reset:!0})w.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);
// Easy API for creating new setFilters
function me(){}function ye(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ve(u,e,t){var l=e.dir,a=e.next,c=a||l,s=t&&"parentNode"===c,f=O++;return e.first?
// Check against closest ancestor/preceding element
function(e,t,n){for(;e=e[l];)if(1===e.nodeType||s)return u(e,t,n);return!1}:
// Check against all ancestor/preceding elements
function(e,t,n){var r,o,i=[S,f];
// We can't set arbitrary data on XML nodes, so they don't benefit from combinator caching
if(n){for(;e=e[l];)if((1===e.nodeType||s)&&u(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||s)if(
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
o=(o=e[A]||(e[A]={}))[e.uniqueID]||(o[e.uniqueID]={}),a&&a===e.nodeName.toLowerCase())e=e[l]||e;else{if((r=o[c])&&r[0]===S&&r[1]===f)
// Assign to newCache so results back-propagate to previous elements
return i[2]=r[2];
// A match means we're done; a fail means we have to keep checking
if((
// Reuse newcache so results back-propagate to previous elements
o[c]=i)[2]=u(e,t,n))return!0}return!1}}function be(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function we(e,t,n,r,o){for(var i,u=[],l=0,a=e.length,c=null!=t;l<a;l++)!(i=e[l])||n&&!n(i,r,o)||(u.push(i),c&&t.push(l));return u}function Ne(p,h,g,m,y,e){return m&&!m[A]&&(m=Ne(m)),y&&!y[A]&&(y=Ne(y,e)),R(function(e,t,n,r){var o,i,u,l=[],a=[],c=t.length,
// Get initial elements from seed or context
s=e||function(e,t,n){for(var r=0,o=t.length;r<o;r++)I(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),
// Prefilter to get matcher input, preserving a map for seed-results synchronization
f=!p||!e&&h?s:we(s,l,p,n,r),d=g?
// If we have a postFinder, or filtered seed, or non-seed postFilter or preexisting results,
y||(e?p:c||m)?
// ...intermediate processing is necessary
[]:
// ...otherwise use results directly
t:f;
// Find primary matches
// Apply postFilter
if(g&&g(f,d,n,r),m)for(o=we(d,a),m(o,[],n,r),
// Un-match failing elements by moving them back to matcherIn
i=o.length;i--;)(u=o[i])&&(d[a[i]]=!(f[a[i]]=u));if(e){if(y||p){if(y){for(
// Get the final matcherOut by condensing this intermediate into postFinder contexts
o=[],i=d.length;i--;)(u=d[i])&&
// Restore matcherIn since elem is not yet a final match
o.push(f[i]=u);y(null,d=[],o,r)}
// Move matched elements from seed to results to keep them synchronized
for(i=d.length;i--;)(u=d[i])&&-1<(o=y?v(e,u):l[i])&&(e[o]=!(t[o]=u))}
// Add elements to results, through postFinder if defined
}else d=we(d===t?d.splice(c,d.length):d),y?y(null,t,d,r):D.apply(t,d)})}function Ce(m,y){function e(e,t,n,r,o){var i,u,l,a=0,c="0",s=e&&[],f=[],d=N,
// We must always have either seed elements or outermost context
p=e||b&&w.find.TAG("*",o),
// Use integer dirruns iff this is the outermost matcher
h=S+=null==d?1:Math.random()||.1,g=p.length;
// Add elements passing elementMatchers directly to results
// Support: IE<9, Safari
// Tolerate NodeList properties (IE: "length"; Safari: <number>) matching elements by id
for(o&&(
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
N=t==x||t||o);c!==g&&null!=(i=p[c]);c++){if(b&&i){for(u=0,
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
t||i.ownerDocument==x||(C(i),n=!E);l=m[u++];)if(l(i,t||x,n)){r.push(i);break}o&&(S=h)}
// Track unmatched elements for set filters
v&&(
// They will have gone through all possible matchers
(i=!l&&i)&&a--,e)&&s.push(i)}
// `i` is now the count of elements visited above, and adding it to `matchedCount`
// makes the latter nonnegative.
// Apply set filters to unmatched elements
// NOTE: This can be skipped if there are no unmatched elements (i.e., `matchedCount`
// equals `i`), unless we didn't visit _any_ elements in the above loop because we have
// no element matchers and no seed.
// Incrementing an initially-string "0" `i` allows `i` to remain a string only in that
// case, which will result in a "00" `matchedCount` that differs from `i` but is also
// numerically zero.
if(a+=c,v&&c!==a){for(u=0;l=y[u++];)l(s,f,t,n);if(e){
// Reintegrate element matches to eliminate the need for sorting
if(0<a)for(;c--;)s[c]||f[c]||(f[c]=J.call(r));
// Discard index placeholder values to get only actual matches
f=we(f)}
// Add matches to results
D.apply(r,f),
// Seedless set matches succeeding multiple successful matchers stipulate sorting
o&&!e&&0<f.length&&1<a+y.length&&I.uniqueSort(r)}
// Override manipulation of globals by nested matchers
return o&&(S=h,N=d),s}var v=0<y.length,b=0<m.length;return v?R(e):e}me.prototype=w.filters=w.pseudos,w.setFilters=new me,p=I.tokenize=function(e,t){var n,r,o,i,u,l,a,c=G[e+" "];if(c)return t?0:c.slice(0);for(u=e,l=[],a=w.preFilter;u;){
// Filters
for(i in
// Comma and first run
n&&!(r=ee.exec(u))||(r&&(
// Don't consume trailing commas as valid
u=u.slice(r[0].length)||u),l.push(o=[])),n=!1,
// Combinators
(r=te.exec(u))&&(n=r.shift(),o.push({value:n,
// Cast descendant combinators to space
type:r[0].replace(b," ")}),u=u.slice(n.length)),w.filter)!(r=T[i].exec(u))||a[i]&&!(r=a[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),u=u.slice(n.length));if(!n)break}
// Return the length of the invalid excess
// if we're just parsing
// Otherwise, throw an error or return tokens
return t?u.length:u?I.error(e):
// Cache the tokens
G(e,l).slice(0)},F=I.compile=function(e,t/* Internal Use Only */){var n,r=[],o=[],i=U[e+" "];if(!i){for(n=(
// Generate a function of recursive functions that can be used to check each element
t=t||p(e)).length;n--;)((i=function e(t){for(var r,n,o,i=t.length,u=w.relative[t[0].type],l=u||w.relative[" "],a=u?1:0,
// The foundational matcher ensures that elements are reachable from top-level context(s)
c=ve(function(e){return e===r},l,!0),s=ve(function(e){return-1<v(r,e)},l,!0),f=[function(e,t,n){return e=!u&&(n||t!==N)||((r=t).nodeType?c:s)(e,t,n),
// Avoid hanging onto element (issue #299)
r=null,e}];a<i;a++)if(n=w.relative[t[a].type])f=[ve(be(f),n)];else{
// Return special upon seeing a positional matcher
if((n=w.filter[t[a].type].apply(null,t[a].matches))[A]){for(
// Find the next relative operator (if any) for proper handling
o=++a;o<i&&!w.relative[t[o].type];o++);return Ne(1<a&&be(f),1<a&&ye(
// If the preceding token was a descendant combinator, insert an implicit any-element `*`
t.slice(0,a-1).concat({value:" "===t[a-2].type?"*":""})).replace(b,"$1"),n,a<o&&e(t.slice(a,o)),o<i&&e(t=t.slice(o)),o<i&&ye(t))}f.push(n)}return be(f)}(t[n]))[A]?r:o).push(i);
// Cache the compiled function
// Save selector and tokenization
(i=U(e,Ce(o,r))).selector=e}return i},
/**
 * A low-level selection function that works with Sizzle's compiled
 *  selector functions
 * @param {String|Function} selector A selector or a pre-compiled
 *  selector function built with Sizzle.compile
 * @param {Element} context
 * @param {Array} [results]
 * @param {Array} [seed] A set of elements to match against
 */
k=I.select=function(e,t,n,r){var o,i,u,l,a,c="function"==typeof e&&e,s=!r&&p(e=c.selector||e);
// Try to minimize operations if there is only one selector in the list and no seed
// (the latter of which guarantees us context)
if(n=n||[],1===s.length){if(2<(
// Reduce context if the leading compound selector is an ID
i=s[0]=s[0].slice(0)).length&&"ID"===(u=i[0]).type&&9===t.nodeType&&E&&w.relative[i[1].type]){if(!(t=(w.find.ID(u.matches[0].replace(q,f),t)||[])[0]))return n;
// Precompiled matchers will still verify ancestry, so step up a level
c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}
// Fetch a seed set for right-to-left matching
for(o=T.needsContext.test(e)?0:i.length;o--&&(u=i[o],!w.relative[l=u.type]);)if((a=w.find[l])&&(r=a(u.matches[0].replace(q,f),ce.test(i[0].type)&&ge(t.parentNode)||t))){if(
// If seed is empty or no tokens remain, we can return early
i.splice(o,1),e=r.length&&ye(i))break;return D.apply(n,r),n}}
// Compile and execute a filtering function if one is not provided
// Provide `match` to avoid retokenization if we modified the selector above
return(c||F(e,s))(r,t,!E,n,!t||ce.test(e)&&ge(t.parentNode)||t),n},
// One-time assignments
// Sort stability
d.sortStable=A.split("").sort(V).join("")===A,
// Support: Chrome 14-35+
// Always assume duplicates if they aren't passed to the comparison function
d.detectDuplicates=!!c,
// Initialize against the default document
C(),
// Support: Webkit<537.32 - Safari 6.0.3/Chrome 25 (fixed in Chrome 27)
// Detached nodes confoundingly follow *each other*
d.sortDetached=$(function(e){
// Should return 1, but returns 4 (following)
return 1&e.compareDocumentPosition(x.createElement("fieldset"))}),
// Support: IE<8
// Prevent attribute/property "interpolation"
// https://msdn.microsoft.com/en-us/library/ms536429%28VS.85%29.aspx
$(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),
// Support: IE<9
// Use defaultValue in place of getAttribute("value")
d.attributes&&$(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),
// Support: IE<9
// Use getAttributeNode to fetch booleans when getAttribute lies
$(function(e){return null==e.getAttribute("disabled")})||de(W,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null});
// EXPOSE
var xe=n.Sizzle;I.noConflict=function(){return n.Sizzle===I&&(n.Sizzle=xe),I},"function"==typeof define&&define.amd?define(function(){return I}):"undefined"!=typeof module&&module.exports?module.exports=I:n.Sizzle=I}(window);