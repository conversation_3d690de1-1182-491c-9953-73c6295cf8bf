define(["../core","../deferred"],function(e){"use strict";
// These usually indicate a programmer mistake during development,
// warn about them ASAP rather than swallowing them by default.
var o=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;e.Deferred.exceptionHook=function(e,n){
// Support: IE 8 - 9 only
// Console exists when dev tools are open, which can happen at any time
window.console&&window.console.warn&&e&&o.test(e.name)&&window.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)}});