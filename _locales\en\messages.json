{"lang_product": {"message": "Product Overview"}, "lang_store": {"message": "Store Overview"}, "lang_price": {"message": "Product Charts"}, "lang_img": {"message": "Buyers Photos"}, "lang_order": {"message": "Order Details"}, "lang_top_2_products": {"message": "Hot Products"}, "lang_top_2_new_products": {"message": "New Products"}, "lang_store_sales_chart": {"message": "Sales Trend"}, "lang_store_Estimated_sales": {"message": "Estimated sales"}, "lang_store_favted_chart": {"message": "Favorites Trend"}, "lang_store_reviews_chart": {"message": "Reviews Trend"}, "lang_total": {"message": "Total"}, "lang_inc": {"message": "Increment"}, "lang_total_order": {"message": "Total Orders"}, "lang_add_favted": {"message": "Add To Wishlists"}, "lang_same_products": {"message": "Similar Products"}, "lang_sales_auto": {"message": "Sales Trend Auto Expand"}, "lang_shopify_products": {"message": "Shopify Dropshipping Import"}, "lang_sales_chart": {"message": "180-day Sales Trending Chart"}, "lang_review_chart": {"message": "180-day Reviews Trending Chart"}, "lang_favted_chart": {"message": "Wishlists Trending Chart"}, "lang_price_chart": {"message": "Prices Trending Chart"}, "lang_success": {"message": "Success"}, "lang_fail": {"message": "Failed"}, "lang_age": {"message": "Has been opened"}, "lang_reviews": {"message": "Reviews"}, "lang_favted": {"message": "Wishlists"}, "lang_positive": {"message": "Positive Feedback"}, "lang_currency": {"message": "Currency Type"}, "lang_low_price": {"message": "Lowest Price"}, "lang_max_price": {"message": "Highest Price"}, "lang_ratings": {"message": "Ratings"}, "lang_search_on_shopify": {"message": "Find competing products in Shopify"}, "lang_no_login_button": {"message": "Login/Register To Add Favorite"}, "lang_currey": {"message": "Currency Type"}, "lang_product_price": {"message": "Price"}, "lang_product_normal_price": {"message": "Normally Price"}, "all_choice": {"message": "Choice"}, "half_choice": {"message": "Semi-Management"}, "lang_product_rating": {"message": "Rating"}, "lang_product_total_orders": {"message": "180-day Orders"}, "lang_new_login": {"message": "Please login on the newly opened page. After successful login, return to this page for operation"}, "lang_orders": {"message": "Orders"}, "lang_contain": {"message": "I have logged in, refresh the page"}, "lang_product_total_reviews": {"message": "180-day Reviews"}, "lang_product_wishlist": {"message": "Wishlists"}, "lang_same_as_product": {"message": "Same Products"}, "lang_no_info": {"message": "Unable to access the information"}, "lang_login_see": {"message": "Login/Register To View All"}, "lang_already_here": {"message": "Existed"}, "lang_wait_download": {"message": "Downloading, please wait..."}, "lang_login_more": {"message": "Login/Register to try more functions"}, "lang_cow": {"message": "HOT! Explore over 300k+ dropshipping stores to find winning products"}, "lang_my_favted": {"message": "My Wishlists"}, "lang_join_group": {"message": "Join Facebook Group"}, "lang_shop_age": {"message": " Years Old"}, "lang_shop_age_month": {"message": "Months Old"}, "lang_goods_rate": {"message": "<PERSON><PERSON><PERSON>"}, "lang_no_info_tip": {"message": "No relevant information found!"}, "lang_download_img": {"message": "Batch Download"}, "lang_download_img_scription": {"message": "Download description images"}, "lang_download_main_img": {"message": "Download main images"}, "lang_download_sku_img": {"message": "Download SKU images"}, "lang_download_video": {"message": "Download Video"}, "lang_tip_ixspy": {"message": "Welcome to try free AliExpress product research and competitive analysis tool."}, "lang_more_shop_info": {"message": "More analysis of stores..."}, "lang_more_product_info": {"message": "More analysis of products..."}, "lang_install_func": {"message": "Installation method"}, "lang_download_zip": {"message": "Download the extension installation file (zip compressed file), unzip"}, "lang_open_right": {"message": "Open the application center in the upper right corner of the browser, enter the 'Extensions' (or 'My App') page, and check the 'Developer Mode' option in the upper right corner"}, "lang_move_to_page": {"message": "Pull the unzipped folder directly into the page. If the corresponding extension card appears on the page, the installation is successful"}, "lang_install_attention": {"message": "Note: The folder cannot be deleted and moved after installation, otherwise it will be unavailable"}, "lang_plugin_update": {"message": "Extension Update"}, "lang_best_new": {"message": "Download the latest version"}, "lang_hot_store": {"message": "Similar Stores"}, "lang_tip_check_upgrade": {"message": "Click To Update"}, "lang_1688": {"message": "1688 Same Product"}, "lang_more": {"message": "Load More"}, "lang_auto_expand": {"message": "Auto Expand"}, "lang_category": {"message": "Category"}, "lang_a": {"message": "en"}, "lang_notify": {"message": "Notification"}, "lang_first_discover": {"message": "First Discovery Time"}, "lang_last_update": {"message": "Update"}, "lang_year_chart": {"message": "View data for a whole year"}, "lang_data_tj": {"message": "Current Statistics"}, "lang_180_sales": {"message": "180 Days' Sales"}, "lang_180_sales_new": {"message": "180 Days' Sales"}, "lang_7_sales": {"message": "7 Days' Sales"}, "lang_total_wishlist": {"message": "Total Wishlists"}, "lang_total_reviews": {"message": "Total Reviews"}, "lang_product_word": {"message": "Product Words Cloud"}, "lang_180_sales_chart": {"message": "180 Days' Sales Trend Chart"}, "lang_store_name": {"message": "Store"}, "lang_products": {"message": "Products"}, "lang_view_data": {"message": "View Sales Trend Chart By IXSPY"}, "lang_no_data": {"message": "No Data"}, "lang_no_login": {"message": "Please Login in First"}, "lang_no_permissions": {"message": "BUSSINESS, Aliexpress Supplier ,eBay Seller ,Shopify Owner ,Aliexpress Platform Package Can be Use"}, "lang_product_ids_empty": {"message": "There is Some Error <PERSON>ppen"}, "lang_est_chart": {"message": "Estimated Sales Trend Chart"}, "lang_no_favted": {"message": "Add to Wishlists By IXSPY"}, "lang_already_favted": {"message": "Collected"}, "lang_no_lu": {"message": "This product is not included and cannot be added to the Wishlists"}, "lang_login_for_more": {"message": "Unlock more functions after Sign In"}, "lang_close": {"message": "Close"}, "lang_to_login": {"message": "Sign Up"}, "lang_to_reg": {"message": "Sign In"}, "lang_video_jc": {"message": "Video Tutorial"}, "lang_set_up": {"message": "Extension Installation"}, "lang_update_up": {"message": "Extension Update"}, "lang_click_play": {"message": "Click to play video"}, "lang_360_set": {"message": "360 Browser installation tutorial"}, "lang_chrome_set": {"message": "Google Chrome installation tutorial"}, "lang_qq_set": {"message": "QQ Browser installation tutorial"}, "lang_360_update": {"message": "360 Browser update tutorial"}, "lang_chrome_update": {"message": "Google Chrome update tutorial"}, "lang_qq_update": {"message": "QQ Browser update tutorial"}, "lang_is_collecting": {"message": "Collecting..."}, "lang_process": {"message": "Progress"}, "lang_yes": {"message": "Yes"}, "lang_no": {"message": "No"}, "lang_country": {"message": "Country"}, "lang_c_price": {"message": "Price difference"}, "lang_carrier": {"message": "Preferred logistics"}, "lang_is_by": {"message": "Free Shipping"}, "lang_carrier_fee": {"message": "Freight"}, "lang_c_msg": {"message": "The calculation of the price difference is based on Russian prices"}, "lang_check_country_price": {"message": "Product Regional Pricing"}, "lang_export_excel": {"message": "Export to Excel"}, "lang_level_user": {"message": "You have used up the number of times, please upgrade to Aliexpress Supplier/Business member or wait until the next natural month to use"}, "lang_begin_collection_tip": {"message": "Currently used AAA times, and BBB times are available. This collection requires multiple page refreshes. It will take a few minutes. Please wait patiently..."}, "lang_ships_from": {"message": "Ships From"}, "lang_is_stop": {"message": "Stop Collection"}, "lang_begin_collection_tip_no_limit": {"message": "This collection requires multiple page refreshes. It will take a few minutes. Please wait patiently..."}, "lang_shuoming": {"message": "Description"}, "lang_shuoming_p_1": {"message": "1. To facilitate comparison, collect the first style uniformly"}, "lang_shuoming_p_2": {"message": "2. Uniformly calculate the price difference based on the Russian price"}, "lang_carrier_date": {"message": "Estimated Delivery on"}, "lang_repeat_collection": {"message": "Recollect"}, "lang_update_title": {"message": "Update Content"}, "RU": {"message": "Russian Federation"}, "ES": {"message": "Spain"}, "FR": {"message": "France"}, "US": {"message": "America"}, "CL": {"message": "Chile"}, "UA": {"message": "Ukraine"}, "NL": {"message": "Netherlands"}, "PL": {"message": "Poland"}, "BR": {"message": "Brazil"}, "IT": {"message": "Italy"}, "DE": {"message": "Germany"}, "KR": {"message": "Korea"}, "CA": {"message": "Canada"}, "UK": {"message": "United Kingdom"}, "IL": {"message": "Israel"}, "AU": {"message": "Australia"}, "BY": {"message": "Belarus"}, "JP": {"message": "Japan"}, "TH": {"message": "Thailand"}, "SG": {"message": "Singapore"}, "ID": {"message": "Indonesia"}, "MY": {"message": "Malaysia"}, "PH": {"message": "Philippines"}, "VN": {"message": "Viet Nam"}, "SA": {"message": "Saudi Arabia"}, "AE": {"message": "United Arab Emirates"}, "PT": {"message": "Portugal"}, "TR": {"message": "Turkey"}, "lang_all_country": {"message": "All Countries"}, "lang_start_collection": {"message": "Start"}, "chose_country_checkboxs": {"message": "chose_country_checkboxs"}, "country_groups_list": {"message": "country-groups-list"}, "need_chose_countrys": {"message": "In addition to the required country, at least one country must be selected"}, "MX": {"message": "Mexico"}, "KZ": {"message": "Kazakhstan"}, "PE": {"message": "Peru"}, "CO": {"message": "Colombia"}, "DZ": {"message": "Algeria"}, "MA": {"message": "Morocco"}, "NZ": {"message": "New Zealand"}, "LV": {"message": "Latvia"}, "LT": {"message": "Lithuania"}, "BE": {"message": "Belgium"}, "CH": {"message": "Switzerland"}, "CZ": {"message": "Czech Republic"}, "SK": {"message": "Slovakia"}, "NO": {"message": "Norway"}, "HU": {"message": "Hungary"}, "BG": {"message": "Bulgaria"}, "EE": {"message": "Estonia"}, "RO": {"message": "Romania"}, "PK": {"message": "Pakistan"}, "HR": {"message": "Croatia"}, "NG": {"message": "Nigeria"}, "IE": {"message": "Ireland"}, "AT": {"message": "Austria"}, "GR": {"message": "Greece"}, "SE": {"message": "Sweden"}, "FI": {"message": "Finland"}, "DK": {"message": "Denmark"}, "SI": {"message": "Slovenia"}, "MT": {"message": "Malta"}, "LK": {"message": "Sri Lanka"}, "LU": {"message": "Luxembourg"}, "weight": {"message": "Package Weight"}, "size": {"message": "Package Size"}, "lang_sku": {"message": "Based on XXX conditional collection"}, "day_7": {"message": "<div class='inline w-60'>7 Days</div>: <div class='inline color-red'> xxx sold </div>"}, "day_30": {"message": "<div class='inline w-60'>30 Days</div>: <div class='inline color-red'> xxx sold </div>"}, "look_more": {"message": "More..."}, "order_sales": {"message": "Estimated Sales"}, "lang_180_day_sales_bh": {"message": "180-day Sales Daily Change"}, "order_avg_7": {"message": "Avg Orders"}, "order_rate_7": {"message": "Order Growth Rate"}, "favted_avg_7": {"message": "Avg Wishlists"}, "favted_rate_7": {"message": "Wishlist Growth Rate"}, "first_date": {"message": "First Discovery Time"}, "class_seven_value": {"message": "seven-value-en"}, "class_seven_label": {"message": "seven-label-en"}, "seven_label_title": {"message": "seven_label_title_en"}, "curr_lang": {"message": "en"}, "country_dis": {"message": "TOP3 Country"}, "click_favted_product": {"message": "Add To Wishlists"}, "already_favted_product": {"message": "Collected"}, "already_favted_store": {"message": "Added"}, "my_persion_center": {"message": "Personal Center"}, "my_task_center": {"message": "Task Center"}, "quick_enter": {"message": "Fast entry"}, "area_pricing": {"message": "Area Pricing"}, "traffic_reverse_check": {"message": "Traffic Reverse Check"}, "keywords_Monitoring": {"message": "Keywords Monitoring"}, "keywords": {"message": "Keywords"}, "search_popularity": {"message": "Search Popularity"}, "search_index": {"message": "Search Index"}, "click_rate": {"message": "Click Rate"}, "order_conversion_rate": {"message": "Order Conversion Rate"}, "competition_index": {"message": "Competition Index"}, "supply_index": {"message": "Supply Index"}, "times_exhausted": {"message": "This feature has been exhausted"}, "lang_favted_product": {"message": "Aliexpress Product"}, "lang_favted_store": {"message": "Aliexpress Store"}, "lang_open_monitor": {"message": "Turn on Monitoring"}, "lang_check_monitor": {"message": "View Monitoring"}, "lang_monitor_res": {"message": "Monitoring Results Display"}, "lang_support": {"message": "Support By IXSPY.COM"}, "lang_monitor_time": {"message": "Monitoring Time"}, "lang_more_monitor_res": {"message": "More Monitoring Results..."}, "lang_analytics": {"message": "Analytics"}, "lang_source": {"message": "Installation Source:"}, "lang_local": {"message": "Local"}, "lang_google": {"message": "Google Web Store"}, "lang_microsoft": {"message": "Edge Web Store"}, "lang_tip_many_extention": {"message": "You have installed multiple Aliexpress Analyzer plug-ins, in order to ensure the user experience, please open only one Aliexpress Analyzer plug-in"}, "lang_collection_shop": {"message": "Click to Favorites"}, "lang_all_tags": {"message": "All"}, "lang_tags_charge": {"message": "Edit Tags"}, "lang_download_words": {"message": "Download Combination Keyword List"}, "analysis_lang_words": {"message": "Combination Keyword Analysis"}, "lang_rank_score": {"message": "Rank Score"}, "lang_long_words": {"message": "Combination Keyword"}, "lang_tip_no_ru": {"message": "The aliexpress.ru domain does not support regional pricing for products, do you want to switch to aliexpress.com domain?"}, "lang_tag": {"message": "Tag"}, "lang_choice": {"message": "Optional"}, "lang_tip_no": {"message": "Cancel"}, "lang_go_no_aliexpress": {"message": "Yes"}, "lang_already_task": {"message": "Joined the collection queue"}, "lang_task_success": {"message": "Product pricing and freight collection have been added to the task queue. Generally, you will receive a collection completion reminder after 3-5 minutes, please pay attention."}, "lang_task_fail": {"message": "Failed to join the collection task, please try again later or contact our customer service staff"}, "lang_search_img": {"message": "Image Search AliExpress Products"}, "lang_title_search_img": {"message": "Search the same Aliexpress products by image"}, "lang_title_search_img_1688": {"message": "Searching for products by pictures in 1688"}, "lang_is_vip": {"message": "Only members can use"}, "lang_words_link": {"message": "Product Related Keywords"}, "lang_carrier_permissions": {"message": "The current number of times has been used up, please upgrade the membership"}, "lang_no_set_360": {"message": "360 browser does not currently support plug-in installation"}, "lang_no_set_qq": {"message": "QQ browser does not currently support plug-in installation"}, "lang_remark": {"message": "Remark"}, "lang_stow": {"message": "<PERSON><PERSON>"}, "lang_open": {"message": "open"}, "lang_dock_left": {"message": "Dock Left"}, "lang_dock_right": {"message": "Dock Right"}, "lang_wechat": {"message": "Join WhatsApp Group"}, "download_product_keyword": {"message": "Download Keywords"}, "lang_cancel_shop_collection": {"message": "Cancel Favorites"}, "lang_cancel_shop_already": {"message": "Favorites Canceled"}, "lang_the_tag_have_not_goods": {"message": "No favorite products under this tag currently"}, "lang_the_tag_have_not_store": {"message": "No favorite stores under this tag currently"}, "lang_collect_cancel_fail": {"message": "Favorite product cancellation failed"}, "lang_1019": {"message": "The product has not been included yet"}}