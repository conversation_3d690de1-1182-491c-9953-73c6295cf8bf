//1. 判断变量是否为空（字符串，对象，数组）
//2. 对象的深拷贝
//3. 获取时间戳
//4. 时间戳的转换
//5. 字符串中获取数字
//6. 字符串转数组
//7. 数组转字符串
//8. 数字用逗号隔开
//9. 数字转 K  M
//10. 判断字符串 / 数组 中是否存在某个值
//11. url 的验证判断
//12. 判断是否是手机端
//13. 计算屏幕宽度
//14. 时间戳转成国外的日期 / 国内的日期
//15. 时间戳转成 具体的时间，几个小时前，几分钟前，几秒前，日期
//16. 小数转成百分号 
//17. forEach  / map  /every / 的用法
// 1. 判断变量是否为空（字符串，对象，数组）
function pub_isEmpty(t){if(null!=t&&""!=t)switch(typeof t){case"string":return""==t||null==t?!0:!1;case"object":return 0===t.length?!0:null==t.length&&0==Object.keys(t).length;case"number":return!1}return!0}
//2. 对象的深拷贝
function pub_deepCopy(t){return JSON.parse(JSON.stringify(t))}
//3. 获取时间戳
function pub_timeStamp(){return Date.parse(new Date)/1e3}
//4. 时间戳的转换  月-日-年
function pub_timeToMDY(t,e=""){var r;return 0==t||""==t?"-":(t*=1e3,(r=new Date(t)).getTime(t),t=r.toISOString().split("T")[0],"open"!=e?t:(r=t.split("-"))[0]+"-"+r[1])}
//5. 字符串中获取数字
function pub_strToNum(t){return parseInt(t)}
//6. 字符串转数组
function pub_strToObj(t,e){return t.split(e)}
//7. 数组转字符串
function pub_objToStr(t,e){return t.join(e)}
//8. 数字用逗号隔开
function pub_numByCommas(t){return Number(t).toLocaleString()}
//9. 数字转 K  M
function pub_numTransKm(t,e=2){let r=Math.abs(t);return r=(r=1e6<(r=1e3<r&&r<1e6?(r/=1e3).toFixed(e)+"K":r)?(r/=1e6).toFixed(e)+"M":r)<0?"-"+r:r}
//10. 判断字符串 / 数组 中是否存在某个值
//11. url 的验证判断
//12. 判断是否是手机端
//13. 计算屏幕宽度
//14. 时间戳转成国外的日期 / 国内的日期
//15. 时间戳转成 具体的时间，几个小时前，几分钟前，几秒前，日期
//16. 小数转成百分号 
//17. forEach  / map  /every / 的用法
//18. js里过滤 注释符号方便 插入js 使用 dom
function pub_format(t,r){return t.replace(/#\{(.*?)\}/g,function(t,e){return r&&e in r?r[e]:""})}
//19. 画出 折线图 或者柱状图，适合在插件里用，有些配置是已经固定了
function pub_drawLineOrBarChart(t="line",e,r,n,a="yes"){var i,o;null===document.getElementById(e)?console.log(100404,e):(e=echarts.init(document.getElementById(e)),o=0,30<(i=r.length)&&(o=parseInt((i-30)/i*100)),option={tooltip:{trigger:"axis",extraCssText:"width:100px;height:80px;position: absolute;"},xAxis:{type:"category",data:r},yAxis:{type:"value",axisLine:{//y轴
show:!1},axisTick:{show:!1},axisLabel:{//决定是否显示数据
interval:"auto",show:!0,formatter:function(t){var e=[],t=pub_numTransKm(t,1);return e.push(t),e}},max:function(t){return t.max==t.min?Math.ceil(Math.ceil((1.1*t.max).toFixed(1))):Math.ceil(t.max+.1*(t.max-t.min))},min:function(t){return t.max==t.min?Math.floor((.9*t.max).toFixed(1)):0==t.min?0:(t=t.min-.1*(t.max-t.min),Math.floor(t))}},grid:{left:60,right:20},series:[{data:n,type:t,
// areaStyle: {
//     normal: {
//       color: '#fff0eb' // 改变区域颜色
//     }
// },
itemStyle:{normal:{
//color:'#fff0eb', //折点颜色
lineStyle:{color:"#a5afb7"}}}}]},"yes"==a&&(option.dataZoom={show:!0,realtime:!0,start:o,end:100}),e.setOption(option,!0))}
//20. 计算对象 、 数组的长度
function pub_xDataLength(t){return(1==Array.isArray(t)?t:Object.keys(t)).length}
//21. 数组里包含对象的排序 like [{a:1,b:2}]
function pub_arrObjSort(t,e){var r;return t.sort((r=e,function(t,e){t=t[r],e=e[r];return isNaN(Number(t))||isNaN(Number(e))||(t=Number(t),e=Number(e)),t<e?-1:e<t?1:0}))}
//22 获取产品的分类名称
function productCategory(t,e="",r=0,n=""){if(null!=t){for(var a in e=""==e?GlobalCategory:e){a=e[a];a.value==t[r]&&(n+=a.label+" > ",null!=t[r+1])&&(n=productCategory(t,a.children,r+1,n))}return r==t.length-1?n.slice(0,-2):n}}async function productCategoryPath(t){return null!=t&&""!=t&&void 0!==(arrCateory=""==arrCateory?await getCategory():arrCateory)[t].path?productCategoryNew(arrCateory[t].path,arrCateory):""}async function productCategoryNew(t,e=""){
// var arrCateory = ''
var r="";if(null!=t&&""!=t){
// console.log(arrCateory)
for(var n in""==e&&(e=await getCategory()),t)void 0!==e[t[n]]&&(r=r?r+" > "+e[t[n]].value:e[t[n]].value);
// console.log(pathName)
return r;
// for(var i in arrCateory){
//     let element = arrCateory[i]
//     if(element.value == obj[index]){
//         pathName += element.label+' > '
//         if(obj[index+1] != undefined){
//             pathName = productCategory(obj,element.children,index+1,pathName)
//         }
//     }
// }
// if(index == (obj.length-1)){
//     return pathName.slice(0,-2)
// }
}}async function getCategory(){return new Promise((e,r)=>{chrome.runtime.sendMessage({type:"content_script",cmd:"request_get_category",objData:{}},function(t){t?e(t):r(null)})})}
//23. 画出词云图
function drawWords(t,e){var r,n=[];for(r in t)n.push([t[r].name,t[r].value]);new Js2WordCloud(document.getElementById(e)).setOption({tooltip:{show:!0},list:n,color:function(){return"rgb("+[Math.round(225*Math.random()),Math.round(225*Math.random()),Math.round(225*Math.random())].join(",")+")"}});
// var chart = echarts.init(document.getElementById(id));
// var option = {
//     tooltip: {},
//     series: [ {
//         type: 'wordCloud',
//         gridSize: 2,
//         sizeRange: [12, 50],
//         rotationRange: [-90, 90],
//         shape: 'pentagon',
//         width: 600,
//         height: 400,
//         textStyle: {
//             normal: {
//                 color: function () {
//                     return 'rgb(' + [
//                         Math.round(Math.random() * 160),
//                         Math.round(Math.random() * 160),
//                         Math.round(Math.random() * 160)
//                     ].join(',') + ')';
//                 }
//             },
//             emphasis: {
//                 shadowBlur: 10,
//                 shadowColor: '#333'
//             }
//         },
//         data: data
//     } ]
// };
// chart.setOption(option);
}function drawSmallBarNew(t,e,r){var n,e={tooltip:{trigger:"axis",axisPointer:{// 坐标轴指示器，坐标轴触发有效
type:"shadow"}},xAxis:{axisTick:{//y轴刻度线
show:!1},axisLine:{//y轴
show:!1},type:"category",data:e,axisLabel:{//决定是否显示数据
show:!1,formatter:function(t){var e=[],t=pub_numTransKm(t,0);return e.push(t),e}}},yAxis:{axisLine:{//y轴
show:!1},axisTick:{//y轴刻度线
show:!1},
// name:arrName[0],
// type: 'value',
splitLine:{show:!0,lineStyle:{type:"dashed"}},//去除网格线
axisLabel:{//决定是否显示数据
show:!1,formatter:function(t){var e=[],t=pub_numTransKm(t,0);return e.push(t),e}},max:function(t){return t.max==t.min?Math.ceil(Math.ceil((1.1*t.max).toFixed(1))):Math.ceil(t.max+.1*(t.max-t.min))},min:function(t){return t.max==t.min?Math.floor((.9*t.max).toFixed(1)):0==t.min?0:(t=t.min-.1*(t.max-t.min),Math.floor(t))}},grid:{left:"10px",right:"10px",bottom:"0px",top:"10px"},series:[{barwidth:100,data:r,type:"line"}]};
// 初始化echarts实例
//var myChart = echarts.init(document.getElementById(id))
// 使用制定的配置项和数据显示图表
(n="echart_product_sales"==t?echarts.init($("."+t+":last")[0]):echarts.init(document.getElementById(t))).setOption(e,!0);let a=$(".self-my-icon").parent().width();n.getDom().style.width=a+"px",n.resize(),window.addEventListener("resize",function(){a=$(".self-my-icon").parent().width(),n.getDom().style.width=a+"px",$(n.getDom()).parent().css("width",a+"px"),n.resize()})}
//画出混合折线图 和柱状图
function drawMixChart(t="line",e,r,n,a,i=!1){try{var o=1.1*Math.max(...n[0]),u=.8*Math.min(...n[0]),s=1.1*Math.max(...n[1]),l=.8*Math.min(...n[1]),o=parseInt(o),u=parseInt(u),
// max2 = parseInt(max2)
s=Math.ceil(s),l=Math.floor(l)}catch(t){console.log("error",t)}o={tooltip:{trigger:"axis"},legend:{data:a},
// grid: {
//     left: '10',
//     right: '4%',
//     bottom: '3%',
//     containLabel: true
// },
toolbox:{feature:{saveAsImage:{}}},xAxis:{type:"category",
// boundaryGap: false,
data:r},yAxis:[{type:"value",name:a[0],position:"left",max:o,min:u,axisLabel:{//决定是否显示数据
interval:"auto",show:!0,formatter:function(t){var e=[],t=pub_numTransKm(t,1);return e.push(t),e}}},{type:"value",name:a[1],position:"right",splitLine:{show:!1},max:s,min:l,axisLabel:{//决定是否显示数据
interval:"auto",show:!0,formatter:function(t){var e=[],t=pub_numTransKm(t,1);return e.push(t),e}}}],series:[{name:a[0],type:t,
//stack: 'Total',
yAxisIndex:0,data:n[0]},{name:a[1],type:t,
//stack: 'Total',
yAxisIndex:1,data:n[1]}]},u=r.length,s=0;30<u&&(s=parseInt((u-30)/u*100)),"yes"==i&&(o.dataZoom={show:!0,realtime:!0,start:s,end:100}),
//var myChart = echarts.init(document.getElementById(id))
// 使用制定的配置项和数据显示图表
(
// 初始化echarts实例
"echart_product_sales"==e?echarts.init($("."+e+":last")[0]):echarts.init(document.getElementById(e))).setOption(o,!0)}
//获取url 中 的请求参数
function getQueryVariable(t,e=""){for(var r=(e=""==e?window.location.search.substring(1):e).split("&"),n=0;n<r.length;n++){var a=r[n].split("=");if(a[0]==t)return a[1]}return!1}
//二维数组合并去重
function pub_arrayMerage(t){var e=[];return t.forEach(t=>{t.forEach(t=>{e.push(t)})}),e}
//比较去拿先
function pub_compareLevel(t,e){var t=(t+="").split(","),r=!1;return t.forEach(t=>{-1!==e.indexOf(t+"")&&(r=!0)}),r}arrCateory="";