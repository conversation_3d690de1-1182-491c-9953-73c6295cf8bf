define(["../var/document","../core"],function(e,n){"use strict";var t=["Webkit","Moz","ms"],i=e.createElement("div").style,c={};
// Return a vendor-prefixed property or undefined
// Return a potentially-mapped jQuery.cssProps or vendor prefixed property
return function(e){var r=n.cssProps[e]||c[e];return r||(e in i?e:c[e]=function(e){for(
// Check for vendor prefixed names
var r=e[0].toUpperCase()+e.slice(1),n=t.length;n--;)if((e=t[n]+r)in i)return e}(e)||e)}});