define(["../core"],function(t){"use strict";
// Cross-browser xml parsing
return t.parseXML=function(r){var e,n;if(!r||"string"!=typeof r)return null;
// Support: IE 9 - 11 only
// IE throws on parseFromString with invalid input.
try{e=(new window.DOMParser).parseFromString(r,"text/xml")}catch(r){}return n=e&&e.getElementsByTagName("parsererror")[0],e&&!n||t.error("Invalid XML: "+(n?t.map(n.childNodes,function(r){return r.textContent}).join("\n"):r)),e},t.parseXML});