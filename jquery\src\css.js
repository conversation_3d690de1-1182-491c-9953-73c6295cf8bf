define(["./core","./core/access","./core/camelCase","./core/nodeName","./var/rcssNum","./css/var/rnumnonpx","./css/var/cssExpand","./css/var/getStyles","./css/var/swap","./css/curCSS","./css/adjustCSS","./css/addGetHookIf","./css/support","./css/finalPropName","./core/init","./core/ready","./selector"],function(d,n,u,a,f,l,g,p,s,b,x,e,h,m){"use strict";var
// Swappable if display is none or starts with table
// except "table", "table-cell", or "table-caption"
// See here for display values: https://developer.mozilla.org/en-US/docs/CSS/display
r=/^(none|table(?!-c[ea]).+)/,y=/^--/,i={position:"absolute",visibility:"hidden",display:"block"},o={letterSpacing:"0",fontWeight:"400"};function c(e,t,n){
// Any relative (+/-) values have already been
// normalized at this point
var s=f.exec(t);return s?
// Guard against undefined "subtract", e.g., when used as in cssHooks
Math.max(0,s[2]-(n||0))+(s[3]||"px"):t}function v(e,t,n,s,r,i){var o="width"===t?1:0,c=0,a=0;
// Adjustment may not be necessary
if(n===(s?"border":"content"))return 0;for(;o<4;o+=2)
// Both box models exclude margin
"margin"===n&&(a+=d.css(e,n+g[o],!0,r)),
// If we get here with a content-box, we're seeking "padding" or "border" or "margin"
s?(
// For "content", subtract padding
"content"===n&&(a-=d.css(e,"padding"+g[o],!0,r)),
// For "content" or "padding", subtract border
"margin"!==n&&(a-=d.css(e,"border"+g[o]+"Width",!0,r))):(
// Add padding
a+=d.css(e,"padding"+g[o],!0,r),
// For "border" or "margin", add border
"padding"!==n?a+=d.css(e,"border"+g[o]+"Width",!0,r):c+=d.css(e,"border"+g[o]+"Width",!0,r));
// Account for positive content-box scroll gutter when requested by providing computedVal
return!s&&0<=i&&(
// offsetWidth/offsetHeight is a rounded sum of content, padding, scroll gutter, and border
// Assuming integer scroll gutter, subtract the rest and round down
a+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-i-a-c-.5))||0),a}function C(e,t,n){
// Start with computed style
var s=p(e),r=(!h.boxSizingReliable()||n)&&"border-box"===d.css(e,"boxSizing",!1,s),i=r,o=b(e,t,s),c="offset"+t[0].toUpperCase()+t.slice(1);
// Support: Firefox <=54
// Return a confounding non-pixel value or feign ignorance, as appropriate.
if(l.test(o)){if(!n)return o;o="auto"}
// Support: IE 9 - 11 only
// Use offsetWidth/offsetHeight for when box sizing is unreliable.
// In those cases, the computed value can be trusted to be border-box.
// Adjust for the element's box model
return(!h.boxSizingReliable()&&r||
// Support: IE 10 - 11+, Edge 15 - 18+
// IE/Edge misreport `getComputedStyle` of table rows with width/height
// set in CSS while `offset*` properties report correct values.
// Interestingly, in some cases IE 9 doesn't suffer from this issue.
!h.reliableTrDimensions()&&a(e,"tr")||
// Fall back to offsetWidth/offsetHeight when value is "auto"
// This happens for inline elements with no explicit setting (gh-3571)
"auto"===o||
// Support: Android <=4.1 - 4.3 only
// Also use offsetWidth/offsetHeight for misreported inline dimensions (gh-3602)
!parseFloat(o)&&"inline"===d.css(e,"display",!1,s))&&
// Make sure the element is visible & connected
e.getClientRects().length&&(r="border-box"===d.css(e,"boxSizing",!1,s),
// Where available, offsetWidth/offsetHeight approximate border box dimensions.
// Where not available (e.g., SVG), assume unreliable box-sizing and interpret the
// retrieved value as a content box dimension.
i=c in e)&&(o=e[c]),(
// Normalize "" and auto
o=parseFloat(o)||0)+v(e,t,n||(r?"border":"content"),i,s,
// Provide the current computed size to request scroll gutter calculation (gh-3589)
o)+"px"}return d.extend({
// Add in style property hooks for overriding the default
// behavior of getting and setting a style property
cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=b(e,"opacity"))?"1":t}}},
// Don't automatically add "px" to these possibly-unitless properties
cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},
// Add in properties whose names you wish to fix before
// setting or getting the value
cssProps:{},
// Get and set the style property on a DOM Node
style:function(e,t,n,s){
// Don't set styles on text and comment nodes
if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){
// Make sure that we're working with the right name
var r,i,o,c=u(t),a=y.test(t),l=e.style;
// Make sure that we're working with the right name. We don't
// want to query the value if it is a CSS custom property
// since they are user-defined.
// Check if we're setting a value
if(a||(t=m(c)),
// Gets hook for the prefixed version, then unprefixed version
o=d.cssHooks[t]||d.cssHooks[c],void 0===n)
// If a hook was provided get the non-computed value from there
return o&&"get"in o&&void 0!==(r=o.get(e,!1,s))?r:l[t];
// Otherwise just get the value from the style object
// Convert "+=" or "-=" to relative numbers (#7345)
"string"===(i=typeof n)&&(r=f.exec(n))&&r[1]&&(n=x(e,t,r),
// Fixes bug #9237
i="number"),
// Make sure that null and NaN values aren't set (#7116)
null==n||n!=n||(
// If a number was passed in, add the unit (except for certain CSS properties)
// The isCustomProp check can be removed in jQuery 4.0 when we only auto-append
// "px" to a few hardcoded values.
"number"!==i||a||(n+=r&&r[3]||(d.cssNumber[c]?"":"px")),
// background-* props affect original clone's values
h.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),o&&"set"in o&&void 0===(n=o.set(e,n,s)))||(a?l.setProperty(t,n):l[t]=n)}},css:function(e,t,n,s){var r,i=u(t);
// Make sure that we're working with the right name. We don't
// want to modify the value if it is a CSS custom property
// since they are user-defined.
// Make numeric if forced or a qualifier was provided and val looks numeric
return y.test(t)||(t=m(i)),
// Convert "normal" to computed value
"normal"===(
// Otherwise, if a way to get the computed value exists, use that
r=void 0===(
// If a hook was provided get the computed value from there
r=(
// Try prefixed name followed by the unprefixed name
i=d.cssHooks[t]||d.cssHooks[i])&&"get"in i?i.get(e,!0,n):r)?b(e,t,s):r)&&t in o&&(r=o[t]),(""===n||n)&&(i=parseFloat(r),!0===n||isFinite(i))?i||0:r}}),d.each(["height","width"],function(e,o){d.cssHooks[o]={get:function(e,t,n){if(t)
// Certain elements can have dimension info if we invisibly show them
// but it must have a current display style that would benefit
return!r.test(d.css(e,"display"))||
// Support: Safari 8+
// Table columns in Safari have non-zero offsetWidth & zero
// getBoundingClientRect().width unless display is changed.
// Support: IE <=11 only
// Running getBoundingClientRect on a disconnected node
// in IE throws an error.
e.getClientRects().length&&e.getBoundingClientRect().width?C(e,o,n):s(e,i,function(){return C(e,o,n)})},set:function(e,t,n){var s=p(e),
// Only read styles.position if the test has a chance to fail
// to avoid forcing a reflow.
r=!h.scrollboxSize()&&"absolute"===s.position,i=(r||n)&&"border-box"===d.css(e,"boxSizing",!1,s),n=n?v(e,o,n,i,s):0;
// Account for unreliable border-box dimensions by comparing offset* to computed and
// faking a content-box to get border and padding (gh-3699)
return i&&r&&(n-=Math.ceil(e["offset"+o[0].toUpperCase()+o.slice(1)]-parseFloat(s[o])-v(e,o,"border",!1,s)-.5)),
// Convert to pixels if value adjustment is needed
n&&(i=f.exec(t))&&"px"!==(i[3]||"px")&&(e.style[o]=t,t=d.css(e,o)),c(0,t,n)}}}),d.cssHooks.marginLeft=e(h.reliableMarginLeft,function(e,t){if(t)return(parseFloat(b(e,"marginLeft"))||e.getBoundingClientRect().left-s(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),
// These hooks are used by animate to expand properties
d.each({margin:"",padding:"",border:"Width"},function(r,i){d.cssHooks[r+i]={expand:function(e){for(var t=0,n={},
// Assumes a single number if not a string
s="string"==typeof e?e.split(" "):[e];t<4;t++)n[r+g[t]+i]=s[t]||s[t-2]||s[0];return n}},"margin"!==r&&(d.cssHooks[r+i].set=c)}),d.fn.extend({css:function(e,t){return n(this,function(e,t,n){var s,r,i={},o=0;if(Array.isArray(t)){for(s=p(e),r=t.length;o<r;o++)i[t[o]]=d.css(e,t[o],!1,s);return i}return void 0!==n?d.style(e,t,n):d.css(e,t)},e,t,1<arguments.length)}}),d});