<!--
 * @Author: Dcx
 * @Msg: 
 * @Date: 2021-01-04 10:16:43
 * @LastEditors: Dcx
 * @LastEditTime: 2021-01-20 15:27:44
 * @FilePath: \webTiktoke:\code\Chrome\chrome-plugin-demo\smt\html\popup.html
-->
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8"/>
    <title></title>
    
    <script type="text/javascript" src="../js/jquery.js"></script>
    <script type="text/javascript" src="../js/common_func.js"></script>
    <script type="text/javascript" src="../js/bootstrap.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../css/popup.css">
</head>
<body style="width:500px;min-height:100px;background-color:#393D49!important;color:white">
	<div>
        <!-- title -->
        <div class="text-center">
            <h3 style="margin-top:40px;">Aliexpress Analyzer<span style="font-size:12px;" class="version_hao">Version 1.3.0</span></h3>
        </div>
        <!--  -->
        <div class='describe-text'>
            <span class="lang_cow"> HOT! Explore over 200k+ dropshipping stores to find winning products </span>
        </div>

        <!-- tip -->
        <!-- <div class='tip-text'>
            This is not an AliExpress page. You need to open a product page to
            start downloading.
        </div> -->


        <div class="user-pic-div">
            <div id="already_login">
                <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                <span class="user-email"></span>
                
            </div>
            <div id="no_login">
                <a style="color:white;line-height:2" target="_blank" href="https://user.ixspy.com/userLogin?site=7&redirect_url=https://ixspy.com/login&to_redirect_url=https://ixspy.com/data&login_type=">
                    <span class="lang_login_more">
                        登陆/注册之后使用更多功能
                    </span>
                </a>
            </div>
        </div>

        <div style="width: 80%;margin: 0 auto;">
            <div class="inline site two-button"> <a style="color:white" href="https://ixspy.com" target="_blank"> IXSPY.COM </a></div>
            <div class="inline group two-button"> <a style="color:white" href="https://www.facebook.com/groups/winning.products" target="_blank"> <span class="lang_join_group">Join Facebook Group</span>  </a> </div>
        </div>

    <div>
        <!-- 收藏的商品 -->
        <hr style="width: 80%;"/>
        <div>
            <div class="my-favted-list">
                <span class="lang_my_favted">我的收藏列表</span>
                <span class="glyphicon glyphicon-heart"></span>
            </div>
            <ul id="ul_goods">
            </ul>
        </div>
    </div>
    </div>
</body>
<script type="text/javascript" src="../js/popup.js"></script>
<script>
// 在popup页面加载时模拟已登录状态
document.addEventListener('DOMContentLoaded', function() {
    // 隐藏未登录区域
    document.getElementById('no_login').style.display = 'none';
    
    // 显示已登录区域
    document.getElementById('already_login').style.display = 'block';
    
    // 设置用户邮箱
    document.querySelector('.user-email').textContent = '<EMAIL>';
});
</script>
</html>
