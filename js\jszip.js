/*!

JSZip v3.1.5 - A JavaScript class for generating and reading zip files
<http://stuartk.com/jszip>

(c) 2009-2016 <PERSON> <stuart [at] stuartk.com>
Dual licenced under the MIT license or GPLv3. See https://raw.github.com/Stuk/jszip/master/LICENSE.markdown.

JSZip uses the library pako released under the MIT license :
https://github.com/nodeca/pako/blob/master/LICENSE
*/
!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).JSZip=t()}(function(){return function n(i,s,a){function o(r,t){if(!s[r]){if(!i[r]){var e="function"==typeof require&&require;if(!t&&e)return e(r,!0);if(u)return u(r,!0);t=new Error("Cannot find module '"+r+"'");throw t.code="MODULE_NOT_FOUND",t}e=s[r]={exports:{}};i[r][0].call(e.exports,function(t){var e=i[r][1][t];return o(e||t)},e,e.exports,n,i,s,a)}return s[r].exports}for(var u="function"==typeof require&&require,t=0;t<a.length;t++)o(a[t]);return o}({1:[function(t,e,r){"use strict";var c=t("./utils"),f=t("./support"),d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(t){for(var e,r,n,i,s,a,o=[],u=0,h=t.length,l="string"!==c.getTypeOf(t);u<t.length;)a=h-u,n=l?(e=t[u++],r=u<h?t[u++]:0,u<h?t[u++]:0):(e=t.charCodeAt(u++),r=u<h?t.charCodeAt(u++):0,u<h?t.charCodeAt(u++):0),i=(3&e)<<4|r>>4,s=1<a?(15&r)<<2|n>>6:64,a=2<a?63&n:64,o.push(d.charAt(e>>2)+d.charAt(i)+d.charAt(s)+d.charAt(a));return o.join("")},r.decode=function(t){var e,r,n,i,s,a,o=0,u=0,h="data:";if(t.substr(0,h.length)===h)throw new Error("Invalid base64 input, it looks like a data url.");var l,h=3*(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"")).length/4;if(t.charAt(t.length-1)===d.charAt(64)&&h--,t.charAt(t.length-2)===d.charAt(64)&&h--,h%1!=0)throw new Error("Invalid base64 input, bad content length.");for(l=new(f.uint8array?Uint8Array:Array)(0|h);o<t.length;)n=d.indexOf(t.charAt(o++)),e=(15&(i=d.indexOf(t.charAt(o++))))<<4|(s=d.indexOf(t.charAt(o++)))>>2,r=(3&s)<<6|(a=d.indexOf(t.charAt(o++))),l[u++]=n<<2|i>>4,64!==s&&(l[u++]=e),64!==a&&(l[u++]=r);return l}},{"./support":30,"./utils":32}],2:[function(t,e,r){"use strict";function n(t,e,r,n,i){this.compressedSize=t,this.uncompressedSize=e,this.crc32=r,this.compression=n,this.compressedContent=i}var i=t("./external"),s=t("./stream/DataWorker"),a=t("./stream/DataLengthProbe"),o=t("./stream/Crc32Probe"),a=t("./stream/DataLengthProbe");n.prototype={getContentWorker:function(){var t=new s(i.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new a("data_length")),e=this;return t.on("end",function(){if(this.streamInfo.data_length!==e.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),t},getCompressedWorker:function(){return new s(i.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},n.createWorkerFrom=function(t,e,r){return t.pipe(new o).pipe(new a("uncompressedSize")).pipe(e.compressWorker(r)).pipe(new a("compressedSize")).withStreamInfo("compression",e)},e.exports=n},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(t,e,r){"use strict";var n=t("./stream/GenericWorker");r.STORE={magic:"\0\0",compressWorker:function(t){return new n("STORE compression")},uncompressWorker:function(){return new n("STORE decompression")}},r.DEFLATE=t("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(t,e,r){"use strict";var n=t("./utils"),o=function(){for(var t=[],e=0;e<256;e++){for(var r=e,n=0;n<8;n++)r=1&r?3988292384^r>>>1:r>>>1;t[e]=r}return t}();e.exports=function(t,e){return void 0!==t&&t.length?("string"!==n.getTypeOf(t)?function(t,e,r,n){var i=o,s=n+r;t^=-1;for(var a=n;a<s;a++)t=t>>>8^i[255&(t^e[a])];return-1^t}:function(t,e,r,n){var i=o,s=n+r;t^=-1;for(var a=n;a<s;a++)t=t>>>8^i[255&(t^e.charCodeAt(a))];return-1^t})(0|e,t,t.length,0):0}},{"./utils":32}],5:[function(t,e,r){"use strict";r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!0,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],6:[function(t,e,r){"use strict";t="undefined"!=typeof Promise?Promise:t("lie");e.exports={Promise:t}},{lie:58}],7:[function(t,e,r){"use strict";function n(t,e){o.call(this,"FlateWorker/"+t),this._pako=null,this._pakoAction=t,this._pakoOptions=e,this.meta={}}var i="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,s=t("pako"),a=t("./utils"),o=t("./stream/GenericWorker"),u=i?"uint8array":"array";r.magic="\b\0",a.inherits(n,o),n.prototype.processChunk=function(t){this.meta=t.meta,null===this._pako&&this._createPako(),this._pako.push(a.transformTo(u,t.data),!1)},n.prototype.flush=function(){o.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},n.prototype.cleanUp=function(){o.prototype.cleanUp.call(this),this._pako=null},n.prototype._createPako=function(){this._pako=new s[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},r.compressWorker=function(t){return new n("Deflate",t)},r.uncompressWorker=function(){return new n("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:59}],8:[function(t,e,r){"use strict";function n(t,e,r,n){s.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=e,this.zipPlatform=r,this.encodeFileName=n,this.streamFiles=t,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}function b(t,e){for(var r="",n=0;n<e;n++)r+=String.fromCharCode(255&t),t>>>=8;return r}function i(t,e,r,n,i,s){var a=t.file,o=t.compression,u=s!==v.utf8encode,h=y.transformTo("string",s(a.name)),l=y.transformTo("string",v.utf8encode(a.name)),c=a.comment,s=y.transformTo("string",s(c)),f=y.transformTo("string",v.utf8encode(c)),d=l.length!==a.name.length,c=f.length!==c.length,p="",m=a.dir,_=a.date,g={crc32:0,compressedSize:0,uncompressedSize:0},r=(e&&!r||(g.crc32=t.crc32,g.compressedSize=t.compressedSize,g.uncompressedSize=t.uncompressedSize),0),t=(e&&(r|=8),u||!d&&!c||(r|=2048),0),e=0,_=(m&&(t|=16),"UNIX"===i?(e=798,t|=(u=a.unixPermissions,i=m,(65535&(u?u:i?16893:33204))<<16)):(e=20,t|=63&(a.dosPermissions||0)),m=_.getUTCHours(),m=(m=((m<<=6)|_.getUTCMinutes())<<5)|_.getUTCSeconds()/2,u=_.getUTCFullYear()-1980,u=(u=((u<<=4)|_.getUTCMonth()+1)<<5)|_.getUTCDate(),d&&(i=b(1,1)+b(w(h),4)+l,p+="up"+b(i.length,2)+i),c&&(a=b(1,1)+b(w(s),4)+f,p+="uc"+b(a.length,2)+a),""),_=(_=(_=(_=(_=(_=(_=(_=(_=(_+="\n\0")+b(r,2))+o.magic)+b(m,2))+b(u,2))+b(g.crc32,4))+b(g.compressedSize,4))+b(g.uncompressedSize,4))+b(h.length,2))+b(p.length,2);return{fileRecord:k.LOCAL_FILE_HEADER+_+h+p,dirRecord:k.CENTRAL_FILE_HEADER+b(e,2)+_+b(s.length,2)+"\0\0\0\0"+b(t,4)+b(n,4)+h+p+s}}var y=t("../utils"),s=t("../stream/GenericWorker"),v=t("../utf8"),w=t("../crc32"),k=t("../signature");y.inherits(n,s),n.prototype.push=function(t){var e=t.meta.percent||0,r=this.entriesCount,n=this._sources.length;this.accumulate?this.contentBuffer.push(t):(this.bytesWritten+=t.data.length,s.prototype.push.call(this,{data:t.data,meta:{currentFile:this.currentFile,percent:r?(e+100*(r-n-1))/r:100}}))},n.prototype.openedSource=function(t){this.currentSourceOffset=this.bytesWritten,this.currentFile=t.file.name;var e=this.streamFiles&&!t.file.dir;e?(t=i(t,e,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName),this.push({data:t.fileRecord,meta:{percent:0}})):this.accumulate=!0},n.prototype.closedSource=function(t){this.accumulate=!1;var e=this.streamFiles&&!t.file.dir,r=i(t,e,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(r.dirRecord),e)this.push({data:(e=t,k.DATA_DESCRIPTOR+b(e.crc32,4)+b(e.compressedSize,4)+b(e.uncompressedSize,4)),meta:{percent:100}});else for(this.push({data:r.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},n.prototype.flush=function(){for(var t=this.bytesWritten,e=0;e<this.dirRecords.length;e++)this.push({data:this.dirRecords[e],meta:{percent:100}});var r=this.bytesWritten-t,r=function(t,e,r,n,i){i=y.transformTo("string",i(n));return k.CENTRAL_DIRECTORY_END+"\0\0\0\0"+b(t,2)+b(t,2)+b(e,4)+b(r,4)+b(i.length,2)+i}(this.dirRecords.length,r,t,this.zipComment,this.encodeFileName);this.push({data:r,meta:{percent:100}})},n.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},n.prototype.registerPrevious=function(t){this._sources.push(t);var e=this;return t.on("data",function(t){e.processChunk(t)}),t.on("end",function(){e.closedSource(e.previous.streamInfo),e._sources.length?e.prepareNextSource():e.end()}),t.on("error",function(t){e.error(t)}),this},n.prototype.resume=function(){return!!s.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},n.prototype.error=function(t){var e=this._sources;if(!s.prototype.error.call(this,t))return!1;for(var r=0;r<e.length;r++)try{e[r].error(t)}catch(t){}return!0},n.prototype.lock=function(){s.prototype.lock.call(this);for(var t=this._sources,e=0;e<t.length;e++)t[e].lock()},e.exports=n},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(t,e,r){"use strict";var h=t("../compressions"),n=t("./ZipFileWorker");r.generateWorker=function(t,a,e){var o=new n(a.streamFiles,e,a.platform,a.encodeFileName),u=0;try{t.forEach(function(t,e){u++;var r=function(t,e){t=t||e,e=h[t];if(e)return e;throw new Error(t+" is not a valid compression method !")}(e.options.compression,a.compression),n=e.options.compressionOptions||a.compressionOptions||{},i=e.dir,s=e.date;e._compressWorker(r,n).withStreamInfo("file",{name:t,dir:i,date:s,comment:e.comment||"",unixPermissions:e.unixPermissions,dosPermissions:e.dosPermissions}).pipe(o)}),o.entriesCount=u}catch(t){o.error(t)}return o}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(t,e,r){"use strict";function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files={},this.comment=null,this.root="",this.clone=function(){var t,e=new n;for(t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}(n.prototype=t("./object")).loadAsync=t("./load"),n.support=t("./support"),n.defaults=t("./defaults"),n.version="3.1.5",n.loadAsync=function(t,e){return(new n).loadAsync(t,e)},n.external=t("./external"),e.exports=n},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(t,e,r){"use strict";var n=t("./utils"),a=t("./external"),o=t("./utf8"),n=t("./utils"),u=t("./zipEntries"),h=t("./stream/Crc32Probe"),l=t("./nodejsUtils");e.exports=function(t,i){var s=this;return i=n.extend(i||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:o.utf8decode}),l.isNode&&l.isStream(t)?a.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):n.prepareContent("the loaded zip file",t,!0,i.optimizedBinaryString,i.base64).then(function(t){var e=new u(i);return e.load(t),e}).then(function(t){var e=[a.Promise.resolve(t)],r=t.files;if(i.checkCRC32)for(var n=0;n<r.length;n++)e.push(function(n){return new a.Promise(function(t,e){var r=n.decompressed.getContentWorker().pipe(new h);r.on("error",function(t){e(t)}).on("end",function(){r.streamInfo.crc32!==n.decompressed.crc32?e(new Error("Corrupted zip : CRC32 mismatch")):t()}).resume()})}(r[n]));return a.Promise.all(e)}).then(function(t){for(var t=t.shift(),e=t.files,r=0;r<e.length;r++){var n=e[r];s.file(n.fileNameStr,n.decompressed,{binary:!0,optimizedBinaryString:!0,date:n.date,dir:n.dir,comment:n.fileCommentStr.length?n.fileCommentStr:null,unixPermissions:n.unixPermissions,dosPermissions:n.dosPermissions,createFolders:i.createFolders})}return t.zipComment.length&&(s.comment=t.zipComment),s})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(t,e,r){"use strict";function n(t,e){s.call(this,"Nodejs stream input adapter for "+t),this._upstreamEnded=!1,this._bindStream(e)}var i=t("../utils"),s=t("../stream/GenericWorker");i.inherits(n,s),n.prototype._bindStream=function(t){var e=this;(this._stream=t).pause(),t.on("data",function(t){e.push({data:t,meta:{percent:0}})}).on("error",function(t){e.isPaused?this.generatedError=t:e.error(t)}).on("end",function(){e.isPaused?e._upstreamEnded=!0:e.end()})},n.prototype.pause=function(){return!!s.prototype.pause.call(this)&&(this._stream.pause(),!0)},n.prototype.resume=function(){return!!s.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},e.exports=n},{"../stream/GenericWorker":28,"../utils":32}],13:[function(t,e,r){"use strict";function n(t,e,r){i.call(this,e),this._helper=t;var n=this;t.on("data",function(t,e){n.push(t)||n._helper.pause(),r&&r(e)}).on("error",function(t){n.emit("error",t)}).on("end",function(){n.push(null)})}var i=t("readable-stream").Readable;t("../utils").inherits(n,i),n.prototype._read=function(){this._helper.resume()},e.exports=n},{"../utils":32,"readable-stream":16}],14:[function(t,e,r){"use strict";e.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(t,e){return new Buffer(t,e)},allocBuffer:function(t){return Buffer.alloc?Buffer.alloc(t):new Buffer(t)},isBuffer:function(t){return Buffer.isBuffer(t)},isStream:function(t){return t&&"function"==typeof t.on&&"function"==typeof t.pause&&"function"==typeof t.resume}}},{}],15:[function(t,e,r){"use strict";function s(t){return"[object RegExp]"===Object.prototype.toString.call(t)}function o(t,e){return e=void 0!==e?e:l.createFolders,t=g(t),this.files[t]||_.call(this,t,null,{dir:!0,createFolders:e}),this.files[t]}var i=t("./utf8"),u=t("./utils"),h=t("./stream/GenericWorker"),a=t("./stream/StreamHelper"),l=t("./defaults"),c=t("./compressedObject"),f=t("./zipObject"),d=t("./generate"),p=t("./nodejsUtils"),m=t("./nodejs/NodejsStreamInputAdapter"),_=function(t,e,r){var n=u.getTypeOf(e),i=u.extend(r||{},l),s=(i.date=i.date||new Date,null!==i.compression&&(i.compression=i.compression.toUpperCase()),"string"==typeof i.unixPermissions&&(i.unixPermissions=parseInt(i.unixPermissions,8)),i.unixPermissions&&16384&i.unixPermissions&&(i.dir=!0),i.dosPermissions&&16&i.dosPermissions&&(i.dir=!0),i.dir&&(t=g(t)),i.createFolders&&(a=0<(s=(a="/"===(a=t).slice(-1)?a.substring(0,a.length-1):a).lastIndexOf("/"))?a.substring(0,s):"")&&o.call(this,a,!0),"string"===n&&!1===i.binary&&!1===i.base64);r&&void 0!==r.binary||(i.binary=!s);(e instanceof c&&0===e.uncompressedSize||i.dir||!e||0===e.length)&&(i.base64=!1,i.binary=!0,e="",i.compression="STORE");var a=e instanceof c||e instanceof h?e:p.isNode&&p.isStream(e)?new m(t,e):u.prepareContent(t,e,i.binary,i.optimizedBinaryString,i.base64),n=new f(t,a,i);this.files[t]=n},g=function(t){return"/"!==t.slice(-1)&&(t+="/"),t};e.exports={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(t){var e,r,n;for(e in this.files)this.files.hasOwnProperty(e)&&(n=this.files[e],r=e.slice(this.root.length,e.length),r)&&e.slice(0,this.root.length)===this.root&&t(r,n)},filter:function(r){var n=[];return this.forEach(function(t,e){r(t,e)&&n.push(e)}),n},file:function(t,e,r){var n,i;return 1===arguments.length?s(t)?(n=t,this.filter(function(t,e){return!e.dir&&n.test(t)})):(i=this.files[this.root+t])&&!i.dir?i:null:(t=this.root+t,_.call(this,t,e,r),this)},folder:function(r){var t,e;return r?s(r)?this.filter(function(t,e){return e.dir&&r.test(t)}):(t=this.root+r,t=o.call(this,t),(e=this.clone()).root=t.name,e):this},remove:function(r){r=this.root+r;var t=this.files[r];if(t||("/"!==r.slice(-1)&&(r+="/"),t=this.files[r]),t&&!t.dir)delete this.files[r];else for(var e=this.filter(function(t,e){return e.name.slice(0,r.length)===r}),n=0;n<e.length;n++)delete this.files[e[n].name];return this},generate:function(t){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(t){var e={};try{if((e=u.extend(t||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:i.utf8encode})).type=e.type.toLowerCase(),e.compression=e.compression.toUpperCase(),"binarystring"===e.type&&(e.type="string"),!e.type)throw new Error("No output type specified.");u.checkSupport(e.type),"darwin"!==e.platform&&"freebsd"!==e.platform&&"linux"!==e.platform&&"sunos"!==e.platform||(e.platform="UNIX"),"win32"===e.platform&&(e.platform="DOS");var r=e.comment||this.comment||"",n=d.generateWorker(this,e,r)}catch(t){(n=new h("error")).error(t)}return new a(n,e.type||"string",e.mimeType)},generateAsync:function(t,e){return this.generateInternalStream(t).accumulate(e)},generateNodeStream:function(t,e){return(t=t||{}).type||(t.type="nodebuffer"),this.generateInternalStream(t).toNodejsStream(e)}}},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(t,e,r){e.exports=t("stream")},{stream:void 0}],17:[function(t,e,r){"use strict";function n(t){i.call(this,t);for(var e=0;e<this.data.length;e++)t[e]=255&t[e]}var i=t("./DataReader");t("../utils").inherits(n,i),n.prototype.byteAt=function(t){return this.data[this.zero+t]},n.prototype.lastIndexOfSignature=function(t){for(var e=t.charCodeAt(0),r=t.charCodeAt(1),n=t.charCodeAt(2),i=t.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===e&&this.data[s+1]===r&&this.data[s+2]===n&&this.data[s+3]===i)return s-this.zero;return-1},n.prototype.readAndCheckSignature=function(t){var e=t.charCodeAt(0),r=t.charCodeAt(1),n=t.charCodeAt(2),t=t.charCodeAt(3),i=this.readData(4);return e===i[0]&&r===i[1]&&n===i[2]&&t===i[3]},n.prototype.readData=function(t){var e;return this.checkOffset(t),0===t?[]:(e=this.data.slice(this.zero+this.index,this.zero+this.index+t),this.index+=t,e)},e.exports=n},{"../utils":32,"./DataReader":18}],18:[function(t,e,r){"use strict";function n(t){this.data=t,this.length=t.length,this.index=0,this.zero=0}var i=t("../utils");n.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(t){},readInt:function(t){var e,r=0;for(this.checkOffset(t),e=this.index+t-1;e>=this.index;e--)r=(r<<8)+this.byteAt(e);return this.index+=t,r},readString:function(t){return i.transformTo("string",this.readData(t))},readData:function(t){},lastIndexOfSignature:function(t){},readAndCheckSignature:function(t){},readDate:function(){var t=this.readInt(4);return new Date(Date.UTC(1980+(t>>25&127),(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(31&t)<<1))}},e.exports=n},{"../utils":32}],19:[function(t,e,r){"use strict";function n(t){i.call(this,t)}var i=t("./Uint8ArrayReader");t("../utils").inherits(n,i),n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(t,e,r){"use strict";function n(t){i.call(this,t)}var i=t("./DataReader");t("../utils").inherits(n,i),n.prototype.byteAt=function(t){return this.data.charCodeAt(this.zero+t)},n.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},n.prototype.readAndCheckSignature=function(t){return t===this.readData(4)},n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./DataReader":18}],21:[function(t,e,r){"use strict";function n(t){i.call(this,t)}var i=t("./ArrayReader");t("../utils").inherits(n,i),n.prototype.readData=function(t){var e;return this.checkOffset(t),0===t?new Uint8Array(0):(e=this.data.subarray(this.zero+this.index,this.zero+this.index+t),this.index+=t,e)},e.exports=n},{"../utils":32,"./ArrayReader":17}],22:[function(t,e,r){"use strict";var n=t("../utils"),i=t("../support"),s=t("./ArrayReader"),a=t("./StringReader"),o=t("./NodeBufferReader"),u=t("./Uint8ArrayReader");e.exports=function(t){var e=n.getTypeOf(t);return n.checkSupport(e),"string"!==e||i.uint8array?"nodebuffer"===e?new o(t):i.uint8array?new u(n.transformTo("uint8array",t)):new s(n.transformTo("array",t)):new a(t)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(t,e,r){"use strict";r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],24:[function(t,e,r){"use strict";function n(t){i.call(this,"ConvertWorker to "+t),this.destType=t}var i=t("./GenericWorker"),s=t("../utils");s.inherits(n,i),n.prototype.processChunk=function(t){this.push({data:s.transformTo(this.destType,t.data),meta:t.meta})},e.exports=n},{"../utils":32,"./GenericWorker":28}],25:[function(t,e,r){"use strict";function n(){i.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}var i=t("./GenericWorker"),s=t("../crc32");t("../utils").inherits(n,i),n.prototype.processChunk=function(t){this.streamInfo.crc32=s(t.data,this.streamInfo.crc32||0),this.push(t)},e.exports=n},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(t,e,r){"use strict";function n(t){s.call(this,"DataLengthProbe for "+t),this.propName=t,this.withStreamInfo(t,0)}var i=t("../utils"),s=t("./GenericWorker");i.inherits(n,s),n.prototype.processChunk=function(t){var e;t&&(e=this.streamInfo[this.propName]||0,this.streamInfo[this.propName]=e+t.data.length),s.prototype.processChunk.call(this,t)},e.exports=n},{"../utils":32,"./GenericWorker":28}],27:[function(t,e,r){"use strict";function n(t){s.call(this,"DataWorker");var e=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,t.then(function(t){e.dataIsReady=!0,e.data=t,e.max=t&&t.length||0,e.type=i.getTypeOf(t),e.isPaused||e._tickAndRepeat()},function(t){e.error(t)})}var i=t("../utils"),s=t("./GenericWorker");i.inherits(n,s),n.prototype.cleanUp=function(){s.prototype.cleanUp.call(this),this.data=null},n.prototype.resume=function(){return!!s.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,i.delay(this._tickAndRepeat,[],this)),!0)},n.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished)||(i.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0)},n.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var t=null,e=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":t=this.data.substring(this.index,e);break;case"uint8array":t=this.data.subarray(this.index,e);break;case"array":case"nodebuffer":t=this.data.slice(this.index,e)}return this.index=e,this.push({data:t,meta:{percent:this.max?this.index/this.max*100:0}})},e.exports=n},{"../utils":32,"./GenericWorker":28}],28:[function(t,e,r){"use strict";function n(t){this.name=t||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}n.prototype={push:function(t){this.emit("data",t)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(t){this.emit("error",t)}return!0},error:function(t){return!this.isFinished&&(this.isPaused?this.generatedError=t:(this.isFinished=!0,this.emit("error",t),this.previous&&this.previous.error(t),this.cleanUp()),!0)},on:function(t,e){return this._listeners[t].push(e),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(t,e){if(this._listeners[t])for(var r=0;r<this._listeners[t].length;r++)this._listeners[t][r].call(this,e)},pipe:function(t){return t.registerPrevious(this)},registerPrevious:function(t){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=t.streamInfo,this.mergeStreamInfo(),this.previous=t;var e=this;return t.on("data",function(t){e.processChunk(t)}),t.on("end",function(){e.end()}),t.on("error",function(t){e.error(t)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){var t;return!(!this.isPaused||this.isFinished||(t=this.isPaused=!1,this.generatedError&&(this.error(this.generatedError),t=!0),this.previous&&this.previous.resume(),t))},flush:function(){},processChunk:function(t){this.push(t)},withStreamInfo:function(t,e){return this.extraStreamInfo[t]=e,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var t in this.extraStreamInfo)this.extraStreamInfo.hasOwnProperty(t)&&(this.streamInfo[t]=this.extraStreamInfo[t])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var t="Worker "+this.name;return this.previous?this.previous+" -> "+t:t}},e.exports=n},{}],29:[function(t,e,r){"use strict";function n(t,o){return new l.Promise(function(e,r){var n=[],i=t._internalType,s=t._outputType,a=t._mimeType;t.on("data",function(t,e){n.push(t),o&&o(e)}).on("error",function(t){n=[],r(t)}).on("end",function(){try{var t=function(t,e,r){switch(t){case"blob":return u.newBlob(u.transformTo("arraybuffer",e),r);case"base64":return h.encode(e);default:return u.transformTo(t,e)}}(s,function(t,e){for(var r=0,n=null,i=0,s=0;s<e.length;s++)i+=e[s].length;switch(t){case"string":return e.join("");case"array":return Array.prototype.concat.apply([],e);case"uint8array":for(n=new Uint8Array(i),s=0;s<e.length;s++)n.set(e[s],r),r+=e[s].length;return n;case"nodebuffer":return Buffer.concat(e);default:throw new Error("concat : unsupported type '"+t+"'")}}(i,n),a);e(t)}catch(t){r(t)}n=[]}).resume()})}function i(t,e,r){var n=e;switch(e){case"blob":case"arraybuffer":n="uint8array";break;case"base64":n="string"}try{this._internalType=n,this._outputType=e,this._mimeType=r,u.checkSupport(n),this._worker=t.pipe(new s(n)),t.lock()}catch(t){this._worker=new a("error"),this._worker.error(t)}}var u=t("../utils"),s=t("./ConvertWorker"),a=t("./GenericWorker"),h=t("../base64"),o=t("../support"),l=t("../external"),c=null;if(o.nodestream)try{c=t("../nodejs/NodejsStreamOutputAdapter")}catch(t){}i.prototype={accumulate:function(t){return n(this,t)},on:function(t,e){var r=this;return"data"===t?this._worker.on(t,function(t){e.call(r,t.data,t.meta)}):this._worker.on(t,function(){u.delay(e,arguments,r)}),this},resume:function(){return u.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(t){if(u.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new c(this,{objectMode:"nodebuffer"!==this._outputType},t)}},e.exports=i},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(t,e,r){"use strict";if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,r.nodebuffer="undefined"!=typeof Buffer,r.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)r.blob=!1;else{var n=new ArrayBuffer(0);try{r.blob=0===new Blob([n],{type:"application/zip"}).size}catch(t){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(n),r.blob=0===i.getBlob("application/zip").size}catch(t){r.blob=!1}}}try{r.nodestream=!!t("readable-stream").Readable}catch(t){r.nodestream=!1}},{"readable-stream":16}],31:[function(t,e,i){"use strict";function r(){s.call(this,"utf-8 decode"),this.leftOver=null}function n(){s.call(this,"utf-8 encode")}for(var u=t("./utils"),h=t("./support"),l=t("./nodejsUtils"),s=t("./stream/GenericWorker"),c=new Array(256),a=0;a<256;a++)c[a]=252<=a?6:248<=a?5:240<=a?4:224<=a?3:192<=a?2:1;c[254]=c[254]=1;i.utf8encode=function(t){if(h.nodebuffer)return l.newBufferFrom(t,"utf-8");for(var e,r,n,i,s=t,a=s.length,o=0,u=0;u<a;u++)55296==(64512&(r=s.charCodeAt(u)))&&u+1<a&&56320==(64512&(n=s.charCodeAt(u+1)))&&(r=65536+(r-55296<<10)+(n-56320),u++),o+=r<128?1:r<2048?2:r<65536?3:4;for(e=new(h.uint8array?Uint8Array:Array)(o),u=i=0;i<o;u++)55296==(64512&(r=s.charCodeAt(u)))&&u+1<a&&56320==(64512&(n=s.charCodeAt(u+1)))&&(r=65536+(r-55296<<10)+(n-56320),u++),r<128?e[i++]=r:(r<2048?e[i++]=192|r>>>6:(r<65536?e[i++]=224|r>>>12:(e[i++]=240|r>>>18,e[i++]=128|r>>>12&63),e[i++]=128|r>>>6&63),e[i++]=128|63&r);return e},i.utf8decode=function(t){if(h.nodebuffer)return u.transformTo("nodebuffer",t).toString("utf-8");for(var e,r,n=t=u.transformTo(h.uint8array?"uint8array":"array",t),i=n.length,s=new Array(2*i),a=0,o=0;o<i;)if((e=n[o++])<128)s[a++]=e;else if(4<(r=c[e]))s[a++]=65533,o+=r-1;else{for(e&=2===r?31:3===r?15:7;1<r&&o<i;)e=e<<6|63&n[o++],r--;1<r?s[a++]=65533:e<65536?s[a++]=e:(e-=65536,s[a++]=55296|e>>10&1023,s[a++]=56320|1023&e)}return s.length!==a&&(s.subarray?s=s.subarray(0,a):s.length=a),u.applyFromCharCode(s)},u.inherits(r,s),r.prototype.processChunk=function(t){var e=u.transformTo(h.uint8array?"uint8array":"array",t.data),r=(this.leftOver&&this.leftOver.length&&(h.uint8array?(r=e,(e=new Uint8Array(r.length+this.leftOver.length)).set(this.leftOver,0),e.set(r,this.leftOver.length)):e=this.leftOver.concat(e),this.leftOver=null),function(t,e){for(var r=(e=(e=e||t.length)>t.length?t.length:e)-1;0<=r&&128==(192&t[r]);)r--;return!(r<0)&&0!==r&&r+c[t[r]]>e?r:e}(e)),n=e;r!==e.length&&(h.uint8array?(n=e.subarray(0,r),this.leftOver=e.subarray(r,e.length)):(n=e.slice(0,r),this.leftOver=e.slice(r,e.length))),this.push({data:i.utf8decode(n),meta:t.meta})},r.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:i.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},i.Utf8DecodeWorker=r,u.inherits(n,s),n.prototype.processChunk=function(t){this.push({data:i.utf8encode(t.data),meta:t.meta})},i.Utf8EncodeWorker=n},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(t,e,a){"use strict";function r(t){return t}function o(t,e){for(var r=0;r<t.length;++r)e[r]=255&t.charCodeAt(r);return e}function n(t){var e=65536,r=a.getTypeOf(t),n=!0;if("uint8array"===r?n=f.applyCanBeUsed.uint8array:"nodebuffer"===r&&(n=f.applyCanBeUsed.nodebuffer),n)for(;1<e;)try{return f.stringifyByChunk(t,r,e)}catch(t){e=Math.floor(e/2)}return f.stringifyByChar(t)}function i(t,e){for(var r=0;r<t.length;r++)e[r]=t[r];return e}var u=t("./support"),h=t("./base64"),s=t("./nodejsUtils"),l=t("core-js/library/fn/set-immediate"),c=t("./external"),f=(a.newBlob=function(e,r){a.checkSupport("blob");try{return new Blob([e],{type:r})}catch(t){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(e),n.getBlob(r)}catch(t){throw new Error("Bug : can't construct the Blob.")}}},{stringifyByChunk:function(t,e,r){var n=[],i=0,s=t.length;if(s<=r)return String.fromCharCode.apply(null,t);for(;i<s;)n.push("array"===e||"nodebuffer"===e?String.fromCharCode.apply(null,t.slice(i,Math.min(i+r,s))):String.fromCharCode.apply(null,t.subarray(i,Math.min(i+r,s)))),i+=r;return n.join("")},stringifyByChar:function(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},applyCanBeUsed:{uint8array:function(){try{return u.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(t){return!1}}(),nodebuffer:function(){try{return u.nodebuffer&&1===String.fromCharCode.apply(null,s.allocBuffer(1)).length}catch(t){return!1}}()}}),d=(a.applyFromCharCode=n,{});d.string={string:r,array:function(t){return o(t,new Array(t.length))},arraybuffer:function(t){return d.string.uint8array(t).buffer},uint8array:function(t){return o(t,new Uint8Array(t.length))},nodebuffer:function(t){return o(t,s.allocBuffer(t.length))}},d.array={string:n,array:r,arraybuffer:function(t){return new Uint8Array(t).buffer},uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s.newBufferFrom(t)}},d.arraybuffer={string:function(t){return n(new Uint8Array(t))},array:function(t){return i(new Uint8Array(t),new Array(t.byteLength))},arraybuffer:r,uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s.newBufferFrom(new Uint8Array(t))}},d.uint8array={string:n,array:function(t){return i(t,new Array(t.length))},arraybuffer:function(t){return t.buffer},uint8array:r,nodebuffer:function(t){return s.newBufferFrom(t)}},d.nodebuffer={string:n,array:function(t){return i(t,new Array(t.length))},arraybuffer:function(t){return d.nodebuffer.uint8array(t).buffer},uint8array:function(t){return i(t,new Uint8Array(t.length))},nodebuffer:r},a.transformTo=function(t,e){if(e=e||"",!t)return e;a.checkSupport(t);var r=a.getTypeOf(e);return d[r][t](e)},a.getTypeOf=function(t){return"string"==typeof t?"string":"[object Array]"===Object.prototype.toString.call(t)?"array":u.nodebuffer&&s.isBuffer(t)?"nodebuffer":u.uint8array&&t instanceof Uint8Array?"uint8array":u.arraybuffer&&t instanceof ArrayBuffer?"arraybuffer":void 0},a.checkSupport=function(t){if(!u[t.toLowerCase()])throw new Error(t+" is not supported by this platform")},a.MAX_VALUE_16BITS=65535,a.MAX_VALUE_32BITS=-1,a.pretty=function(t){for(var e,r="",n=0;n<(t||"").length;n++)r+="\\x"+((e=t.charCodeAt(n))<16?"0":"")+e.toString(16).toUpperCase();return r},a.delay=function(t,e,r){l(function(){t.apply(r||null,e||[])})},a.inherits=function(t,e){function r(){}r.prototype=e.prototype,t.prototype=new r},a.extend=function(){for(var t,e={},r=0;r<arguments.length;r++)for(t in arguments[r])arguments[r].hasOwnProperty(t)&&void 0===e[t]&&(e[t]=arguments[r][t]);return e},a.prepareContent=function(r,t,n,i,s){return c.Promise.resolve(t).then(function(n){return u.blob&&(n instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(n)))&&"undefined"!=typeof FileReader?new c.Promise(function(e,r){var t=new FileReader;t.onload=function(t){e(t.target.result)},t.onerror=function(t){r(t.target.error)},t.readAsArrayBuffer(n)}):n}).then(function(t){var e=a.getTypeOf(t);return e?("arraybuffer"===e?t=a.transformTo("uint8array",t):"string"===e&&(s?t=h.decode(t):n&&!0!==i&&(t=o(e=t,new(u.uint8array?Uint8Array:Array)(e.length)))),t):c.Promise.reject(new Error("Can't read the data of '"+r+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,"core-js/library/fn/set-immediate":36}],33:[function(t,e,r){"use strict";function n(t){this.files=[],this.loadOptions=t}var i=t("./reader/readerFor"),s=t("./utils"),a=t("./signature"),o=t("./zipEntry"),u=(t("./utf8"),t("./support"));n.prototype={checkSignature:function(t){var e;if(!this.reader.readAndCheckSignature(t))throw this.reader.index-=4,e=this.reader.readString(4),new Error("Corrupted zip or bug: unexpected signature ("+s.pretty(e)+", expected "+s.pretty(t)+")")},isSignature:function(t,e){var r=this.reader.index;this.reader.setIndex(t);t=this.reader.readString(4)===e;return this.reader.setIndex(r),t},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),e=u.uint8array?"uint8array":"array",e=s.transformTo(e,t);this.zipComment=this.loadOptions.decodeFileName(e)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t,e,r,n=this.zip64EndOfCentralSize-44;0<n;)t=this.reader.readInt(2),e=this.reader.readInt(4),r=this.reader.readData(e),this.zip64ExtensibleData[t]={id:t,length:e,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){for(var t,e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(a.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(a.CENTRAL_FILE_HEADER);)(t=new o({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(a.CENTRAL_DIRECTORY_END);if(t<0)throw!this.isSignature(0,a.LOCAL_FILE_HEADER)?new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html"):new Error("Corrupted zip: can't find end of central directory");this.reader.setIndex(t);var e=t;if(this.checkSignature(a.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===s.MAX_VALUE_16BITS||this.diskWithCentralDirStart===s.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===s.MAX_VALUE_16BITS||this.centralDirRecords===s.MAX_VALUE_16BITS||this.centralDirSize===s.MAX_VALUE_32BITS||this.centralDirOffset===s.MAX_VALUE_32BITS){if(this.zip64=!0,(t=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,a.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}t=this.centralDirOffset+this.centralDirSize,t=e-(t=this.zip64?t+20+(12+this.zip64EndOfCentralSize):t);if(0<t)this.isSignature(e,a.CENTRAL_FILE_HEADER)||(this.reader.zero=t);else if(t<0)throw new Error("Corrupted zip: missing "+Math.abs(t)+" bytes.")},prepareReader:function(t){this.reader=i(t)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=n},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utf8":31,"./utils":32,"./zipEntry":34}],34:[function(t,e,r){"use strict";function n(t,e){this.options=t,this.loadOptions=e}var i=t("./reader/readerFor"),s=t("./utils"),a=t("./compressedObject"),o=t("./crc32"),u=t("./utf8"),h=t("./compressions"),l=t("./support");n.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(t){var e;if(t.skip(22),this.fileNameLength=t.readInt(2),e=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(e),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough informations from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(e=function(t){for(var e in h)if(h.hasOwnProperty(e)&&h[e].magic===t)return h[e];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+s.pretty(this.compressionMethod)+" unknown (inner file : "+s.transformTo("string",this.fileName)+")");this.decompressed=new a(this.compressedSize,this.uncompressedSize,this.crc32,e,t.readData(this.compressedSize))},readCentralPart:function(t){this.versionMadeBy=t.readInt(2),t.skip(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4);var e=t.readInt(2);if(this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");t.skip(e),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==t&&(this.dosPermissions=63&this.externalFileAttributes),3==t&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(t){var e;this.extraFields[1]&&(e=i(this.extraFields[1].value),this.uncompressedSize===s.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===s.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===s.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===s.MAX_VALUE_32BITS)&&(this.diskNumberStart=e.readInt(4))},readExtraFields:function(t){var e,r,n,i=t.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});t.index<i;)e=t.readInt(2),r=t.readInt(2),n=t.readData(r),this.extraFields[e]={id:e,length:r,value:n}},handleUTF8:function(){var t,e=l.uint8array?"uint8array":"array";this.useUTF8()?(this.fileNameStr=u.utf8decode(this.fileName),this.fileCommentStr=u.utf8decode(this.fileComment)):(null!==(t=this.findExtraFieldUnicodePath())?this.fileNameStr=t:(t=s.transformTo(e,this.fileName),this.fileNameStr=this.loadOptions.decodeFileName(t)),null!==(t=this.findExtraFieldUnicodeComment())?this.fileCommentStr=t:(t=s.transformTo(e,this.fileComment),this.fileCommentStr=this.loadOptions.decodeFileName(t)))},findExtraFieldUnicodePath:function(){var t,e=this.extraFields[28789];return!e||1!==(t=i(e.value)).readInt(1)||o(this.fileName)!==t.readInt(4)?null:u.utf8decode(t.readData(e.length-5))},findExtraFieldUnicodeComment:function(){var t,e=this.extraFields[25461];return!e||1!==(t=i(e.value)).readInt(1)||o(this.fileComment)!==t.readInt(4)?null:u.utf8decode(t.readData(e.length-5))}},e.exports=n},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(t,e,r){"use strict";function n(t,e,r){this.name=t,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=e,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}}var s=t("./stream/StreamHelper"),i=t("./stream/DataWorker"),a=t("./utf8"),o=t("./compressedObject"),u=t("./stream/GenericWorker");n.prototype={internalStream:function(t){var e=null,r="string";try{if(!t)throw new Error("No output type specified.");var n="string"===(r=t.toLowerCase())||"text"===r,i=("binarystring"!==r&&"text"!==r||(r="string"),e=this._decompressWorker(),!this._dataBinary);i&&!n&&(e=e.pipe(new a.Utf8EncodeWorker)),!i&&n&&(e=e.pipe(new a.Utf8DecodeWorker))}catch(t){(e=new u("error")).error(t)}return new s(e,r,"")},async:function(t,e){return this.internalStream(t).accumulate(e)},nodeStream:function(t,e){return this.internalStream(t||"nodebuffer").toNodejsStream(e)},_compressWorker:function(t,e){var r;return this._data instanceof o&&this._data.compression.magic===t.magic?this._data.getCompressedWorker():(r=this._decompressWorker(),this._dataBinary||(r=r.pipe(new a.Utf8EncodeWorker)),o.createWorkerFrom(r,t,e))},_decompressWorker:function(){return this._data instanceof o?this._data.getContentWorker():this._data instanceof u?this._data:new i(this._data)}};for(var h=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],l=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},c=0;c<h.length;c++)n.prototype[h[c]]=l;e.exports=n},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(t,e,r){t("../modules/web.immediate"),e.exports=t("../modules/_core").setImmediate},{"../modules/_core":40,"../modules/web.immediate":56}],37:[function(t,e,r){e.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},{}],38:[function(t,e,r){var n=t("./_is-object");e.exports=function(t){if(n(t))return t;throw TypeError(t+" is not an object!")}},{"./_is-object":51}],39:[function(t,e,r){var n={}.toString;e.exports=function(t){return n.call(t).slice(8,-1)}},{}],40:[function(t,e,r){e=e.exports={version:"2.3.0"};"number"==typeof __e&&(__e=e)},{}],41:[function(t,e,r){var s=t("./_a-function");e.exports=function(n,i,t){if(s(n),void 0===i)return n;switch(t){case 1:return function(t){return n.call(i,t)};case 2:return function(t,e){return n.call(i,t,e)};case 3:return function(t,e,r){return n.call(i,t,e,r)}}return function(){return n.apply(i,arguments)}}},{"./_a-function":37}],42:[function(t,e,r){e.exports=!t("./_fails")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},{"./_fails":45}],43:[function(t,e,r){var n=t("./_is-object"),i=t("./_global").document,s=n(i)&&n(i.createElement);e.exports=function(t){return s?i.createElement(t):{}}},{"./_global":46,"./_is-object":51}],44:[function(t,e,r){function m(t,e,r){var n,i,s,a=t&m.F,o=t&m.G,u=t&m.S,h=t&m.P,l=t&m.B,c=t&m.W,f=o?g:g[e]||(g[e]={}),d=f[v],p=o?_:u?_[e]:(_[e]||{})[v];for(n in r=o?e:r)i=!a&&p&&void 0!==p[n],i&&n in f||(s=(i?p:r)[n],f[n]=o&&"function"!=typeof p[n]?r[n]:l&&i?b(s,_):c&&p[n]==s?function(n){function t(t,e,r){if(this instanceof n){switch(arguments.length){case 0:return new n;case 1:return new n(t);case 2:return new n(t,e)}return new n(t,e,r)}return n.apply(this,arguments)}return t[v]=n[v],t}(s):h&&"function"==typeof s?b(Function.call,s):s,h&&((f.virtual||(f.virtual={}))[n]=s,t&m.R)&&d&&!d[n]&&y(d,n,s))}var _=t("./_global"),g=t("./_core"),b=t("./_ctx"),y=t("./_hide"),v="prototype";m.F=1,m.G=2,m.S=4,m.P=8,m.B=16,m.W=32,m.U=64,m.R=128,e.exports=m},{"./_core":40,"./_ctx":41,"./_global":46,"./_hide":47}],45:[function(t,e,r){e.exports=function(t){try{return!!t()}catch(t){return!0}}},{}],46:[function(t,e,r){e=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)},{}],47:[function(t,e,r){var n=t("./_object-dp"),i=t("./_property-desc");e.exports=t("./_descriptors")?function(t,e,r){return n.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},{"./_descriptors":42,"./_object-dp":52,"./_property-desc":53}],48:[function(t,e,r){e.exports=t("./_global").document&&document.documentElement},{"./_global":46}],49:[function(t,e,r){e.exports=!t("./_descriptors")&&!t("./_fails")(function(){return 7!=Object.defineProperty(t("./_dom-create")("div"),"a",{get:function(){return 7}}).a})},{"./_descriptors":42,"./_dom-create":43,"./_fails":45}],50:[function(t,e,r){e.exports=function(t,e,r){var n=void 0===r;switch(e.length){case 0:return n?t():t.call(r);case 1:return n?t(e[0]):t.call(r,e[0]);case 2:return n?t(e[0],e[1]):t.call(r,e[0],e[1]);case 3:return n?t(e[0],e[1],e[2]):t.call(r,e[0],e[1],e[2]);case 4:return n?t(e[0],e[1],e[2],e[3]):t.call(r,e[0],e[1],e[2],e[3])}return t.apply(r,e)}},{}],51:[function(t,e,r){e.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},{}],52:[function(t,e,r){var n=t("./_an-object"),i=t("./_ie8-dom-define"),s=t("./_to-primitive"),a=Object.defineProperty;r.f=t("./_descriptors")?Object.defineProperty:function(t,e,r){if(n(t),e=s(e,!0),n(r),i)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(t[e]=r.value),t}},{"./_an-object":38,"./_descriptors":42,"./_ie8-dom-define":49,"./_to-primitive":55}],53:[function(t,e,r){e.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},{}],54:[function(t,e,r){function n(){var t,e=+this;_.hasOwnProperty(e)&&(t=_[e],delete _[e],t())}function i(t){n.call(t.data)}var s,a=t("./_ctx"),o=t("./_invoke"),u=t("./_html"),h=t("./_dom-create"),l=t("./_global"),c=l.process,f=l.setImmediate,d=l.clearImmediate,p=l.MessageChannel,m=0,_={},g="onreadystatechange";f&&d||(f=function(t){for(var e=[],r=1;r<arguments.length;)e.push(arguments[r++]);return _[++m]=function(){o("function"==typeof t?t:Function(t),e)},s(m),m},d=function(t){delete _[t]},"process"==t("./_cof")(c)?s=function(t){c.nextTick(a(n,t,1))}:p?(p=(t=new p).port2,t.port1.onmessage=i,s=a(p.postMessage,p,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(s=function(t){l.postMessage(t+"","*")},l.addEventListener("message",i,!1)):s=g in h("script")?function(t){u.appendChild(h("script"))[g]=function(){u.removeChild(this),n.call(t)}}:function(t){setTimeout(a(n,t,1),0)}),e.exports={set:f,clear:d}},{"./_cof":39,"./_ctx":41,"./_dom-create":43,"./_global":46,"./_html":48,"./_invoke":50}],55:[function(t,e,r){var i=t("./_is-object");e.exports=function(t,e){if(!i(t))return t;var r,n;if(e&&"function"==typeof(r=t.toString)&&!i(n=r.call(t))||"function"==typeof(r=t.valueOf)&&!i(n=r.call(t))||!e&&"function"==typeof(r=t.toString)&&!i(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")}},{"./_is-object":51}],56:[function(t,e,r){var n=t("./_export"),t=t("./_task");n(n.G+n.B,{setImmediate:t.set,clearImmediate:t.clear})},{"./_export":44,"./_task":54}],57:[function(t,h,e){!function(e){"use strict";function r(){s=!0;for(var t,e,r=u.length;r;){for(e=u,u=[],t=-1;++t<r;)e[t]();r=u.length}s=!1}var t,n,i,s,a=e.MutationObserver||e.WebKitMutationObserver,o=a?(t=0,a=new a(r),n=e.document.createTextNode(""),a.observe(n,{characterData:!0}),function(){n.data=t=++t%2}):e.setImmediate||void 0===e.MessageChannel?"document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){r(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(r,0)}:((i=new e.MessageChannel).port1.onmessage=r,function(){i.port2.postMessage(0)}),u=[];h.exports=function(t){1!==u.push(t)||s||o()}}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],58:[function(t,e,r){"use strict";function h(){}function n(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=p,this.queue=[],this.outcome=void 0,t!==h&&o(this,t)}function i(t,e,r){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function s(e,r,n){l(function(){var t;try{t=r(n)}catch(t){return c.reject(e,t)}t===e?c.reject(e,new TypeError("Cannot resolve promise with itself")):c.resolve(e,t)})}function a(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function o(e,t){function r(t){i||(i=!0,c.reject(e,t))}function n(t){i||(i=!0,c.resolve(e,t))}var i=!1,s=u(function(){t(n,r)});"error"===s.status&&r(s.value)}function u(t,e){var r={};try{r.value=t(e),r.status="success"}catch(t){r.status="error",r.value=t}return r}var l=t("immediate"),c={},f=["REJECTED"],d=["FULFILLED"],p=["PENDING"];(e.exports=n).prototype.catch=function(t){return this.then(null,t)},n.prototype.then=function(t,e){var r;return"function"!=typeof t&&this.state===d||"function"!=typeof e&&this.state===f?this:(r=new this.constructor(h),this.state!==p?s(r,this.state===d?t:e,this.outcome):this.queue.push(new i(r,t,e)),r)},i.prototype.callFulfilled=function(t){c.resolve(this.promise,t)},i.prototype.otherCallFulfilled=function(t){s(this.promise,this.onFulfilled,t)},i.prototype.callRejected=function(t){c.reject(this.promise,t)},i.prototype.otherCallRejected=function(t){s(this.promise,this.onRejected,t)},c.resolve=function(t,e){var r=u(a,e);if("error"===r.status)return c.reject(t,r.value);r=r.value;if(r)o(t,r);else{t.state=d,t.outcome=e;for(var n=-1,i=t.queue.length;++n<i;)t.queue[n].callFulfilled(e)}return t},c.reject=function(t,e){t.state=f,t.outcome=e;for(var r=-1,n=t.queue.length;++r<n;)t.queue[r].callRejected(e);return t},n.resolve=function(t){return t instanceof this?t:c.resolve(new this(h),t)},n.reject=function(t){var e=new this(h);return c.reject(e,t)},n.all=function(t){function e(t,e){r.resolve(t).then(function(t){s[e]=t,++a!==n||i||(i=!0,c.resolve(u,s))},function(t){i||(i=!0,c.reject(u,t))})}var r=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var n=t.length,i=!1;if(!n)return this.resolve([]);for(var s=new Array(n),a=0,o=-1,u=new this(h);++o<n;)e(t[o],o);return u},n.race=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var r=t.length,n=!1;if(!r)return this.resolve([]);for(var i,s=-1,a=new this(h);++s<r;)i=t[s],e.resolve(i).then(function(t){n||(n=!0,c.resolve(a,t))},function(t){n||(n=!0,c.reject(a,t))});return a}},{immediate:57}],59:[function(t,e,r){"use strict";var n={};(0,t("./lib/utils/common").assign)(n,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),e.exports=n},{"./lib/deflate":60,"./lib/inflate":61,"./lib/utils/common":62,"./lib/zlib/constants":65}],60:[function(t,e,r){"use strict";function n(t){if(!(this instanceof n))return new n(t);this.options=o.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});t=this.options;if(t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new h,(this.strm.avail_out=0)!==(e=a.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy)))throw new Error(s[e]);if(t.header&&a.deflateSetHeader(this.strm,t.header),t.dictionary){var e,t="string"==typeof t.dictionary?u.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary;if(0!==(e=a.deflateSetDictionary(this.strm,t)))throw new Error(s[e]);this._dict_set=!0}}function i(t,e){e=new n(e);if(e.push(t,!0),e.err)throw e.msg||s[e.err];return e.result}var a=t("./zlib/deflate"),o=t("./utils/common"),u=t("./utils/strings"),s=t("./zlib/messages"),h=t("./zlib/zstream"),l=Object.prototype.toString;n.prototype.push=function(t,e){var r,n,i=this.strm,s=this.options.chunkSize;if(this.ended)return!1;n=e===~~e?e:!0===e?4:0,"string"==typeof t?i.input=u.string2buf(t):"[object ArrayBuffer]"===l.call(t)?i.input=new Uint8Array(t):i.input=t,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new o.Buf8(s),i.next_out=0,i.avail_out=s),1!==(r=a.deflate(i,n))&&0!==r)return this.onEnd(r),!(this.ended=!0)}while(0!==i.avail_out&&(0!==i.avail_in||4!==n&&2!==n)||("string"===this.options.to?this.onData(u.buf2binstring(o.shrinkBuf(i.output,i.next_out))):this.onData(o.shrinkBuf(i.output,i.next_out))),(0<i.avail_in||0===i.avail_out)&&1!==r);return 4===n?(r=a.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r):2!==n||(this.onEnd(0),!(i.avail_out=0))},n.prototype.onData=function(t){this.chunks.push(t)},n.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Deflate=n,r.deflate=i,r.deflateRaw=function(t,e){return(e=e||{}).raw=!0,i(t,e)},r.gzip=function(t,e){return(e=e||{}).gzip=!0,i(t,e)}},{"./utils/common":62,"./utils/strings":63,"./zlib/deflate":67,"./zlib/messages":72,"./zlib/zstream":74}],61:[function(t,e,r){"use strict";function n(t){if(!(this instanceof n))return new n(t);this.options=f.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options,t=(e.raw&&0<=e.windowBits&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits)&&(e.windowBits=-15),!(0<=e.windowBits&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),15<e.windowBits&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0,c.inflateInit2(this.strm,e.windowBits));if(t!==p.Z_OK)throw new Error(s[t]);this.header=new o,c.inflateGetHeader(this.strm,this.header)}function i(t,e){e=new n(e);if(e.push(t,!0),e.err)throw e.msg||s[e.err];return e.result}var c=t("./zlib/inflate"),f=t("./utils/common"),d=t("./utils/strings"),p=t("./zlib/constants"),s=t("./zlib/messages"),a=t("./zlib/zstream"),o=t("./zlib/gzheader"),m=Object.prototype.toString;n.prototype.push=function(t,e){var r,n,i,s,a,o=this.strm,u=this.options.chunkSize,h=this.options.dictionary,l=!1;if(this.ended)return!1;n=e===~~e?e:!0===e?p.Z_FINISH:p.Z_NO_FLUSH,"string"==typeof t?o.input=d.binstring2buf(t):"[object ArrayBuffer]"===m.call(t)?o.input=new Uint8Array(t):o.input=t,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new f.Buf8(u),o.next_out=0,o.avail_out=u),(r=c.inflate(o,p.Z_NO_FLUSH))===p.Z_NEED_DICT&&h&&(a="string"==typeof h?d.string2buf(h):"[object ArrayBuffer]"===m.call(h)?new Uint8Array(h):h,r=c.inflateSetDictionary(this.strm,a)),r===p.Z_BUF_ERROR&&!0===l&&(r=p.Z_OK,l=!1),r!==p.Z_STREAM_END&&r!==p.Z_OK)return this.onEnd(r),!(this.ended=!0)}while(!o.next_out||0!==o.avail_out&&r!==p.Z_STREAM_END&&(0!==o.avail_in||n!==p.Z_FINISH&&n!==p.Z_SYNC_FLUSH)||("string"===this.options.to?(a=d.utf8border(o.output,o.next_out),i=o.next_out-a,s=d.buf2string(o.output,a),o.next_out=i,o.avail_out=u-i,i&&f.arraySet(o.output,o.output,a,i,0),this.onData(s)):this.onData(f.shrinkBuf(o.output,o.next_out))),0===o.avail_in&&0===o.avail_out&&(l=!0),(0<o.avail_in||0===o.avail_out)&&r!==p.Z_STREAM_END);return(n=r===p.Z_STREAM_END?p.Z_FINISH:n)===p.Z_FINISH?(r=c.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===p.Z_OK):n!==p.Z_SYNC_FLUSH||(this.onEnd(p.Z_OK),!(o.avail_out=0))},n.prototype.onData=function(t){this.chunks.push(t)},n.prototype.onEnd=function(t){t===p.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=f.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Inflate=n,r.inflate=i,r.inflateRaw=function(t,e){return(e=e||{}).raw=!0,i(t,e)},r.ungzip=i},{"./utils/common":62,"./utils/strings":63,"./zlib/constants":65,"./zlib/gzheader":68,"./zlib/inflate":70,"./zlib/messages":72,"./zlib/zstream":74}],62:[function(t,e,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array,i=(r.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var r=e.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n])}}return t},r.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)},{arraySet:function(t,e,r,n,i){if(e.subarray&&t.subarray)t.set(e.subarray(r,r+n),i);else for(var s=0;s<n;s++)t[i+s]=e[r+s]},flattenChunks:function(t){for(var e,r,n,i=0,s=0,a=t.length;s<a;s++)i+=t[s].length;for(n=new Uint8Array(i),s=e=0,a=t.length;s<a;s++)r=t[s],n.set(r,e),e+=r.length;return n}}),s={arraySet:function(t,e,r,n,i){for(var s=0;s<n;s++)t[i+s]=e[r+s]},flattenChunks:function(t){return[].concat.apply([],t)}};r.setTyped=function(t){t?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,s))},r.setTyped(n)},{}],63:[function(t,e,r){"use strict";function u(t,e){if(e<65537&&(t.subarray&&s||!t.subarray&&i))return String.fromCharCode.apply(null,h.shrinkBuf(t,e));for(var r="",n=0;n<e;n++)r+=String.fromCharCode(t[n]);return r}var h=t("./common"),i=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(t){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){s=!1}for(var l=new h.Buf8(256),n=0;n<256;n++)l[n]=252<=n?6:248<=n?5:240<=n?4:224<=n?3:192<=n?2:1;l[254]=l[254]=1,r.string2buf=function(t){for(var e,r,n,i,s=t.length,a=0,o=0;o<s;o++)55296==(64512&(r=t.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=t.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),a+=r<128?1:r<2048?2:r<65536?3:4;for(e=new h.Buf8(a),o=i=0;i<a;o++)55296==(64512&(r=t.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=t.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),r<128?e[i++]=r:(r<2048?e[i++]=192|r>>>6:(r<65536?e[i++]=224|r>>>12:(e[i++]=240|r>>>18,e[i++]=128|r>>>12&63),e[i++]=128|r>>>6&63),e[i++]=128|63&r);return e},r.buf2binstring=function(t){return u(t,t.length)},r.binstring2buf=function(t){for(var e=new h.Buf8(t.length),r=0,n=e.length;r<n;r++)e[r]=t.charCodeAt(r);return e},r.buf2string=function(t,e){for(var r,n,i=e||t.length,s=new Array(2*i),a=0,o=0;o<i;)if((r=t[o++])<128)s[a++]=r;else if(4<(n=l[r]))s[a++]=65533,o+=n-1;else{for(r&=2===n?31:3===n?15:7;1<n&&o<i;)r=r<<6|63&t[o++],n--;1<n?s[a++]=65533:r<65536?s[a++]=r:(r-=65536,s[a++]=55296|r>>10&1023,s[a++]=56320|1023&r)}return u(s,a)},r.utf8border=function(t,e){for(var r=(e=(e=e||t.length)>t.length?t.length:e)-1;0<=r&&128==(192&t[r]);)r--;return!(r<0)&&0!==r&&r+l[t[r]]>e?r:e}},{"./common":62}],64:[function(t,e,r){"use strict";e.exports=function(t,e,r,n){for(var i=65535&t|0,s=t>>>16&65535|0,a=0;0!==r;){for(r-=a=2e3<r?2e3:r;s=s+(i=i+e[n++]|0)|0,--a;);i%=65521,s%=65521}return i|s<<16|0}},{}],65:[function(t,e,r){"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],66:[function(t,e,r){"use strict";var o=function(){for(var t=[],e=0;e<256;e++){for(var r=e,n=0;n<8;n++)r=1&r?3988292384^r>>>1:r>>>1;t[e]=r}return t}();e.exports=function(t,e,r,n){var i=o,s=n+r;t^=-1;for(var a=n;a<s;a++)t=t>>>8^i[255&(t^e[a])];return-1^t}},{}],67:[function(t,N,e){"use strict";function u(t,e){return t.msg=j[e],e}function o(t){return(t<<1)-(4<t?9:0)}function l(t){for(var e=t.length;0<=--e;)t[e]=0}function h(t){var e=t.state,r=e.pending;0!==(r=r>t.avail_out?t.avail_out:r)&&(g.arraySet(t.output,e.pending_buf,e.pending_out,r,t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending)&&(e.pending_out=0)}function c(t,e){b._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,h(t.strm)}function f(t,e){t.pending_buf[t.pending++]=e}function d(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function s(t,e){var r,n,i=t.max_chain_length,s=t.strstart,a=t.prev_length,o=t.nice_match,u=t.strstart>t.w_size-A?t.strstart-(t.w_size-A):0,h=t.window,l=t.w_mask,c=t.prev,f=t.strstart+E,d=h[s+a-1],p=h[s+a];t.prev_length>=t.good_match&&(i>>=2),o>t.lookahead&&(o=t.lookahead);do{if(h[(r=e)+a]===p&&h[r+a-1]===d&&h[r]===h[s]&&h[++r]===h[s+1]){for(s+=2,r++;h[++s]===h[++r]&&h[++s]===h[++r]&&h[++s]===h[++r]&&h[++s]===h[++r]&&h[++s]===h[++r]&&h[++s]===h[++r]&&h[++s]===h[++r]&&h[++s]===h[++r]&&s<f;);if(n=E-(f-s),s=f-E,a<n){if(t.match_start=e,o<=(a=n))break;d=h[s+a-1],p=h[s+a]}}}while((e=c[e&l])>u&&0!=--i);return a<=t.lookahead?a:t.lookahead}function p(t){var e,r,n,i,s,a,o,u,h,l=t.w_size;do{if(u=t.window_size-t.lookahead-t.strstart,t.strstart>=l+(l-A)){for(g.arraySet(t.window,t.window,l,l,0),t.match_start-=l,t.strstart-=l,t.block_start-=l,e=r=t.hash_size;n=t.head[--e],t.head[e]=l<=n?n-l:0,--r;);for(e=r=l;n=t.prev[--e],t.prev[e]=l<=n?n-l:0,--r;);u+=l}if(0===t.strm.avail_in)break;if(s=t.strm,a=t.window,o=t.strstart+t.lookahead,u=u,h=void 0,h=s.avail_in,r=0===(h=u<h?u:h)?0:(s.avail_in-=h,g.arraySet(a,s.input,s.next_in,h,o),1===s.state.wrap?s.adler=U(s.adler,a,h,o):2===s.state.wrap&&(s.adler=y(s.adler,a,h,o)),s.next_in+=h,s.total_in+=h,h),t.lookahead+=r,t.lookahead+t.insert>=C)for(i=t.strstart-t.insert,t.ins_h=t.window[i],t.ins_h=(t.ins_h<<t.hash_shift^t.window[i+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[i+C-1])&t.hash_mask,t.prev[i&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=i,i++,t.insert--,!(t.lookahead+t.insert<C)););}while(t.lookahead<A&&0!==t.strm.avail_in)}function r(t,e){for(var r,n;;){if(t.lookahead<A){if(p(t),t.lookahead<A&&e===v)return B;if(0===t.lookahead)break}if(r=0,t.lookahead>=C&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+C-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-A&&(t.match_length=s(t,r)),t.match_length>=C)if(n=b._tr_tally(t,t.strstart-t.match_start,t.match_length-C),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=C){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+C-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else n=b._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(c(t,!1),0===t.strm.avail_out))return B}return t.insert=t.strstart<C-1?t.strstart:C-1,e===w?(c(t,!0),0===t.strm.avail_out?T:D):t.last_lit&&(c(t,!1),0===t.strm.avail_out)?B:R}function n(t,e){for(var r,n,i;;){if(t.lookahead<A){if(p(t),t.lookahead<A&&e===v)return B;if(0===t.lookahead)break}if(r=0,t.lookahead>=C&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+C-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=C-1,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-A&&(t.match_length=s(t,r),t.match_length<=5)&&(1===t.strategy||t.match_length===C&&4096<t.strstart-t.match_start)&&(t.match_length=C-1),t.prev_length>=C&&t.match_length<=t.prev_length){for(i=t.strstart+t.lookahead-C,n=b._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-C),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=i&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+C-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=C-1,t.strstart++,n&&(c(t,!1),0===t.strm.avail_out))return B}else if(t.match_available){if((n=b._tr_tally(t,0,t.window[t.strstart-1]))&&c(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return B}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=b._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<C-1?t.strstart:C-1,e===w?(c(t,!0),0===t.strm.avail_out?T:D):t.last_lit&&(c(t,!1),0===t.strm.avail_out)?B:R}function i(t,e,r,n,i){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=n,this.func=i}function P(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=S,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new g.Buf16(2*K),this.dyn_dtree=new g.Buf16(2*(2*H+1)),this.bl_tree=new g.Buf16(2*(2*G+1)),l(this.dyn_ltree),l(this.dyn_dtree),l(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new g.Buf16(Y+1),this.heap=new g.Buf16(2*z+1),l(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new g.Buf16(2*z+1),l(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function a(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=W,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?I:O,t.adler=2===e.wrap?0:1,e.last_flush=v,b._tr_init(e),k):u(t,x)}function m(t){var e=a(t);return e===k&&((t=t.state).window_size=2*t.w_size,l(t.head),t.max_lazy_match=F[t.level].max_lazy,t.good_match=F[t.level].good_length,t.nice_match=F[t.level].nice_length,t.max_chain_length=F[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=C-1,t.match_available=0,t.ins_h=0),e}function _(t,e,r,n,i,s){if(!t)return x;var a=1;if(e===L&&(e=6),n<0?(a=0,n=-n):15<n&&(a=2,n-=16),i<1||M<i||r!==S||n<8||15<n||e<0||9<e||s<0||Z<s)return u(t,x);8===n&&(n=9);var o=new P;return(t.state=o).strm=t,o.wrap=a,o.gzhead=null,o.w_bits=n,o.w_size=1<<o.w_bits,o.w_mask=o.w_size-1,o.hash_bits=i+7,o.hash_size=1<<o.hash_bits,o.hash_mask=o.hash_size-1,o.hash_shift=~~((o.hash_bits+C-1)/C),o.window=new g.Buf8(2*o.w_size),o.head=new g.Buf16(o.hash_size),o.prev=new g.Buf16(o.w_size),o.lit_bufsize=1<<i+6,o.pending_buf_size=4*o.lit_bufsize,o.pending_buf=new g.Buf8(o.pending_buf_size),o.d_buf=+o.lit_bufsize,o.l_buf=3*o.lit_bufsize,o.level=e,o.strategy=s,o.method=r,m(t)}var g=t("../utils/common"),b=t("./trees"),U=t("./adler32"),y=t("./crc32"),j=t("./messages"),v=0,w=4,k=0,x=-2,L=-1,Z=4,W=2,S=8,M=9,z=286,H=30,G=19,K=2*z+1,Y=15,C=3,E=258,A=E+C+1,I=42,O=113,B=1,R=2,T=3,D=4,F=[new i(0,0,0,0,function(t,e){var r=65535;for(r>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(p(t),0===t.lookahead&&e===v)return B;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+r;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,c(t,!1),0===t.strm.avail_out))return B;if(t.strstart-t.block_start>=t.w_size-A&&(c(t,!1),0===t.strm.avail_out))return B}return t.insert=0,e===w?(c(t,!0),0===t.strm.avail_out?T:D):(t.strstart>t.block_start&&(c(t,!1),t.strm.avail_out),B)}),new i(4,4,8,4,r),new i(4,5,16,8,r),new i(4,6,32,32,r),new i(4,4,16,16,n),new i(8,16,32,32,n),new i(8,16,128,128,n),new i(8,32,128,256,n),new i(32,128,258,1024,n),new i(32,258,258,4096,n)];e.deflateInit=function(t,e){return _(t,e,S,15,8,0)},e.deflateInit2=_,e.deflateReset=m,e.deflateResetKeep=a,e.deflateSetHeader=function(t,e){return!t||!t.state||2!==t.state.wrap?x:(t.state.gzhead=e,k)},e.deflate=function(t,e){var r,n,i,s;if(!t||!t.state||5<e||e<0)return t?u(t,x):x;if(n=t.state,!t.output||!t.input&&0!==t.avail_in||666===n.status&&e!==w)return u(t,0===t.avail_out?-5:x);if(n.strm=t,r=n.last_flush,n.last_flush=e,n.status===I&&(2===n.wrap?(t.adler=0,f(n,31),f(n,139),f(n,8),n.gzhead?(f(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),f(n,255&n.gzhead.time),f(n,n.gzhead.time>>8&255),f(n,n.gzhead.time>>16&255),f(n,n.gzhead.time>>24&255),f(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),f(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(f(n,255&n.gzhead.extra.length),f(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(t.adler=y(t.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(f(n,0),f(n,0),f(n,0),f(n,0),f(n,0),f(n,9===n.level?2:2<=n.strategy||n.level<2?4:0),f(n,3),n.status=O)):(a=S+(n.w_bits-8<<4)<<8,a|=(2<=n.strategy||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(a|=32),a+=31-a%31,n.status=O,d(n,a),0!==n.strstart&&(d(n,t.adler>>>16),d(n,65535&t.adler)),t.adler=1)),69===n.status)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>i&&(t.adler=y(t.adler,n.pending_buf,n.pending-i,i)),h(t),i=n.pending,n.pending!==n.pending_buf_size));)f(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(t.adler=y(t.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(t.adler=y(t.adler,n.pending_buf,n.pending-i,i)),h(t),i=n.pending,n.pending===n.pending_buf_size)){s=1;break}}while(s=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,f(n,s),0!==s);n.gzhead.hcrc&&n.pending>i&&(t.adler=y(t.adler,n.pending_buf,n.pending-i,i)),0===s&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(t.adler=y(t.adler,n.pending_buf,n.pending-i,i)),h(t),i=n.pending,n.pending===n.pending_buf_size)){s=1;break}}while(s=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,f(n,s),0!==s);n.gzhead.hcrc&&n.pending>i&&(t.adler=y(t.adler,n.pending_buf,n.pending-i,i)),0===s&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&h(t),n.pending+2<=n.pending_buf_size&&(f(n,255&t.adler),f(n,t.adler>>8&255),t.adler=0,n.status=O)):n.status=O),0!==n.pending){if(h(t),0===t.avail_out)return n.last_flush=-1,k}else if(0===t.avail_in&&o(e)<=o(r)&&e!==w)return u(t,-5);if(666===n.status&&0!==t.avail_in)return u(t,-5);if(0!==t.avail_in||0!==n.lookahead||e!==v&&666!==n.status){var a=2===n.strategy?function(t,e){for(var r;;){if(0===t.lookahead&&(p(t),0===t.lookahead)){if(e===v)return B;break}if(t.match_length=0,r=b._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(c(t,!1),0===t.strm.avail_out))return B}return t.insert=0,e===w?(c(t,!0),0===t.strm.avail_out?T:D):t.last_lit&&(c(t,!1),0===t.strm.avail_out)?B:R}(n,e):3===n.strategy?function(t,e){for(var r,n,i,s,a=t.window;;){if(t.lookahead<=E){if(p(t),t.lookahead<=E&&e===v)return B;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=C&&0<t.strstart&&(n=a[i=t.strstart-1])===a[++i]&&n===a[++i]&&n===a[++i]){for(s=t.strstart+E;n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&i<s;);t.match_length=E-(s-i),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=C?(r=b._tr_tally(t,1,t.match_length-C),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=b._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(c(t,!1),0===t.strm.avail_out))return B}return t.insert=0,e===w?(c(t,!0),0===t.strm.avail_out?T:D):t.last_lit&&(c(t,!1),0===t.strm.avail_out)?B:R}(n,e):F[n.level].func(n,e);if(a!==T&&a!==D||(n.status=666),a===B||a===T)return 0===t.avail_out&&(n.last_flush=-1),k;if(a===R&&(1===e?b._tr_align(n):5!==e&&(b._tr_stored_block(n,0,0,!1),3===e)&&(l(n.head),0===n.lookahead)&&(n.strstart=0,n.block_start=0,n.insert=0),h(t),0===t.avail_out))return n.last_flush=-1,k}return e!==w||!(n.wrap<=0)&&(2===n.wrap?(f(n,255&t.adler),f(n,t.adler>>8&255),f(n,t.adler>>16&255),f(n,t.adler>>24&255),f(n,255&t.total_in),f(n,t.total_in>>8&255),f(n,t.total_in>>16&255),f(n,t.total_in>>24&255)):(d(n,t.adler>>>16),d(n,65535&t.adler)),h(t),0<n.wrap&&(n.wrap=-n.wrap),0!==n.pending)?k:1},e.deflateEnd=function(t){var e;return t&&t.state?(e=t.state.status)!==I&&69!==e&&73!==e&&91!==e&&103!==e&&e!==O&&666!==e?u(t,x):(t.state=null,e===O?u(t,-3):k):x},e.deflateSetDictionary=function(t,e){var r,n,i,s,a,o,u,h=e.length;if(!t||!t.state)return x;if(2===(s=(r=t.state).wrap)||1===s&&r.status!==I||r.lookahead)return x;for(1===s&&(t.adler=U(t.adler,e,h,0)),r.wrap=0,h>=r.w_size&&(0===s&&(l(r.head),r.strstart=0,r.block_start=0,r.insert=0),u=new g.Buf8(r.w_size),g.arraySet(u,e,h-r.w_size,r.w_size,0),e=u,h=r.w_size),u=t.avail_in,a=t.next_in,o=t.input,t.avail_in=h,t.next_in=0,t.input=e,p(r);r.lookahead>=C;){for(n=r.strstart,i=r.lookahead-(C-1);r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+C-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++,--i;);r.strstart=n,r.lookahead=C-1,p(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=C-1,r.match_available=0,t.next_in=a,t.input=o,t.avail_in=u,r.wrap=s,k},e.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":62,"./adler32":64,"./crc32":66,"./messages":72,"./trees":73}],68:[function(t,e,r){"use strict";e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],69:[function(t,e,r){"use strict";e.exports=function(t,e){var r,n,i,s,a,o,u=t.state,h=t.next_in,l=t.input,c=h+(t.avail_in-5),f=t.next_out,d=t.output,p=f-(e-t.avail_out),m=f+(t.avail_out-257),_=u.dmax,g=u.wsize,b=u.whave,y=u.wnext,v=u.window,w=u.hold,k=u.bits,x=u.lencode,S=u.distcode,z=(1<<u.lenbits)-1,C=(1<<u.distbits)-1;t:do{for(k<15&&(w+=l[h++]<<k,k+=8,w+=l[h++]<<k,k+=8),r=x[w&z];;){if(w>>>=n=r>>>24,k-=n,0===(n=r>>>16&255))d[f++]=65535&r;else{if(!(16&n)){if(0==(64&n)){r=x[(65535&r)+(w&(1<<n)-1)];continue}if(32&n){u.mode=12;break t}t.msg="invalid literal/length code",u.mode=30;break t}for(i=65535&r,(n&=15)&&(k<n&&(w+=l[h++]<<k,k+=8),i+=w&(1<<n)-1,w>>>=n,k-=n),k<15&&(w+=l[h++]<<k,k+=8,w+=l[h++]<<k,k+=8),r=S[w&C];;){if(w>>>=n=r>>>24,k-=n,!(16&(n=r>>>16&255))){if(0==(64&n)){r=S[(65535&r)+(w&(1<<n)-1)];continue}t.msg="invalid distance code",u.mode=30;break t}if(s=65535&r,k<(n&=15)&&(w+=l[h++]<<k,(k+=8)<n)&&(w+=l[h++]<<k,k+=8),_<(s+=w&(1<<n)-1)){t.msg="invalid distance too far back",u.mode=30;break t}if(w>>>=n,k-=n,(n=f-p)<s){if(b<(n=s-n)&&u.sane){t.msg="invalid distance too far back",u.mode=30;break t}if(o=v,(a=0)===y){if(a+=g-n,n<i){for(i-=n;d[f++]=v[a++],--n;);a=f-s,o=d}}else if(y<n){if(a+=g+y-n,(n-=y)<i){for(i-=n;d[f++]=v[a++],--n;);if(a=0,y<i){for(i-=n=y;d[f++]=v[a++],--n;);a=f-s,o=d}}}else if(a+=y-n,n<i){for(i-=n;d[f++]=v[a++],--n;);a=f-s,o=d}for(;2<i;)d[f++]=o[a++],d[f++]=o[a++],d[f++]=o[a++],i-=3;i&&(d[f++]=o[a++],1<i)&&(d[f++]=o[a++])}else{for(a=f-s;d[f++]=d[a++],d[f++]=d[a++],d[f++]=d[a++],2<(i-=3););i&&(d[f++]=d[a++],1<i)&&(d[f++]=d[a++])}break}}break}}while(h<c&&f<m);w&=(1<<(k-=(i=k>>3)<<3))-1,t.next_in=h-=i,t.next_out=f,t.avail_in=h<c?c-h+5:5-(h-c),t.avail_out=f<m?m-f+257:257-(f-m),u.hold=w,u.bits=k}},{}],70:[function(t,e,r){"use strict";function B(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function n(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new F.Buf16(320),this.work=new F.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function i(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=H,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new F.Buf32(u),e.distcode=e.distdyn=new F.Buf32(h),e.sane=1,e.back=-1,W):M}function s(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,i(t)):M}function a(t,e){var r,n;return!t||!t.state||(n=t.state,e<0?(r=0,e=-e):(r=1+(e>>4),e<48&&(e&=15)),e&&(e<8||15<e))?M:(null!==n.window&&n.wbits!==e&&(n.window=null),n.wrap=r,n.wbits=e,s(t))}function o(t,e){var r;return t?(r=new n,(t.state=r).window=null,(r=a(t,e))!==W&&(t.state=null),r):M}function R(t,e,r,n){var i,t=t.state;return null===t.window&&(t.wsize=1<<t.wbits,t.wnext=0,t.whave=0,t.window=new F.Buf8(t.wsize)),n>=t.wsize?(F.arraySet(t.window,e,r-t.wsize,t.wsize,0),t.wnext=0,t.whave=t.wsize):(n<(i=t.wsize-t.wnext)&&(i=n),F.arraySet(t.window,e,r-n,i,t.wnext),(n-=i)?(F.arraySet(t.window,e,r-n,n,0),t.wnext=n,t.whave=t.wsize):(t.wnext+=i,t.wnext===t.wsize&&(t.wnext=0),t.whave<t.wsize&&(t.whave+=i))),0}var T,D,F=t("../utils/common"),N=t("./adler32"),P=t("./crc32"),U=t("./inffast"),j=t("./inftrees"),L=1,Z=2,W=0,M=-2,H=1,u=852,h=592,G=!0;r.inflateReset=s,r.inflateReset2=a,r.inflateResetKeep=i,r.inflateInit=function(t){return o(t,15)},r.inflateInit2=o,r.inflate=function(t,e){var r,n,i,s,a,o,u,h,l,c,f,d,p,m,_,g,b,y,v,w,k,x,S,z,C=0,E=new F.Buf8(4),A=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return M;12===(r=t.state).mode&&(r.mode=13),a=t.next_out,i=t.output,u=t.avail_out,s=t.next_in,n=t.input,o=t.avail_in,h=r.hold,l=r.bits,c=o,f=u,x=W;t:for(;;)switch(r.mode){case H:if(0===r.wrap)r.mode=13;else{for(;l<16;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(2&r.wrap&&35615===h)E[r.check=0]=255&h,E[1]=h>>>8&255,r.check=P(r.check,E,2,0),l=h=0,r.mode=2;else if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&h)<<8)+(h>>8))%31)t.msg="incorrect header check",r.mode=30;else if(8!=(15&h))t.msg="unknown compression method",r.mode=30;else{if(l-=4,k=8+(15&(h>>>=4)),0===r.wbits)r.wbits=k;else if(k>r.wbits){t.msg="invalid window size",r.mode=30;break}r.dmax=1<<k,t.adler=r.check=1,r.mode=512&h?10:12,l=h=0}}break;case 2:for(;l<16;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(r.flags=h,8!=(255&r.flags)){t.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){t.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=h>>8&1),512&r.flags&&(E[0]=255&h,E[1]=h>>>8&255,r.check=P(r.check,E,2,0)),l=h=0,r.mode=3;case 3:for(;l<32;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}r.head&&(r.head.time=h),512&r.flags&&(E[0]=255&h,E[1]=h>>>8&255,E[2]=h>>>16&255,E[3]=h>>>24&255,r.check=P(r.check,E,4,0)),l=h=0,r.mode=4;case 4:for(;l<16;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}r.head&&(r.head.xflags=255&h,r.head.os=h>>8),512&r.flags&&(E[0]=255&h,E[1]=h>>>8&255,r.check=P(r.check,E,2,0)),l=h=0,r.mode=5;case 5:if(1024&r.flags){for(;l<16;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}r.length=h,r.head&&(r.head.extra_len=h),512&r.flags&&(E[0]=255&h,E[1]=h>>>8&255,r.check=P(r.check,E,2,0)),l=h=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&((d=o<(d=r.length)?o:d)&&(r.head&&(k=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),F.arraySet(r.head.extra,n,s,d,k)),512&r.flags&&(r.check=P(r.check,n,d,s)),o-=d,s+=d,r.length-=d),r.length))break t;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===o)break t;for(d=0;k=n[s+d++],r.head&&k&&r.length<65536&&(r.head.name+=String.fromCharCode(k)),k&&d<o;);if(512&r.flags&&(r.check=P(r.check,n,d,s)),o-=d,s+=d,k)break t}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===o)break t;for(d=0;k=n[s+d++],r.head&&k&&r.length<65536&&(r.head.comment+=String.fromCharCode(k)),k&&d<o;);if(512&r.flags&&(r.check=P(r.check,n,d,s)),o-=d,s+=d,k)break t}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;l<16;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(h!==(65535&r.check)){t.msg="header crc mismatch",r.mode=30;break}l=h=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),t.adler=r.check=0,r.mode=12;break;case 10:for(;l<32;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}t.adler=r.check=B(h),l=h=0,r.mode=11;case 11:if(0===r.havedict)return t.next_out=a,t.avail_out=u,t.next_in=s,t.avail_in=o,r.hold=h,r.bits=l,2;t.adler=r.check=1,r.mode=12;case 12:if(5===e||6===e)break t;case 13:if(r.last)h>>>=7&l,l-=7&l,r.mode=27;else{for(;l<3;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}switch(r.last=1&h,--l,3&(h>>>=1)){case 0:r.mode=14;break;case 1:I=O=void 0;var I,O=r;if(G){for(T=new F.Buf32(512),D=new F.Buf32(32),I=0;I<144;)O.lens[I++]=8;for(;I<256;)O.lens[I++]=9;for(;I<280;)O.lens[I++]=7;for(;I<288;)O.lens[I++]=8;for(j(L,O.lens,0,288,T,0,O.work,{bits:9}),I=0;I<32;)O.lens[I++]=5;j(Z,O.lens,0,32,D,0,O.work,{bits:5}),G=!1}if(O.lencode=T,O.lenbits=9,O.distcode=D,O.distbits=5,r.mode=20,6!==e)break;h>>>=2,l-=2;break t;case 2:r.mode=17;break;case 3:t.msg="invalid block type",r.mode=30}h>>>=2,l-=2}break;case 14:for(h>>>=7&l,l-=7&l;l<32;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if((65535&h)!=(h>>>16^65535)){t.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&h,l=h=0,r.mode=15,6===e)break t;case 15:r.mode=16;case 16:if(d=r.length){if(0===(d=u<(d=o<d?o:d)?u:d))break t;F.arraySet(i,n,s,d,a),o-=d,s+=d,u-=d,a+=d,r.length-=d}else r.mode=12;break;case 17:for(;l<14;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(r.nlen=257+(31&h),h>>>=5,l-=5,r.ndist=1+(31&h),h>>>=5,l-=5,r.ncode=4+(15&h),h>>>=4,l-=4,286<r.nlen||30<r.ndist){t.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;l<3;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}r.lens[A[r.have++]]=7&h,h>>>=3,l-=3}for(;r.have<19;)r.lens[A[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,S={bits:r.lenbits},x=j(0,r.lens,0,19,r.lencode,0,r.work,S),r.lenbits=S.bits,x){t.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;g=(C=r.lencode[h&(1<<r.lenbits)-1])>>>16&255,b=65535&C,!((_=C>>>24)<=l);){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(b<16)h>>>=_,l-=_,r.lens[r.have++]=b;else{if(16===b){for(z=_+2;l<z;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(h>>>=_,l-=_,0===r.have){t.msg="invalid bit length repeat",r.mode=30;break}k=r.lens[r.have-1],d=3+(3&h),h>>>=2,l-=2}else if(17===b){for(z=_+3;l<z;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}k=0,d=3+(7&(h>>>=_)),h>>>=3,l=l-_-3}else{for(z=_+7;l<z;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}k=0,d=11+(127&(h>>>=_)),h>>>=7,l=l-_-7}if(r.have+d>r.nlen+r.ndist){t.msg="invalid bit length repeat",r.mode=30;break}for(;d--;)r.lens[r.have++]=k}}if(30===r.mode)break;if(0===r.lens[256]){t.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,S={bits:r.lenbits},x=j(L,r.lens,0,r.nlen,r.lencode,0,r.work,S),r.lenbits=S.bits,x){t.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,S={bits:r.distbits},x=j(Z,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,S),r.distbits=S.bits,x){t.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===e)break t;case 20:r.mode=21;case 21:if(6<=o&&258<=u){t.next_out=a,t.avail_out=u,t.next_in=s,t.avail_in=o,r.hold=h,r.bits=l,U(t,f),a=t.next_out,i=t.output,u=t.avail_out,s=t.next_in,n=t.input,o=t.avail_in,h=r.hold,l=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;g=(C=r.lencode[h&(1<<r.lenbits)-1])>>>16&255,b=65535&C,!((_=C>>>24)<=l);){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(g&&0==(240&g)){for(y=_,v=g,w=b;g=(C=r.lencode[w+((h&(1<<y+v)-1)>>y)])>>>16&255,b=65535&C,!(y+(_=C>>>24)<=l);){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}h>>>=y,l-=y,r.back+=y}if(h>>>=_,l-=_,r.back+=_,r.length=b,0===g){r.mode=26;break}if(32&g){r.back=-1,r.mode=12;break}if(64&g){t.msg="invalid literal/length code",r.mode=30;break}r.extra=15&g,r.mode=22;case 22:if(r.extra){for(z=r.extra;l<z;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}r.length+=h&(1<<r.extra)-1,h>>>=r.extra,l-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;g=(C=r.distcode[h&(1<<r.distbits)-1])>>>16&255,b=65535&C,!((_=C>>>24)<=l);){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(0==(240&g)){for(y=_,v=g,w=b;g=(C=r.distcode[w+((h&(1<<y+v)-1)>>y)])>>>16&255,b=65535&C,!(y+(_=C>>>24)<=l);){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}h>>>=y,l-=y,r.back+=y}if(h>>>=_,l-=_,r.back+=_,64&g){t.msg="invalid distance code",r.mode=30;break}r.offset=b,r.extra=15&g,r.mode=24;case 24:if(r.extra){for(z=r.extra;l<z;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}r.offset+=h&(1<<r.extra)-1,h>>>=r.extra,l-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){t.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===u)break t;if(r.offset>(d=f-u)){if((d=r.offset-d)>r.whave&&r.sane){t.msg="invalid distance too far back",r.mode=30;break}p=d>r.wnext?(d-=r.wnext,r.wsize-d):r.wnext-d,d>r.length&&(d=r.length),m=r.window}else m=i,p=a-r.offset,d=r.length;for(u-=d=u<d?u:d,r.length-=d;i[a++]=m[p++],--d;);0===r.length&&(r.mode=21);break;case 26:if(0===u)break t;i[a++]=r.length,u--,r.mode=21;break;case 27:if(r.wrap){for(;l<32;){if(0===o)break t;o--,h|=n[s++]<<l,l+=8}if(f-=u,t.total_out+=f,r.total+=f,f&&(t.adler=r.check=(r.flags?P:N)(r.check,i,f,a-f)),f=u,(r.flags?h:B(h))!==r.check){t.msg="incorrect data check",r.mode=30;break}l=h=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;l<32;){if(0===o)break t;o--,h+=n[s++]<<l,l+=8}if(h!==(4294967295&r.total)){t.msg="incorrect length check",r.mode=30;break}l=h=0}r.mode=29;case 29:x=1;break t;case 30:x=-3;break t;case 31:return-4;default:return M}return t.next_out=a,t.avail_out=u,t.next_in=s,t.avail_in=o,r.hold=h,r.bits=l,(r.wsize||f!==t.avail_out&&r.mode<30&&(r.mode<27||4!==e))&&R(t,t.output,t.next_out,f-t.avail_out)?(r.mode=31,-4):(c-=t.avail_in,f-=t.avail_out,t.total_in+=c,t.total_out+=f,r.total+=f,r.wrap&&f&&(t.adler=r.check=(r.flags?P:N)(r.check,i,f,t.next_out-f)),t.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),x=(0==c&&0===f||4===e)&&x===W?-5:x)},r.inflateEnd=function(t){var e;return t&&t.state?((e=t.state).window&&(e.window=null),t.state=null,W):M},r.inflateGetHeader=function(t,e){return!t||!t.state||0==(2&(t=t.state).wrap)?M:((t.head=e).done=!1,W)},r.inflateSetDictionary=function(t,e){var r,n=e.length;return!t||!t.state||0!==(r=t.state).wrap&&11!==r.mode?M:11===r.mode&&N(1,e,n,0)!==r.check?-3:R(t,e,n,n)?(r.mode=31,-4):(r.havedict=1,W)},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":62,"./adler32":64,"./crc32":66,"./inffast":69,"./inftrees":71}],71:[function(t,e,r){"use strict";var D=t("../utils/common"),F=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],N=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],P=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],U=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(t,e,r,n,i,s,a,o){for(var u,h,l,c,f,d,p,m,_,g=o.bits,b=0,y=0,v=0,w=0,k=0,x=0,S=0,z=0,C=0,E=0,A=null,I=0,O=new D.Buf16(16),B=new D.Buf16(16),R=null,T=0,b=0;b<=15;b++)O[b]=0;for(y=0;y<n;y++)O[e[r+y]]++;for(k=g,w=15;1<=w&&0===O[w];w--);if(w<k&&(k=w),0===w)i[s++]=20971520,i[s++]=20971520,o.bits=1;else{for(v=1;v<w&&0===O[v];v++);for(k<v&&(k=v),b=z=1;b<=15;b++)if((z=(z<<1)-O[b])<0)return-1;if(0<z&&(0===t||1!==w))return-1;for(B[1]=0,b=1;b<15;b++)B[b+1]=B[b]+O[b];for(y=0;y<n;y++)0!==e[r+y]&&(a[B[e[r+y]]++]=y);if(d=0===t?(A=R=a,19):1===t?(A=F,I-=257,R=N,T-=257,256):(A=P,R=U,-1),b=v,f=s,S=y=E=0,l=-1,c=(C=1<<(x=k))-1,1===t&&852<C||2===t&&592<C)return 1;for(;;){for(_=a[y]<d?(m=0,a[y]):a[y]>d?(m=R[T+a[y]],A[I+a[y]]):(m=96,0),u=1<<(p=b-S),v=h=1<<x;i[f+(E>>S)+(h-=u)]=p<<24|m<<16|_|0,0!==h;);for(u=1<<b-1;E&u;)u>>=1;if(0!==u?E=(E&u-1)+u:E=0,y++,0==--O[b]){if(b===w)break;b=e[r+a[y]]}if(k<b&&(E&c)!==l){for(f+=v,z=1<<(x=b-(S=0===S?k:S));x+S<w&&!((z-=O[x+S])<=0);)x++,z<<=1;if(C+=1<<x,1===t&&852<C||2===t&&592<C)return 1;i[l=E&c]=k<<24|x<<16|f-s|0}}0!==E&&(i[f+E]=b-S<<24|64<<16|0),o.bits=k}return 0}},{"../utils/common":62}],72:[function(t,e,r){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],73:[function(r,a,t){"use strict";function e(t){for(var e=t.length;0<=--e;)t[e]=0}function o(t,e,r,n,i){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=t&&t.length}function u(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function h(t){return t<256?D[t]:D[256+(t>>>7)]}function i(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function l(t,e,r){t.bi_valid>n-r?(t.bi_buf|=e<<t.bi_valid&65535,i(t,t.bi_buf),t.bi_buf=e>>n-t.bi_valid,t.bi_valid+=r-n):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)}function c(t,e,r){l(t,r[2*e],r[2*e+1])}function f(t,e){for(var r=0;r|=1&t,t>>>=1,r<<=1,0<--e;);return r>>>1}function z(t,e,r){for(var n,i=new Array(A+1),s=0,a=1;a<=A;a++)i[a]=s=s+r[a-1]<<1;for(n=0;n<=e;n++){var o=t[2*n+1];0!==o&&(t[2*n]=f(i[o]++,o))}}function d(t){for(var e=0;e<x;e++)t.dyn_ltree[2*e]=0;for(e=0;e<S;e++)t.dyn_dtree[2*e]=0;for(e=0;e<E;e++)t.bl_tree[2*e]=0;t.dyn_ltree[2*I]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function p(t){8<t.bi_valid?i(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function s(t,e,r,n){var i=2*e,s=2*r;return t[i]<t[s]||t[i]===t[s]&&n[e]<=n[r]}function C(t,e,r){for(var n=t.heap[r],i=r<<1;i<=t.heap_len&&(i<t.heap_len&&s(e,t.heap[i+1],t.heap[i],t.depth)&&i++,!s(e,n,t.heap[i],t.depth));)t.heap[r]=t.heap[i],r=i,i<<=1;t.heap[r]=n}function m(t,e,r){var n,i,s,a,o=0;if(0!==t.last_lit)for(;n=t.pending_buf[t.d_buf+2*o]<<8|t.pending_buf[t.d_buf+2*o+1],i=t.pending_buf[t.l_buf+o],o++,0==n?c(t,i,e):(c(t,(s=F[i])+k+1,e),0!==(a=O[s])&&l(t,i-=N[s],a),c(t,s=h(--n),r),0!==(a=B[s])&&l(t,n-=P[s],a)),o<t.last_lit;);c(t,I,e)}function _(t,e){var r,n,i,s=e.dyn_tree,a=e.stat_desc.static_tree,o=e.stat_desc.has_stree,u=e.stat_desc.elems,h=-1;for(t.heap_len=0,t.heap_max=L,r=0;r<u;r++)0!==s[2*r]?(t.heap[++t.heap_len]=h=r,t.depth[r]=0):s[2*r+1]=0;for(;t.heap_len<2;)s[2*(i=t.heap[++t.heap_len]=h<2?++h:0)]=1,t.depth[i]=0,t.opt_len--,o&&(t.static_len-=a[2*i+1]);for(e.max_code=h,r=t.heap_len>>1;1<=r;r--)C(t,s,r);for(i=u;r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],C(t,s,1),n=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=n,s[2*i]=s[2*r]+s[2*n],t.depth[i]=(t.depth[r]>=t.depth[n]?t.depth[r]:t.depth[n])+1,s[2*r+1]=s[2*n+1]=i,t.heap[1]=i++,C(t,s,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1];for(var l,c,f,d,p,m=t,_=e.dyn_tree,g=e.max_code,b=e.stat_desc.static_tree,y=e.stat_desc.has_stree,v=e.stat_desc.extra_bits,w=e.stat_desc.extra_base,k=e.stat_desc.max_length,x=0,S=0;S<=A;S++)m.bl_count[S]=0;for(_[2*m.heap[m.heap_max]+1]=0,l=m.heap_max+1;l<L;l++)k<(S=_[2*_[2*(c=m.heap[l])+1]+1]+1)&&(S=k,x++),_[2*c+1]=S,g<c||(m.bl_count[S]++,d=0,w<=c&&(d=v[c-w]),p=_[2*c],m.opt_len+=p*(S+d),y&&(m.static_len+=p*(b[2*c+1]+d)));if(0!==x){do{for(S=k-1;0===m.bl_count[S];)S--}while(m.bl_count[S]--,m.bl_count[S+1]+=2,m.bl_count[k]--,0<(x-=2));for(S=k;0!==S;S--)for(c=m.bl_count[S];0!==c;)g<(f=m.heap[--l])||(_[2*f+1]!==S&&(m.opt_len+=(S-_[2*f+1])*_[2*f],_[2*f+1]=S),c--)}z(s,h,t.bl_count)}function g(t,e,r){var n,i,s=-1,a=e[1],o=0,u=7,h=4;for(0===a&&(u=138,h=3),e[2*(r+1)+1]=65535,n=0;n<=r;n++)i=a,a=e[2*(n+1)+1],++o<u&&i===a||(o<h?t.bl_tree[2*i]+=o:0!==i?(i!==s&&t.bl_tree[2*i]++,t.bl_tree[2*W]++):o<=10?t.bl_tree[2*M]++:t.bl_tree[2*H]++,s=i,h=(o=0)===a?(u=138,3):i===a?(u=6,3):(u=7,4))}function b(t,e,r){var n,i,s=-1,a=e[1],o=0,u=7,h=4;for(0===a&&(u=138,h=3),n=0;n<=r;n++)if(i=a,a=e[2*(n+1)+1],!(++o<u&&i===a)){if(o<h)for(;c(t,i,t.bl_tree),0!=--o;);else 0!==i?(i!==s&&(c(t,i,t.bl_tree),o--),c(t,W,t.bl_tree),l(t,o-3,2)):o<=10?(c(t,M,t.bl_tree),l(t,o-3,3)):(c(t,H,t.bl_tree),l(t,o-11,7));s=i,h=(o=0)===a?(u=138,3):i===a?(u=6,3):(u=7,4)}}function y(t,e,r,n){l(t,0+(n?1:0),3),n=e,e=r,r=!0,p(t=t),r&&(i(t,e),i(t,~e)),U.arraySet(t.pending_buf,t.window,n,e,t.pending),t.pending+=e}var U=r("../utils/common"),v=0,j=1,w=29,k=256,x=k+1+w,S=30,E=19,L=2*x+1,A=15,n=16,Z=7,I=256,W=16,M=17,H=18,O=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],B=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],G=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],K=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],R=new Array(2*(x+2)),T=(e(R),new Array(2*S)),D=(e(T),new Array(512)),F=(e(D),new Array(256)),N=(e(F),new Array(w)),P=(e(N),new Array(S));e(P);var Y,X,V,q=!1;t._tr_init=function(t){if(!q){for(var e,r,n,i=new Array(A+1),s=0,a=0;a<w-1;a++)for(N[a]=s,e=0;e<1<<O[a];e++)F[s++]=a;for(F[s-1]=a,a=n=0;a<16;a++)for(P[a]=n,e=0;e<1<<B[a];e++)D[n++]=a;for(n>>=7;a<S;a++)for(P[a]=n<<7,e=0;e<1<<B[a]-7;e++)D[256+n++]=a;for(r=0;r<=A;r++)i[r]=0;for(e=0;e<=143;)R[2*e+1]=8,e++,i[8]++;for(;e<=255;)R[2*e+1]=9,e++,i[9]++;for(;e<=279;)R[2*e+1]=7,e++,i[7]++;for(;e<=287;)R[2*e+1]=8,e++,i[8]++;for(z(R,x+1,i),e=0;e<S;e++)T[2*e+1]=5,T[2*e]=f(e,5);Y=new o(R,O,k+1,x,A),X=new o(T,B,0,S,A),V=new o(new Array(0),G,0,E,Z),q=!0}t.l_desc=new u(t.dyn_ltree,Y),t.d_desc=new u(t.dyn_dtree,X),t.bl_desc=new u(t.bl_tree,V),t.bi_buf=0,t.bi_valid=0,d(t)},t._tr_stored_block=y,t._tr_flush_block=function(t,e,r,n){var i,s,a,o=0;if(0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){for(var e=4093624447,r=0;r<=31;r++,e>>>=1)if(1&e&&0!==t.dyn_ltree[2*r])return v;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return j;for(r=32;r<k;r++)if(0!==t.dyn_ltree[2*r])return j;return v}(t)),_(t,t.l_desc),_(t,t.d_desc),o=function(t){var e;for(g(t,t.dyn_ltree,t.l_desc.max_code),g(t,t.dyn_dtree,t.d_desc.max_code),_(t,t.bl_desc),e=E-1;3<=e&&0===t.bl_tree[2*K[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=i&&(i=s)):i=s=r+5,r+4<=i&&-1!==e)y(t,e,r,n);else if(4===t.strategy||s===i)l(t,2+(n?1:0),3),m(t,R,T);else{l(t,4+(n?1:0),3);var u=t,h=(e=t.l_desc.max_code+1,r=t.d_desc.max_code+1,o+1);for(l(u,e-257,5),l(u,r-1,5),l(u,h-4,4),a=0;a<h;a++)l(u,u.bl_tree[2*K[a]+1],3);b(u,u.dyn_ltree,e-1),b(u,u.dyn_dtree,r-1),m(t,t.dyn_ltree,t.dyn_dtree)}d(t),n&&p(t)},t._tr_tally=function(t,e,r){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&r,t.last_lit++,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(F[r]+k+1)]++,t.dyn_dtree[2*h(e)]++),t.last_lit===t.lit_bufsize-1},t._tr_align=function(t){l(t,2,3),c(t,I,R),16===(t=t).bi_valid?(i(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):8<=t.bi_valid&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},{"../utils/common":62}],74:[function(t,e,r){"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}]},{},[10])(10)});