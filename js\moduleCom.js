import localStorage from"./localstore.js";
//fetch 请求
async function ajaxFetch(o,e,t=""){var r={},n={method:e,headers:{"content-type":"application/json"},
// credentials:"include",
cache:"no-cache",credentials:"same-origin",mode:"cors",redirect:"follow",referrer:"no-referrer"};if("post"==e)n.body=JSON.stringify(t);else if("get"==e)for(var a in-1!==o.indexOf("?")&&(o+="&"),t)o+=a+"="+t[a]+"&";return await fetch(o,n).then(e=>{var t=e.headers.get("user_id"),r=e.headers.get("level"),n=e.headers.get("extention_msg_id");return-1!==o.indexOf("ixspy.com")&&-1==o.indexOf("cdn.ixspy.com")&&-1===o.indexOf("-event-")&&-1===o.indexOf("smt-version")&&-1===o.indexOf("class-config")&&(localStorage.setItem("user_id",t),localStorage.setItem("level",r),localStorage.setItem("msg_id",n)),e.json()}).then(e=>{r=e}).catch(e=>{r=!1}),r}async function fetchLongWords(e,t,r=""){var n={},o={method:t,headers:{"content-type":"application/x-www-form-urlencoded"},
// credentials:"include",
cache:"no-cache",credentials:"same-origin",mode:"cors",redirect:"follow",referrer:"no-referrer"};return"post"==t&&(o.body=JSON.stringify(r)),-1!==e.indexOf("/searchSuggest/")?await fetch(e,o).then(e=>e.json()).then(e=>{n=e.data.keywords}).catch(e=>{console.log("获取长尾词错误".url,e),n=!1}):await fetch(e,o).then(e=>e.text()).then(e=>{n=e}).catch(e=>{console.log("获取长尾词错误".url,e),n=!1}),n}function pub_isEmpty(e){if(null!=e&&""!=e)switch(typeof e){case"string":return""==e||null==e?!0:!1;case"object":return 0===e.length?!0:null==e.length&&0==Object.keys(e).length;case"number":return!1}return!0}function compareLevel(e,t){var e=(e+="").split(","),r=!1;return e.forEach(e=>{-1!==t.indexOf(e+"")&&(r=!0)}),r}function pub_format(e,r){return e.replace(/#\{(.*?)\}/g,function(e,t){return r&&t in r?r[t]:""})}function pub_xDataLength(e){return(1==Array.isArray(e)?e:Object.keys(e)).length}export{ajaxFetch,fetchLongWords,pub_isEmpty,compareLevel,pub_format,pub_xDataLength};