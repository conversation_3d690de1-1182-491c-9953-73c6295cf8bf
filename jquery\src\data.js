define(["./core","./core/access","./core/camelCase","./data/var/dataPriv","./data/var/dataUser"],function(t,o,c,u,f){"use strict";
//	Implementation Summary
//
//	1. Enforce API surface and semantic compatibility with 1.9.x branch
//	2. Improve the module's maintainability by reducing the storage
//		paths to a single mechanism.
//	3. Use the same single mechanism to support "private" and "user" data.
//	4. _Never_ expose "private" data to user code (TODO: Drop _data, _removeData)
//	5. Avoid exposing implementation details on user objects (eg. expando properties)
//	6. Provide a clear path for implementation upgrade to WeakMap in 2014
var i=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,s=/[A-Z]/g;function d(t,e,a){var n,r;
// If nothing was found internally, try to fetch any
// data from the HTML5 data-* attribute
if(void 0===a&&1===t.nodeType)if(n="data-"+e.replace(s,"-$&").toLowerCase(),"string"==typeof(a=t.getAttribute(n))){try{a="true"===(r=a)||"false"!==r&&("null"===r?null:
// Only convert to a number if it doesn't change the string
r===+r+""?+r:i.test(r)?JSON.parse(r):r)}catch(t){}
// Make sure we set the data so it isn't changed later
f.set(t,e,a)}else a=void 0;return a}return t.extend({hasData:function(t){return f.hasData(t)||u.hasData(t)},data:function(t,e,a){return f.access(t,e,a)},removeData:function(t,e){f.remove(t,e)},
// TODO: Now that all calls to _data and _removeData have been replaced
// with direct calls to dataPriv methods, these can be deprecated.
_data:function(t,e,a){return u.access(t,e,a)},_removeData:function(t,e){u.remove(t,e)}}),t.fn.extend({data:function(a,t){var e,n,r,i=this[0],s=i&&i.attributes;
// Gets all values
if(void 0!==a)
// Sets multiple values
return"object"==typeof a?this.each(function(){f.set(this,a)}):o(this,function(t){var e;
// The calling jQuery object (element matches) is not empty
// (and therefore has an element appears at this[ 0 ]) and the
// `value` parameter was not undefined. An empty jQuery object
// will result in `undefined` for elem = this[ 0 ] which will
// throw an exception if an attempt to read a data cache is made.
if(i&&void 0===t)return void 0!==(
// Attempt to get data from the cache
// The key will always be camelCased in Data
e=f.get(i,a))||void 0!==(
// Attempt to "discover" the data in
// HTML5 custom data-* attrs
e=d(i,a))?e:
// We tried really hard, but the data doesn't exist.
void 0;
// Set the data...
this.each(function(){
// We always store the camelCased key
f.set(this,a,t)})},null,t,1<arguments.length,null,!0);if(this.length&&(r=f.get(i),1===i.nodeType)&&!u.get(i,"hasDataAttrs")){for(e=s.length;e--;)
// Support: IE 11 only
// The attrs elements can be null (#14894)
s[e]&&0===(n=s[e].name).indexOf("data-")&&(n=c(n.slice(5)),d(i,n,r[n]));u.set(i,"hasDataAttrs",!0)}return r},removeData:function(t){return this.each(function(){f.remove(this,t)})}}),t});