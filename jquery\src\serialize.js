define(["./core","./core/toType","./var/rcheckableType","./var/isFunction","./core/init","./traversing",// filter
"./attributes/prop"],function(s,u,n,a){"use strict";var o=/\[\]$/,t=/\r?\n/g,r=/^(?:submit|button|image|reset|file)$/i,i=/^(?:input|select|textarea|keygen)/i;
// Serialize an array of form elements or a set of
// key/values into a query string
return s.param=function(e,n){function r(e,n){n=a(n)?n():n,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)}var t,i=[];if(null==e)return"";
// If an array was passed in, assume that it is an array of form elements.
if(Array.isArray(e)||e.jquery&&!s.isPlainObject(e))
// Serialize the form elements
s.each(e,function(){r(this.name,this.value)});else
// If traditional, encode the "old" way (the way 1.3.2 or older
// did it), otherwise encode params recursively.
for(t in e)!function r(t,e,i,a){if(Array.isArray(e))
// Serialize array item.
s.each(e,function(e,n){i||o.test(t)?
// Treat each array item as a scalar.
a(t,n):
// Item is non-scalar (array or object), encode its numeric index.
r(t+"["+("object"==typeof n&&null!=n?e:"")+"]",n,i,a)});else if(i||"object"!==u(e))
// Serialize scalar item.
a(t,e);else
// Serialize object item.
for(var n in e)r(t+"["+n+"]",e[n],i,a)}(t,e[t],n,r);
// Return the resulting serialization
return i.join("&")},s.fn.extend({serialize:function(){return s.param(this.serializeArray())},serializeArray:function(){return this.map(function(){
// Can add propHook for "elements" to filter or add form elements
var e=s.prop(this,"elements");return e?s.makeArray(e):this}).filter(function(){var e=this.type;
// Use .is( ":disabled" ) so that fieldset[disabled] works
return this.name&&!s(this).is(":disabled")&&i.test(this.nodeName)&&!r.test(e)&&(this.checked||!n.test(e))}).map(function(e,n){var r=s(this).val();return null==r?null:Array.isArray(r)?s.map(r,function(e){return{name:n.name,value:e.replace(t,"\r\n")}}):{name:n.name,value:r.replace(t,"\r\n")}}).get()}}),s});