var extFlag="google",E_VERSION="1.7.9",glob_url="https://ixspy.com",glob_goods_url=glob_url+"/smt-goods-info/?source="+extFlag+"&e-version="+E_VERSION+"&id=",glob_competitor_url=glob_url+"/smt-shopify-compete-info?source="+extFlag+"&e-version="+E_VERSION,glob_show_pic_url=glob_url+"/smt-goods-common-image?source="+extFlag+"&e-version="+E_VERSION+"&id=",glob_goods_chart_url=glob_url+"/smt-goods-chart?source="+extFlag+"&e-version="+E_VERSION+"&id=",glob_store_info_url=glob_url+"/smt-shop-info?source="+extFlag+"&e-version="+E_VERSION+"&admin_seq=",glob_favted_goods_url=glob_url+"/smt-collection-goods?source="+extFlag,glob_favted_goods_list_url=glob_url+"/smt-collection-goods-list?source="+extFlag+"&e-version="+E_VERSION+"&user_id=",glob_version=glob_url+"/smt-version?source="+extFlag+"&e-version="+E_VERSION+"&version=",glob_site_msg=glob_url+"/get-event-notify?source="+extFlag+"&e-version="+E_VERSION,glob_year_chart=glob_url+"/smt-year-chart?source="+extFlag+"&e-version="+E_VERSION,glob_tj_url=glob_url+"/smt-tj-info?source="+extFlag+"&e-version="+E_VERSION,glob_export_permission=glob_url+"/smt-export-permission?source="+extFlag+"&e-version="+E_VERSION,glob_class_config=glob_url+"/smt-class-config?source="+extFlag+"&e-version="+E_VERSION,glob_seven_data_url=glob_url+"/smt-seven-data?source="+extFlag+"&e-version="+E_VERSION,glob_favted_store_url=glob_url+"/smt-collection-store?source="+extFlag+"&e-version="+E_VERSION,glob_remove_store_url=glob_url+"/smt-remove-store?source="+extFlag+"&e-version="+E_VERSION,glob_my_favted_url=glob_url+"/smt-my-collection?source="+extFlag+"&e-version="+E_VERSION,glob_monitor_url=glob_url+"/smt-monitor-data?source="+extFlag+"&e-version="+E_VERSION,glob_add_long_words=glob_url+"/smt-add-long-words?source="+extFlag+"&e-version="+E_VERSION,glob_get_long_words=glob_url+"/smt-get-long-words?source="+extFlag+"&e-version="+E_VERSION,glob_log_url=glob_url+"/smt-error-log?source="+extFlag+"&e-version="+E_VERSION,glob_product_carrier_url=glob_url+"/smt-product-carrier?source="+extFlag+"&e-version="+E_VERSION,glob_site_notify_url=glob_url+"/smt-notify?source="+extFlag+"&e-version="+E_VERSION,glob_upload_store_id_url=glob_url+"/smt-upload-store-id?source="+extFlag+"&e-version="+E_VERSION,glob_upload_product_id=glob_url+"/smt-upload-product-id?source="+extFlag+"&e-version="+E_VERSION,glob_get_keyword_info=glob_url+"/smt-get-keyword-info?source="+extFlag+"&e-version="+E_VERSION,glob_get_tags=glob_url+"/smt-get-tags?source="+extFlag+"&e-version="+E_VERSION,glob_remove_product=glob_url+"/smt-remove-goods?source="+extFlag+"&e-version="+E_VERSION;// local  microsoft  google
const cdn_url="https://cdn.ixspy.cn/";var glob_goods_id=0,glob_store_id=0,glob_store_info=[],glob_product_info=[],glob_img_info=[],glob_chart_info=[],glob_shopify_goods_info=[],glob_device_id="",glob_user_id="",glob_aliexpress_session="",glob_login_session="",glob_category="",globAllowLevel=["2","4","5","6","7","9","10","11","16","18","19","21","22"],tabUrl=null,tabCookies=null;import{pub_xDataLength,pub_format,ajaxFetch,pub_isEmpty,fetchLongWords,compareLevel}from"./js/moduleCom.js";
// import {jszip} from './js/jszip'
import localStorage from"./js/localstore.js";
//1. 获取cookies
function bg_getCookiesForKey(e,t){chrome.cookies.get({url:glob_url,name:e},t)}
//2. 请求商品详情数据
async function bd_requestGoodsInfo(e,t="",o=0){return!pub_isEmpty(e)&&!(!(t=await ajaxFetch(glob_goods_url+e+"&device_id="+glob_device_id+"&source_id="+t+"&user_id="+glob_user_id+"&sale="+(o=o||0),"get"))||0!=t.error.code)&&(o=t.data,t=bg_checkProductId(e),o.isIdHere=t,o)}
// 清空网站的Cookie
async function removeCookies(e){let o=e.split(".")[0];chrome.cookies.getAll({domain:"aliexpress.com"},function(e){for(var t=0;t<e.length;t++)"aep_usuc_f"==e[t].name&&chrome.cookies.remove({url:"https://"+o+e[t].domain+e[t].path,name:e[t].name},function(e){chrome.tabs.create({url:"https://www.aliexpress.com"})})})}async function getAds(){var e=new Date,e=parseInt(e.getTime()/1e3),e=cdn_url+"smt/ads.json?time="+e;return await ajaxFetch(e,"get")}
//3. 获取竞争对手的产品
async function bg_requestCompetitorGoodsInfo(e){return!pub_isEmpty(e.product_id)&&!pub_isEmpty(e.source_product_id)&&!(!(e=await ajaxFetch(glob_competitor_url+"&device_id="+glob_device_id+"&user_id="+glob_user_id,"post",e))||0!=e.error.code)&&e.data.list}
//4. 用户收藏商品id
async function bg_requestCollectionGoodsId(e,t=""){var o,a,r={msg:!1,data:void 0};return pub_isEmpty(e.productId)||(!1!==(a=await goodsId(e.productId))?r.msg=a:""==(o=await localStorage.getItem("user_id"))||null==o?r.msg="need_login":(o={user_id:o,goods_id:e.productId,tags:e.tags,flag:t},a=0,(e=await ajaxFetch(glob_favted_goods_url+"&device_id="+glob_device_id+"&user_id="+glob_user_id,"post",o))?0!=(r.data=e).error.code?1==t?r.data=e:"5018"==e.error.code?(console.log("objRes.error.code",e.error.code),r.msg="need_login"):"5024"==e.error.code?r.msg="times_exhausted":"5019"==e.error.code?r.msg="lang_no_lu":r.msg=0:(r.data=e,1==t?r.data=e:(glob_product_info.isIdHere="ok","success"==e.data.msg?r.msg="ok":"already_exist"==e.data.msg?r.msg="already":r.msg=e.data.msg),localStorage.setItem("products_id",JSON.stringify(e.data.favted_ids))):r.msg=0)),r}
//用户收藏店铺
async function bg_requestCollectionStoreId(e){var t=await localStorage.getItem("user_id");return""==t||null==t?"need_login":(t={user_id:t,id:e.store_id,tags:e.tags},e=0,(t=await ajaxFetch(glob_favted_store_url+"&device_id="+glob_device_id+"&user_id="+glob_user_id,"post",t))?0!=t.error.code?"5018"==t.error.code?"need_login":"5024"==t.error.code?"times_exhausted":0:(e="success"==t.data.msg?"ok":"already_exist"==t.data.msg?"already":t.data.msg,localStorage.setItem("store_id",JSON.stringify(t.data.favted_ids)),e):0)}
//用户取消收藏店铺
async function bg_requestRemoveStoreId(e){var t=await localStorage.getItem("user_id");return""==t||null==t?"need_login":(t={id:e.store_id},e=0,(t=await ajaxFetch(glob_remove_store_url+"&device_id="+glob_device_id+"&user_id="+glob_user_id,"post",t))?0!=t.error.code?"5018"==t.error.code?"need_login":0:(e="success"==t.data.msg?"ok":t.data.msg,localStorage.setItem("store_id",JSON.stringify(t.data.favted_ids)),e):0)}
//5.将收藏的id 存在客户端一份
async function goodsId(e){var t=await localStorage.getItem("products_id");return!pub_isEmpty(t)&&-1!==JSON.parse(t).indexOf(e+"")&&"already"}
//6.获取买家秀pic
async function bg_requestShowPic(e,t){return!pub_isEmpty(e)&&!(!(e=await ajaxFetch(glob_show_pic_url+e+"&device_id="+glob_device_id+"&user_id="+glob_user_id+"&source_id="+t,"get"))||0!=e.error.code)&&e.data}
//7.获取商品的图表数据
async function bg_requestGoodsChart(e,t){var o,a;return!(pub_isEmpty(e)||(o=await localStorage.getItem("level"),a=await localStorage.getItem("user_id"),!(e=await ajaxFetch(glob_goods_chart_url+e+"&level="+o+"&user_id="+a+"&device_id="+glob_device_id+"&user_id="+glob_user_id+"&source_id="+t,"get")))||0!=e.error.code)&&e.data}
//8.获取店铺信息
async function bg_requestStoreInfo(e,t,o="",a=""){return!pub_isEmpty(e)&&!(!(e=await ajaxFetch(glob_store_info_url+e+"&product_id="+o+"&is_index="+t+"&device_id="+glob_device_id+"&user_id="+glob_user_id+"&source_id="+a,"get"))||0!=e.error.code)&&e.data}
//9.判断传递进来的product_id / store_id 是否有变化，没有变化就将原来请求的结果数据返回
function bg_checkIsSameId(e,t){if("xxx"==e)return!0;if("request_store"==t)return 0!=glob_store_id&&e==glob_store_id&&0!=pub_xDataLength(glob_store_info)&&glob_store_info;if(0==glob_goods_id)return!1;if(e!=glob_goods_id)return glob_product_info=[],glob_img_info=[],!(glob_chart_info=[]);var o=!1;switch(t){case"request_product":0!=glob_product_info&&0<pub_xDataLength(glob_product_info)&&(o=glob_product_info);break;case"request_img":0!=glob_img_info&&0<pub_xDataLength(glob_img_info)&&(o=glob_img_info);break;case"request_chart":0!=glob_chart_info&&0<pub_xDataLength(glob_chart_info)&&(o=glob_chart_info)}return o}
//10. 异步转同步获取cookies
function bg_asyncGetCookiesForKey(t){chrome.cookies.get({url:glob_url,name:t},function(e){null==e?localStorage.setItem(t,""):localStorage.setItem(t,e.value)})}
//12. 判断产品id 是否存在客户端了

function bg_soreces_download(e){
//'_50x50.jpg_.webp'
// var jszip = new jszip()
var t=e.urls,o=e.product_name,a=typeof t;if(console.log(e,a,123),"string"==a)try{-1!==t.indexOf("definition=h265")&&(t=t.replace("definition=h265","")),chrome.downloads.download({url:t,filename:"video.mp4"},function(e){console.log(e)})}catch(e){}else if("object"==a)for(var r in t){var r=t[r].replace("_.webp",""),s=(
//  NewurlJpeg = NewurlJpeg.replace("?width=800&height=800&hash=1600","")
r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=(r=r.replace("_50x50.jpg","")).replace("50x50.jpg","")).replace("_50x50.png","")).replace("50x50.png","")).replace("_220x220.jpg","")).replace("220x220.jpg","")).replace("_220x220.png","")).replace("220x220.png","")).replace("_80x80.jpg","")).replace("80x80.jpg","")).replace("_80x80.png","")).replace("80x80.png","")).replace("_120x120.jpg","")).replace("120x120.jpg","")).replace("_120x120.png","")).replace("120x120.png","")).replace("_220x220q75.jpg","")).replace("_.avif","")).split("?")[0]).split("."),s=s[s.length-1];"avif"==s&&(s="jpg"),console.log(r,o.trim()+"/pic."+s);
// console.log( 'filename:' + product_name + '/pic.' + ext)
try{chrome.downloads.download({url:r,filename:o.trim()+"/pic."+s},function(e){})}catch(e){console.log(e)}}}
//18. background 向 content_script 通信
function backgroundSendMessageToContentScript(t,o){chrome.tabs.query({active:!0,currentWindow:!0},function(e){try{null!=e[0]&&null!=e[0].url&&-1!==e[0].url.indexOf("aliexpress")&&chrome.tabs.sendMessage(e[0].id,t,function(e){return o&&o(e),!0})}catch(e){}})}
//19. 获取版本号，判断是否要进行更新提示
async function getVersion(){var e,t=chrome.runtime.getURL("manifest.json"),t=await ajaxFetch(t,"get");return localStorage.setItem("version",t.version),"local"!=extFlag?await localStorage.getItem("version"):(e=await localStorage.getItem("version"),0!=(t=await ajaxFetch(glob_version+(e=null==e?"":e),"get"))&&0==t.error.code&&(t=t.data,localStorage.setItem("check_res",JSON.stringify(t)),e=await localStorage.getItem("version")),e)}async function getCategory(){var t=new Date,t=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate(),e=await localStorage.getItem("ixspy_category_plug_version_date");let o=await localStorage.getItem("ixspy_category_plug_version"),a=((!e||!o||e!=t)&&(o=await category_plug_config(t)),await localStorage.getItem("ixspy_category_plug"));if(a&&(a=JSON.parse(a)).version==o&&a.data||
//本地没有
await getCategoryIntoLocal(t,o),!glob_category)try{a=await localStorage.getItem("ixspy_category_plug"),a=JSON.parse(a),glob_category=a.data}catch(e){await getCategoryIntoLocal(t,o)}return glob_category}
/**
 * load category into local
 * @param {*} version 
 */async function getCategoryIntoLocal(e,t){e=cdn_url+"aliexpress/Category/extension/category_plug2.json?v="+e;glob_category=await ajaxFetch(e,"get"),localStorage.setItem("ixspy_category_plug",JSON.stringify({version:t,data:glob_category}))}
/**
 * load category_plug_config version
 * @param {*} version_date 
 * @returns 
 */async function category_plug_config(e){localStorage.setItem("ixspy_category_plug_version_date",e);let t="";e=cdn_url+"aliexpress/extention/config/category_plug_config.json?v="+e,e=await ajaxFetch(e,"get");return e&&(t=e.version),localStorage.setItem("ixspy_category_plug_version",t),t}
//21. 根据存储的内容检测是否需要提示更新
async function checkShowUpdateTip(e=""){var t=await localStorage.getItem("check_res");if(null==t||""==t)return!1;var o=(t=JSON.parse(t)).version,a=t.msg;if("ok"==a&&(localStorage.setItem("version",o),backgroundSendMessageToContentScript({type:"background",cmd:"close_update",data:t},function(e){}),e))return"no_update";if("need_update"==a){if(1==await localStorage.getItem("close_no_force_update"))return backgroundSendMessageToContentScript({type:"background",cmd:"show_update_tip",data:t},function(e){}),!1;backgroundSendMessageToContentScript({type:"background",cmd:"need_update",data:t},function(e){})}"must_update"==a&&backgroundSendMessageToContentScript({type:"background",cmd:"must_update",data:t},function(e){})}
//22.获取站内事件消息
async function getSiteEventMessage(){var e=await localStorage.getItem("user_id"),t=await localStorage.getItem("level"),o=await localStorage.getItem("msg_ids"),a=[],e=await ajaxFetch(glob_site_msg,"get",{user_id:e,level:t,msg_ids:o,site_id:100,market:{google:1,microsoft:2,local:3,360:4}[extFlag]});if(!e)return!1;if(0!=e.error.code)return!1;var r,s=e.data;if(0==s.length)return!1;for(r in null==o&&(o=""),s){var i=s[r].id+"";-1===o.indexOf(i)&&(o+=i+",",a.push(s[r]))}return localStorage.setItem("msg_ids",o),a}
//23.获取一年的图表数据
async function bd_request_year_chart(e){var t=await localStorage.getItem("level"),t={type:e.type,chart_type:e.flag,store_id:e.store_id,product_id:e.product_id,level:t,source_id:e.x_object_id},e=await localStorage.getItem("user_id"),e=(t.user_id=e,await ajaxFetch(glob_year_chart+"&device_id="+glob_device_id,"get",t));return e&&0==e.error.code?e.data:[]}
//24.统计商品数据
async function bg_requestTjProduct(e){var t=await localStorage.getItem("level"),o=await localStorage.getItem("user_id"),o=(e.user_id=o,e.level=t,await ajaxFetch(glob_tj_url+"&device_id="+glob_device_id,"post",e));return o&&0==o.error.code?o.data:[]}
//25. 生成设备id
async function createDeviceId(){var e=await localStorage.getItem("device_id");if(pub_isEmpty(e)){var s=new Date,i=s.getFullYear();let e=s.getMonth()+1,t=s.getDate(),o=s.getHours(),a=s.getMinutes(),r=s.getSeconds();String(e).length<2?e=Number("0"+e):e,String(t).length<2?t=Number("0"+t):t,String(o).length<2?o=Number("0"+o):o,String(a).length<2?a=Number("0"+a):a,String(r).length<2?r=Number("0"+r):r;s=""+i+e+t+o+a+r;glob_device_id=s+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("device_id",glob_device_id)}else glob_device_id=e}
//27
function completeRequest(e){glob_user_id=e.getResponseHeader("user_id");e.getResponseHeader("level");e=e.getResponseHeader("email");localStorage.setItem("user_id",glob_user_id),
//localStorage.setItem('level',level)
localStorage.setItem("user_email",e),backgroundSendMessageToContentScript({type:"background",cmd:"user_info_change",data:glob_user_id},function(e){})}
//28. 覆盖Cookies
function setCookies(e,t){var o=-1!==(o=t.replace("www.","")).indexOf(".ru")?".aliexpress.ru":".aliexpress.com";return chrome.cookies.set({url:"https://"+t,name:"aep_usuc_f",value:"site=glo&c_tp=USD&ups_d=1|1|1|1&ups_u_t=1647323746537&region="+e+"&b_locale=en_"+e+"&ae_u_p_s=2",domain:o,path:"/",expirationDate:1931865830},function(e){}),"ok"}
//29.判断收集权限,
async function checkCountryPrice(e){resetExportId();var t,o=e.status,e=await localStorage.getItem("export_id"),e=!1,a=await ajaxFetch(glob_export_permission,"get");
//未登录
return 0!=a&&0==a.error.code&&(
// console.log(objRes.data)
e=a.data.is_permission,localStorage.setItem("level",a.data.level),localStorage.setItem("user_email",a.data.email),localStorage.setItem("user_id",a.data.user_id)),"no_login"==e?"no_login":(null!=(a=await localStorage.getItem("collection_nums"))&&""!=a&&null!=a&&NaN!=a||(a=0),a=parseInt(a),e=await localStorage.getItem("level"),t=globAllowLevel,"add_nums"!=o||(""==a||null==a?(localStorage.setItem("collection_nums",1),{res:!0,nums:1,level:e,add_nums:1}):!(5<=a&&!compareLevel(e,t))&&(localStorage.setItem("collection_nums",a+=1),{res:!0,nums:a,level:e,add_nums:1})));
//检测操作次数,开启收集，并不是在收集的过程中
}
//30. 判断月份，月份不同的时候重置下exportid
async function resetExportId(){var e=(new Date).getMonth(),t=(e+=1,await localStorage.getItem("month"));null==t||""==t||null==t?localStorage.setItem("month",e):t!=e&&(localStorage.setItem("collection_nums",""),localStorage.setItem("month",e))}
//31.获取收集的class的信息
async function getClassConfig(e=!1){try{if("init"==e){var t=await localStorage.getItem("timer_get_config"),o=new Date,a=parseInt(o.getTime()/1e3);if(!pub_isEmpty(t)&&a-parseInt(t)<1800)return;
// console.log('设置config的时间',nowTime)
await localStorage.setItem("timer_get_config",a)}await localStorage.getItem("timer_get_config");
// console.log('获取config的时间',tt)
var r=cdn_url+"aliexpress/extention/config/aliexpress_extention_config_v2.json?v="+Math.random(),s=(await localStorage.getItem("config_version"),!1),i=!1,l=!1,c=await ajaxFetch(r,"get");
// console.log('获取config的时间',tt,objRes)
//处理结果
return 0==c||0!=c.error.code?(s=await localStorage.getItem("config_class"),i=await localStorage.getItem("conifg_tj_class"),l=await localStorage.getItem("download_class"),c={config_class:JSON.parse(s),config_tj_class:JSON.parse(i),download_class:JSON.parse(l)}):(null!=c.data.allow_level&&(globAllowLevel=c.data.allow_level),localStorage.setItem("config_version",c.data.config_version),s=c.data.data,i=c.data.tj_class,l=c.data.download_class,localStorage.setItem("config_class",JSON.stringify(s)),localStorage.setItem("conifg_tj_class",JSON.stringify(i)),localStorage.setItem("download_class",JSON.stringify(l)),e?void 0:c={config_class:s,config_tj_class:i,download_class:l})}catch(e){console.log("获取配置信息出现异常",e)}}
//32.获取七天数据
async function requestSevenData(e){
//{'init_data':objProductIds,'x_object':objProductIdsXobject}
var t=e.init_data,o=[];for(a in t)o.push(t[a]);
//判断权限
await localStorage.getItem("level");e=await localStorage.getItem("user_id");
// if(!compareLevel(userLevel,levels)){
//     return []
// }
if(0==e||null==e||""==e)return[];e=await ajaxFetch(glob_seven_data_url,"post",{id:o});if(!e||0!=e.error.code)return[];var a,r=e.data;for(a in t){var s=t[a];s!=a&&(r[a]=r[s])}return r}
//33. 检测是否已经收藏
async function checkIsFavted(e){var t=e.id,e=e.flag;if("product"==e)return goodsId(t);if("store"==e){e=await localStorage.getItem("store_id");
// console.log(strIds);
if(!pub_isEmpty(e)){var o,a=JSON.parse(e);for(o in a)if(a[o]==t)return"already"}return!1;
// if(obj.indexOf(parseInt(id)) !== -1){
//     console.log('already');
//     return 'already'
// }else{
//     console.log('null');
//     return false
// }
}}
//34. 获取收藏的内容
async function bg_getMyFavted(e){var t=await localStorage.getItem("user_id");return""==t||null==t?"need_login":(t=await ajaxFetch(glob_my_favted_url,"post",e))?5018==t.error.code?"need_login":0==t.error.code?t.data:[]:[]}
//35. 获取监控的数据
async function bg_getMonitorData(e){var t=await localStorage.getItem("user_id");return""==t||null==t?"need_login":(t=await ajaxFetch(glob_monitor_url,"post",e))?5018==t.error.code?"need_login":0==t.error.code?t.data:[]:[]}
//检测是否登陆
async function checkUserInfo(e=""){if("init"==e){var e=await localStorage.getItem("timer_get_user_info"),t=new Date,t=parseInt(t.getTime()/1e3);if(!pub_isEmpty(e)&&t-parseInt(e)<7200)return{};
// console.log('设置是否登陆的时间',nowTime)
await localStorage.setItem("timer_get_user_info",t)}await localStorage.getItem("timer_get_user_info");
// console.log('获取否登陆的时间',tt)
e={},e.version=await localStorage.getItem("version"),t=await ajaxFetch(glob_export_permission,"get");return t&&(
// console.log(objRes.data,123)
null==t.data.level?(localStorage.setItem("level",""),localStorage.setItem("user_email",""),localStorage.setItem("user_id","")):(localStorage.setItem("level",t.data.level),localStorage.setItem("user_email",t.data.email),localStorage.setItem("user_id",t.data.user_id),e=t.data)),e}
//点击插件小图标跳转到下面指定地址
//获取长尾词信息
async function getScriptLongWordsData(t){var o=t.url,a=(pub_isEmpty(o)||-1!==o.indexOf("http")||(o="https:"+o),localStorage.setItem("long_words_url",o),localStorage.setItem("long_words_params",JSON.stringify(t.params)),[]);for(let e=0;e<1;e++){var r=await fetchLongWords(o,"post",t.params);a.push(r)}return a}
//长尾词收集
// async function addLongWordsData(data){
//     var url = glob_add_long_words
//     var params = {
//         'device_id':glob_device_id,
//         'keyword_info':data.data
//     }
//     var res = await ajaxFetch(url,'post',params)
//     if(!res || res.error.code != 0){
//         return false
//     }
//     if(res.error.code == 0){
//         return true
//     }
// }
//获取搜索的长尾词
function getLongWordsData(e){var t={device_id:glob_device_id,
//'keyword_info':data.data,
page:1,size:10};return $.ajax({url:glob_get_long_words,async:!1,method:"post",data:t,success:function(e){objRes=e},error:function(e){objRes=e.responseText}}),objRes}
//记录错误日志
async function addLog(e){var t=await localStorage.getItem("add_log_time"),o=new Date,o=Date.parse(o)/1e3;if(!(null!=t&&""!=t&&null!=t&&o-parseInt(t)<=300))return localStorage.setItem("add_log_time",o),await ajaxFetch(glob_log_url,"post"),!0}
//获取更多的长尾词信息
async function getMoreLongWords(){var e=await localStorage.getItem("user_id");if(""==e||null==e)return"need_login";e=await localStorage.getItem("level");
// console.log('level',level)
if(null==e||""==e)return"need_permissions";if(-1===(e+="").indexOf(",")&&0==e)return"need_permissions";var t=await localStorage.getItem("long_words_url"),o=await localStorage.getItem("long_words_params"),o=JSON.parse(o),a=[];for(let e=0;e<10;e++){var r=await fetchLongWords(t,"post",o);a.push(r)}return a}checkUserInfo("init"),getClassConfig("init"),getVersion(),getCategory(),
// getCategoryArr()
createDeviceId(),localStorage.setItem("set_source",extFlag),localStorage.setItem("close_no_force_update",0),
//-------------测试代码
//-------------测试代码
//0. 监听其它地方向 background 发送的消息
chrome.runtime.onMessage.addListener(function(e,t,o){var a=e.type,l=e.objData,c=e.cmd;if("content_script"==a){var _=!1;if("check_login"!=c){e=bg_checkIsSameId(l.id,c);if(!1!==e&&0<e.length)return checkShowUpdateTip(),void o(e)}bg_asyncGetCookiesForKey("user_id");return async function(){switch(c){case"set_site_country":
// console.log('set_site_country:'+objData);
localStorage.setItem("site_country",l);
// // localStorage.getItem('site_country')
// objRes = await localStorage.getItem('site_country')
// console.log(objRes)
break;case"keyword_info_api":_=await getKeywordsInfo(l);break;case"upload_product_id":_=await uploadProductId(l);break;case"append_ocuntry_chose_k":_=await checkUserInfo("append_ocuntry_chose_k");break;case"upload_store_id":_=await uploadStoreId(l.data);break;case"search_by_img":_=await searchByImg(l);break;case"prdocut_carrier_api":_=await productCarrierApi(l);break;case"add_log":_=await addLog(l);break;case"more_long_words":await checkUserInfo("more_long_words"),_=await getMoreLongWords();break;case"get_long_words":_=getLongWordsData(l);break;
// case 'long_words_add':
//     objRes = await addLongWordsData(objData)
//     break;
case"long_words_url":var e=await getScriptLongWordsData(l),t=await getClassConfig();_={res:e,config_class:t};break;case"get_set_source":_=await localStorage.getItem("set_source");break;case"persion":await getSiteNotifyMessage(),await checkShowUpdateTip(),_=await checkUserInfo("persion");break;case"check_monitor":_=await bg_getMonitorData(l);break;case"get_my_favted":await getSiteNotifyMessage(),await checkShowUpdateTip(),_=await bg_getMyFavted(l);break;case"collection_store":
//收藏店铺
_=await bg_requestCollectionStoreId(l);break;case"is_favted":_=await checkIsFavted(l);break;case"isLoginrequest_seven_data_todo":await checkUserInfo("isLoginrequest_seven_data");break;case"isLoginrequest_seven_data":
// await checkUserInfo('isLoginrequest_seven_data')
var o=await localStorage.getItem("user_id"),a=globAllowLevel,r=await localStorage.getItem("level");
// console.log('user_id',userId,levels,userLevel)
_=pub_isEmpty(o)?(console.log("未登录"),!1):compareLevel(r,a)?"ok":(console.log("权限不够"),!1);break;case"request_seven_data":_=await requestSevenData(l);break;case"request_get_category":_=await getCategory();break;case"get_chose_countrys":e=await localStorage.getItem("chose_countrys");null!=e&&(_=JSON.parse(e));break;case"set_chose_countrys":localStorage.setItem("chose_countrys",JSON.stringify(l.countrys));break;case"get_class_config":_=await getClassConfig();break;case"get_show_auto":_=await localStorage.getItem("show_auto");break;case"set_show_auto":localStorage.setItem("show_auto",l.flag);break;case"check_country_price":t=await checkCountryPrice(l),e=await getClassConfig();_={check_country_price:t,config_class:e};break;case"set_smt_country":_=setCookies(l.country,l.hostname);break;case"request_get_favted_ids":_=await localStorage.getItem("products_id");break;case"request_version":_=await getVersion();break;case"request_favted_favted":_=await bg_requestCollectionGoodsId(l,1);break;case"request_tj_chart":_=await bg_requestTjProduct(l);break;case"request_product_chart":_=await bg_requestGoodsChart(l.product_id,l.x_object_id);
//glob_goods_id = objData.id
//glob_chart_info = objRes
break;case"request_year_chart":
//判断是否登陆，是否有权限
o=await localStorage.getItem("user_id"),a=globAllowLevel,r=await localStorage.getItem("level");pub_isEmpty(o)?_="no_login":compareLevel(r,a)?0==pub_isEmpty(o)&&compareLevel(r,a)&&(await getSiteNotifyMessage(),null!=(_=await bd_request_year_chart(l)).chart_type)&&(_.level=await localStorage.getItem("level")):_="no_permissions";break;case"getStoreId":_=await bd_requestGoodsInfo(l.id,s=!0),glob_goods_id=l.id,glob_product_info=_;break;case"self_check_update"://主动检查是否要更新
await getSiteNotifyMessage(),localStorage.setItem("close_no_force_update",0),await getVersion(),_=await checkShowUpdateTip(1);break;case"need_update_close"://非强制更新
localStorage.setItem("close_no_force_update",1);break;case"request_product":
//checkShowUpdateTip()
//     if(flag){
//         objRes = 'update_tip'
//         break
//     }
_=await bd_requestGoodsInfo(l.id,l.x_object_id,l.sale),glob_goods_id=l.id,glob_product_info=_;break;case"request_store":await getSiteNotifyMessage(),await checkShowUpdateTip(),_=await bg_requestStoreInfo(l.id,l.is_index,l.product_id,l.x_object_id),glob_store_id=l.id,glob_store_info=_;break;case"request_img":await getSiteNotifyMessage(),await checkShowUpdateTip(),
// if(flag){
//     objRes = 'update_tip'
//     break
// }
_=await bg_requestShowPic(l.id,l.x_object_id),glob_goods_id=l.id,glob_img_info=_;break;case"request_chart":await getSiteNotifyMessage(),await checkShowUpdateTip(),
// if(flag){
//     objRes = 'update_tip'
//     break
// }
_=await bg_requestGoodsChart(l.id,l.x_object_id),glob_goods_id=l.id,glob_chart_info=_;break;case"request_shopify_goods":_=await bg_requestCompetitorGoodsInfo(l),glob_product_info.shopify_goods_info=_;break;case"collection_goods":_=await bg_requestCollectionGoodsId(l);break;case"check_login":o=await localStorage.getItem("user_id"),t=await localStorage.getItem("user_email");console.log(o,t,132),_=!pub_isEmpty(o)&&0!=o&&"null"!=o&&!pub_isEmpty(t)&&"ok";break;case"sources_download":_=bg_soreces_download(l);break;case"create_download_button_dom":_=await getClassConfig();break;case"get_notify":_=await getSiteEventMessage();break;case"check_turn":localStorage.setItem("smt_turn",l.value);break;case"get_turn":_=await localStorage.getItem("smt_turn");break;case"set_sales_auto":localStorage.setItem("smt_sales_auto",l.value);break;case"get_sales_auto":_=await localStorage.getItem("smt_sales_auto");break;case"login_tip":_="ok";break;case"remove_cookie":_=await removeCookies(l.country);break;case"get_ads":_=await getAds();break;case"get_sycm_words_data":(_=await getSycmWordsData(l.url)).exportFileName=l.name,_.searchType=l.type,_.title=l.title;break;case"toExportExpertWordExcel":_=l;break;case"remove_shop":
//取消收藏店铺
_=await bg_requestRemoveStoreId(l);break;case"empty_func":case"createSearchImgButtonDomRuSingle":
//空方法
_="ok";break;case"glob_get_tags":_=await bg_getTags(l);break;case"remove_product":_=await bg_removeProduct(l)}var s=typeof _;if("object"==s&&null!=_)try{var i=await localStorage.getItem("show_auto");_.show_auto=i}catch(e){console.log(e)}}().then(e=>{o(_)}),!0}}),chrome.action.onClicked.addListener(function(e){chrome.tabs.create({url:glob_url})});var timerCheckMessage="";
//收集产品价格的任务
async function productCarrierApi(e){e.from="extention";var e=await ajaxFetch(glob_product_carrier_url,"post",e),t=4;return clearInterval(timerCheckMessage),timerCheckMessage=setInterval(()=>{t<1&&clearInterval(timerCheckMessage),checkUserInfo("productCarrierApi"),--t},12e4),e}
//获取站点消息 和事件消息不同
async function getSiteNotifyMessage(){var e=await localStorage.getItem("msg_id");return null!=e&&""!=e&&(e=await ajaxFetch(glob_site_notify_url,"get",{header_msg_id:e}))&&0==e.error.code?(localStorage.setItem("msg_id",0),createdNotify((e={type:"background",cmd:"site_notify",data:e.data}).data),void backgroundSendMessageToContentScript(e,function(e){})):{}}
//创建插件的消息通知
function createdNotify(e){try{for(var t in e){var o,a;isNaN(t)||(o=e[t],"en"==chrome.i18n.getMessage("lang_a")?(o.title=o.en_title,o.msg_content=o.en_msg_content,o.button_name=o.button_name_en):o.button_name=o.button_name_zh,a={type:"basic",iconUrl:"img/notify.png",title:o.title,message:o.msg_content,buttons:[{title:o.button_name,iconUrl:"img/notify.png"}],priority:2,requireInteraction:!0},chrome.notifications.create(null,a))}}catch(e){console.log("error",e)}}
//监听消息按键事件，及点击按键后移除此条消息通知
//搜图的权限
async function searchByImg(e){await localStorage.getItem("level");
// if(!compareLevel(userLevel,globAllowLevel)){
//     return 'no_permission'
// }
chrome.tabs.create({url:e.url})}
//获得关键词详情
async function getKeywordsInfo(e){return await ajaxFetch(glob_get_keyword_info,"post",e)}
//
async function uploadStoreId(e){if(null!=e.ownerMemberId&&null!=e.storeId)return e={owner_member_id:e.ownerMemberId,store_id:e.storeId,url_store_id:e.url_store_id},await ajaxFetch(glob_upload_store_id_url,"get",e)}async function uploadProductId(e){
//时间戳限制
var t=new Date,t=parseInt(t.getTime()/1e3),o=await localStorage.getItem("config_class");if(null!=(o=JSON.parse(o)).product_id_limit&&0<o.product_id_limit){if(t>parseInt(o.product_id_limit))return}else if(1664530459<t)return;return await ajaxFetch(glob_upload_product_id,"post",e)}async function getSycmWordsData(e){return await ajaxFetch(e,"get")}async function bg_getTags(e){return{...await ajaxFetch(glob_get_tags,"get"),...e}}async function bg_removeProduct(e){var t=await ajaxFetch(glob_remove_product,"post",e);return 0==t.error.code&&
//更新收藏列表
localStorage.setItem("products_id",JSON.stringify(t.data.favted_ids)),{res:t,params:e}}chrome.notifications.onButtonClicked.addListener(function(e){chrome.tabs.create({url:glob_url+"/data#/mycenter/task?tab=product_carrier_fee"}),chrome.notifications.clear(e,function(){})}),setInterval(()=>{getSiteNotifyMessage()},5e3);

