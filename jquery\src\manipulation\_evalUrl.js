define(["../ajax"],function(n){"use strict";return n._evalUrl=function(t,a,e){return n.ajax({url:t,
// Make this explicit, since user can override this through ajaxSetup (#11264)
type:"GET",dataType:"script",cache:!0,async:!1,global:!1,
// Only evaluate the response if it is successful (gh-4126)
// dataFilter is not invoked for failure responses, so using it instead
// of the default converter is kludgy but it works.
converters:{"text script":function(){}},dataFilter:function(t){n.globalEval(t,a,e)}})},n._evalUrl});