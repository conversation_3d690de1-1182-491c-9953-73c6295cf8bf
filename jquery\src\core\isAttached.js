define(["../core","../var/documentElement","../selector"],function(n,e){"use strict";var o=function(e){return n.contains(e.ownerDocument,e)},t={composed:!0};
// Support: IE 9 - 11+, Edge 12 - 18+, iOS 10.0 - 10.2 only
// Check attachment across shadow DOM boundaries when possible (gh-3504)
// Support: iOS 10.0-10.2 only
// Early iOS 10 versions support `attachShadow` but not `getRootNode`,
// leading to errors. We need to check for `getRootNode`.
return o=e.getRootNode?function(e){return n.contains(e.ownerDocument,e)||e.getRootNode(t)===e.ownerDocument}:o});