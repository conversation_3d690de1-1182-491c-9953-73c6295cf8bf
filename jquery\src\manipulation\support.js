define(["../var/document","../var/support"],function(e,t){"use strict";var n;return n=e.createDocumentFragment().appendChild(e.createElement("div")),
// Support: Android 4.0 - 4.3 only
// Check state lost if the name is set (#11217)
// Support: Windows Web Apps (WWA)
// `name` and `type` must use .setAttribute for WWA (#14901)
(e=e.createElement("input")).setAttribute("type","radio"),e.setAttribute("checked","checked"),e.setAttribute("name","t"),n.appendChild(e),
// Support: Android <=4.1 only
// Older WebKit doesn't clone checked state correctly in fragments
t.checkClone=n.cloneNode(!0).cloneNode(!0).lastChild.checked,
// Support: IE <=11 only
// Make sure textarea (and checkbox) defaultValue is properly cloned
n.innerHTML="<textarea>x</textarea>",t.noCloneChecked=!!n.cloneNode(!0).lastChild.defaultValue,
// Support: IE <=9 only
// IE <=9 replaces <option> tags with their contents when inserted outside of
// the select element.
n.innerHTML="<option></option>",t.option=!!n.lastChild,t});