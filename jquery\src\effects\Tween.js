define(["../core","../css/finalPropName","../css"],function(i,o){"use strict";function p(t,o,e,s,n){return new p.prototype.init(t,o,e,s,n)}((i.Tween=p).prototype={constructor:p,init:function(t,o,e,s,n,p){this.elem=t,this.prop=e,this.easing=n||i.easing._default,this.options=o,this.start=this.now=this.cur(),this.end=s,this.unit=p||(i.cssNumber[e]?"":"px")},cur:function(){var t=p.propHooks[this.prop];return(t&&t.get?t:p.propHooks._default).get(this)},run:function(t){var o,e=p.propHooks[this.prop];return this.options.duration?this.pos=o=i.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=o=t,this.now=(this.end-this.start)*o+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(e&&e.set?e:p.propHooks._default).set(this),this}}).init.prototype=p.prototype,
// Support: IE <=9 only
// Panic based approach to setting things on disconnected nodes
(p.propHooks={_default:{get:function(t){
// Use a property on the element directly when it is not a DOM element,
// or when there is no matching style property that exists.
return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(
// Passing an empty string as a 3rd parameter to .css will automatically
// attempt a parseFloat and fallback to a string if the parse fails.
// Simple values such as "10px" are parsed to Float;
// complex values such as "rotate(1rad)" are returned as-is.
t=i.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){
// Use step hook for back compat.
// Use cssHook if its there.
// Use .style if available and use plain properties where available.
i.fx.step[t.prop]?i.fx.step[t.prop](t):1!==t.elem.nodeType||!i.cssHooks[t.prop]&&null==t.elem.style[o(t.prop)]?t.elem[t.prop]=t.now:i.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=p.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},i.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},i.fx=p.prototype.init,
// Back compat <1.8 extension point
i.fx.step={}});