/*! jQuery v2.1.1 | (c) 2005, 2014 jQuery Foundation, Inc. | jquery.org/license */
!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)}("undefined"!=typeof window?window:this,function(h,P){function M(e,t){return t.toUpperCase()}var e=[],c=e.slice,R=e.concat,W=e.push,i=e.indexOf,n={},$=n.toString,B=n.hasOwnProperty,g={},m=h.document,t="2.1.1",w=function(e,t){return new w.fn.init(e,t)},I=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,_=/^-ms-/,z=/-([\da-z])/gi;function X(e){var t=e.length,n=w.type(e);return"function"!==n&&!w.isWindow(e)&&(!(1!==e.nodeType||!t)||"array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}w.fn=w.prototype={jquery:t,constructor:w,selector:"",length:0,toArray:function(){return c.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:c.call(this)},pushStack:function(e){e=w.merge(this.constructor(),e);return e.prevObject=this,e.context=this.context,e},each:function(e,t){return w.each(this,e,t)},map:function(n){return this.pushStack(w.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(c.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:W,sort:e.sort,splice:e.splice},w.extend=w.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},s=1,a=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[s]||{},s++),"object"==typeof o||w.isFunction(o)||(o={}),s===a&&(o=this,s--);s<a;s++)if(null!=(e=arguments[s]))for(t in e)i=o[t],n=e[t],o!==n&&(u&&n&&(w.isPlainObject(n)||(r=w.isArray(n)))?(i=r?(r=!1,i&&w.isArray(i)?i:[]):i&&w.isPlainObject(i)?i:{},o[t]=w.extend(u,i,n)):void 0!==n&&(o[t]=n));return o},w.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===w.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){return!w.isArray(e)&&0<=e-parseFloat(e)},isPlainObject:function(e){return!("object"!==w.type(e)||e.nodeType||w.isWindow(e)||e.constructor&&!B.call(e.constructor.prototype,"isPrototypeOf"))},isEmptyObject:function(e){for(var t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[$.call(e)]||"object":typeof e},globalEval:function(e){var t,n=eval;(e=w.trim(e))&&(1===e.indexOf("use strict")?((t=m.createElement("script")).text=e,m.head.appendChild(t).parentNode.removeChild(t)):n(e))},camelCase:function(e){return e.replace(_,"ms-").replace(z,M)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r,i=0,o=e.length,s=X(e);if(n){if(s)for(;i<o&&!1!==(r=t.apply(e[i],n));i++);else for(i in e)if(r=t.apply(e[i],n),!1===r)break}else if(s)for(;i<o&&!1!==(r=t.call(e[i],i,e[i]));i++);else for(i in e)if(r=t.call(e[i],i,e[i]),!1===r)break;return e},trim:function(e){return null==e?"":(e+"").replace(I,"")},makeArray:function(e,t){t=t||[];return null!=e&&(X(Object(e))?w.merge(t,"string"==typeof e?[e]:e):W.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,s=!n;i<o;i++)!t(e[i],i)!=s&&r.push(e[i]);return r},map:function(e,t,n){var r,i=0,o=e.length,s=[];if(X(e))for(;i<o;i++)null!=(r=t(e[i],i,n))&&s.push(r);else for(i in e)r=t(e[i],i,n),null!=r&&s.push(r);return R.apply([],s)},guid:1,proxy:function(e,t){var n,r;return"string"==typeof t&&(r=e[t],t=e,e=r),w.isFunction(e)?(n=c.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(c.call(arguments)))}).guid=e.guid=e.guid||w.guid++,r):void 0},now:Date.now,support:g}),w.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var e=function(P){function f(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(65536+r):String.fromCharCode(r>>10|55296,1023&r|56320)}var e,p,b,o,M,d,R,W,w,l,c,h,T,t,g,m,r,i,v,x="sizzle"+-new Date,y=P.document,C=0,$=0,B=le(),I=le(),_=le(),z=function(e,t){return e===t&&(c=!0),0},n="undefined",X={}.hasOwnProperty,s=[],U=s.pop,V=s.push,N=s.push,Y=s.slice,k=s.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},G="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",a="[\\x20\\t\\r\\n\\f]",u="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",Q=u.replace("w","w#"),J="\\["+a+"*("+u+")(?:"+a+"*([*^$|!~]?=)"+a+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+Q+"))|)"+a+"*\\]",K=":("+u+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+J+")*)|.*)\\)|)",E=new RegExp("^"+a+"+|((?:^|[^\\\\])(?:\\\\.)*)"+a+"+$","g"),Z=new RegExp("^"+a+"*,"+a+"*"),ee=new RegExp("^"+a+"*([>+~]|"+a+")"+a+"*"),te=new RegExp("="+a+"*([^\\]'\"]*?)"+a+"*\\]","g"),ne=new RegExp(K),re=new RegExp("^"+Q+"$"),S={ID:new RegExp("^#("+u+")"),CLASS:new RegExp("^\\.("+u+")"),TAG:new RegExp("^("+u.replace("w","w*")+")"),ATTR:new RegExp("^"+J),PSEUDO:new RegExp("^"+K),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+a+"*(even|odd|(([+-]|)(\\d*)n|)"+a+"*(?:([+-]|)"+a+"*(\\d+)|))"+a+"*\\)|)","i"),bool:new RegExp("^(?:"+G+")$","i"),needsContext:new RegExp("^"+a+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+a+"*((?:-\\d)?\\d*)"+a+"*\\)|)(?=[^-]|$)","i")},ie=/^(?:input|select|textarea|button)$/i,oe=/^h\d$/i,j=/^[^{]+\{\s*\[native \w/,se=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ae=/[+~]/,ue=/'|\\/g,D=new RegExp("\\\\([\\da-f]{1,6}"+a+"?|("+a+")|.)","ig");try{N.apply(s=Y.call(y.childNodes),y.childNodes),s[y.childNodes.length].nodeType}catch(e){N={apply:s.length?function(e,t){V.apply(e,Y.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function A(e,t,n,r){var i,o,s,a,u,l,c;if((t?t.ownerDocument||t:y)!==T&&h(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(i=(t=t||T).nodeType)&&9!==i)return[];if(g&&!r){if(l=se.exec(e))if(c=l[1]){if(9===i){if(!(a=t.getElementById(c))||!a.parentNode)return n;if(a.id===c)return n.push(a),n}else if(t.ownerDocument&&(a=t.ownerDocument.getElementById(c))&&v(t,a)&&a.id===c)return n.push(a),n}else{if(l[2])return N.apply(n,t.getElementsByTagName(e)),n;if((c=l[3])&&p.getElementsByClassName&&t.getElementsByClassName)return N.apply(n,t.getElementsByClassName(c)),n}if(p.qsa&&(!m||!m.test(e))){if(u=a=x,l=t,c=9===i&&e,1===i&&"object"!==t.nodeName.toLowerCase()){for(s=d(e),(a=t.getAttribute("id"))?u=a.replace(ue,"\\$&"):t.setAttribute("id",u),u="[id='"+u+"'] ",o=s.length;o--;)s[o]=u+O(s[o]);l=ae.test(e)&&pe(t.parentNode)||t,c=s.join(",")}if(c)try{return N.apply(n,l.querySelectorAll(c)),n}catch(e){}finally{a||t.removeAttribute("id")}}}return W(e.replace(E,"$1"),t,n,r)}function le(){var n=[];function r(e,t){return n.push(e+" ")>b.cacheLength&&delete r[n.shift()],r[e+" "]=t}return r}function L(e){return e[x]=!0,e}function q(e){var t=T.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function ce(e,t){for(var n=e.split("|"),r=e.length;r--;)b.attrHandle[n[r]]=t}function fe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function H(s){return L(function(o){return o=+o,L(function(e,t){for(var n,r=s([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function pe(e){return e&&typeof e.getElementsByTagName!==n&&e}for(e in p=A.support={},M=A.isXML=function(e){e=e&&(e.ownerDocument||e).documentElement;return!!e&&"HTML"!==e.nodeName},h=A.setDocument=function(e){var u=e?e.ownerDocument||e:y,e=u.defaultView;return u!==T&&9===u.nodeType&&u.documentElement?(t=(T=u).documentElement,g=!M(u),e&&e!==e.top&&(e.addEventListener?e.addEventListener("unload",function(){h()},!1):e.attachEvent&&e.attachEvent("onunload",function(){h()})),p.attributes=q(function(e){return e.className="i",!e.getAttribute("className")}),p.getElementsByTagName=q(function(e){return e.appendChild(u.createComment("")),!e.getElementsByTagName("*").length}),p.getElementsByClassName=j.test(u.getElementsByClassName)&&q(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),p.getById=q(function(e){return t.appendChild(e).id=x,!u.getElementsByName||!u.getElementsByName(x).length}),p.getById?(b.find.ID=function(e,t){if(typeof t.getElementById!==n&&g)return(t=t.getElementById(e))&&t.parentNode?[t]:[]},b.filter.ID=function(e){var t=e.replace(D,f);return function(e){return e.getAttribute("id")===t}}):(delete b.find.ID,b.filter.ID=function(e){var t=e.replace(D,f);return function(e){e=typeof e.getAttributeNode!==n&&e.getAttributeNode("id");return e&&e.value===t}}),b.find.TAG=p.getElementsByTagName?function(e,t){return typeof t.getElementsByTagName!==n?t.getElementsByTagName(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},b.find.CLASS=p.getElementsByClassName&&function(e,t){return typeof t.getElementsByClassName!==n&&g?t.getElementsByClassName(e):void 0},r=[],m=[],(p.qsa=j.test(u.querySelectorAll))&&(q(function(e){e.innerHTML="<select msallowclip=''><option selected=''></option></select>",e.querySelectorAll("[msallowclip^='']").length&&m.push("[*^$]="+a+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+a+"*(?:value|"+G+")"),e.querySelectorAll(":checked").length||m.push(":checked")}),q(function(e){var t=u.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+a+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(p.matchesSelector=j.test(i=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.msMatchesSelector))&&q(function(e){p.disconnectedMatch=i.call(e,"div"),i.call(e,"[s!='']:x"),r.push("!=",K)}),m=m.length&&new RegExp(m.join("|")),r=r.length&&new RegExp(r.join("|")),e=j.test(t.compareDocumentPosition),v=e||j.test(t.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},z=e?function(e,t){var n;return e===t?(c=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!p.sortDetached&&t.compareDocumentPosition(e)===n?e===u||e.ownerDocument===y&&v(y,e)?-1:t===u||t.ownerDocument===y&&v(y,t)?1:l?k.call(l,e)-k.call(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!i||!o)return e===u?-1:t===u?1:i?-1:o?1:l?k.call(l,e)-k.call(l,t):0;if(i===o)return fe(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?fe(s[r],a[r]):s[r]===y?-1:a[r]===y?1:0},u):T},A.matches=function(e,t){return A(e,null,null,t)},A.matchesSelector=function(e,t){if((e.ownerDocument||e)!==T&&h(e),t=t.replace(te,"='$1']"),!(!p.matchesSelector||!g||r&&r.test(t)||m&&m.test(t)))try{var n=i.call(e,t);if(n||p.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return 0<A(t,T,null,[e]).length},A.contains=function(e,t){return(e.ownerDocument||e)!==T&&h(e),v(e,t)},A.attr=function(e,t){(e.ownerDocument||e)!==T&&h(e);var n=b.attrHandle[t.toLowerCase()],n=n&&X.call(b.attrHandle,t.toLowerCase())?n(e,t,!g):void 0;return void 0!==n?n:p.attributes||!g?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},A.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},A.uniqueSort=function(e){var t,n=[],r=0,i=0;if(c=!p.detectDuplicates,l=!p.sortStable&&e.slice(0),e.sort(z),c){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return l=null,e},o=A.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=o(t);return n},(b=A.selectors={cacheLength:50,createPseudo:L,match:S,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(D,f),e[3]=(e[3]||e[4]||e[5]||"").replace(D,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||A.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&A.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return S.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&ne.test(n)&&(t=(t=d(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(D,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=B[e+" "];return t||(t=new RegExp("(^|"+a+")"+e+"("+a+"|$)"))&&B(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==n&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=A.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(d,e,t,h,g){var m="nth"!==d.slice(0,3),v="last"!==d.slice(-4),y="of-type"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,s,a,u,l=m!=v?"nextSibling":"previousSibling",c=e.parentNode,f=y&&e.nodeName.toLowerCase(),p=!n&&!y;if(c){if(m){for(;l;){for(o=e;o=o[l];)if(y?o.nodeName.toLowerCase()===f:1===o.nodeType)return!1;u=l="only"===d&&!u&&"nextSibling"}return!0}if(u=[v?c.firstChild:c.lastChild],v&&p){for(a=(r=(i=c[x]||(c[x]={}))[d]||[])[0]===C&&r[1],s=r[0]===C&&r[2],o=a&&c.childNodes[a];o=++a&&o&&o[l]||(s=a=0,u.pop());)if(1===o.nodeType&&++s&&o===e){i[d]=[C,a,s];break}}else if(p&&(r=(e[x]||(e[x]={}))[d])&&r[0]===C)s=r[1];else for(;(o=++a&&o&&o[l]||(s=a=0,u.pop()))&&((y?o.nodeName.toLowerCase()!==f:1!==o.nodeType)||!++s||(p&&((o[x]||(o[x]={}))[d]=[C,s]),o!==e)););return(s-=g)===h||s%h==0&&0<=s/h}}},PSEUDO:function(e,o){var t,s=b.pseudos[e]||b.setFilters[e.toLowerCase()]||A.error("unsupported pseudo: "+e);return s[x]?s(o):1<s.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?L(function(e,t){for(var n,r=s(e,o),i=r.length;i--;)e[n=k.call(e,r[i])]=!(t[n]=r[i])}):function(e){return s(e,0,t)}):s}},pseudos:{not:L(function(e){var r=[],i=[],a=R(e.replace(E,"$1"));return a[x]?L(function(e,t,n,r){for(var i,o=a(e,null,r,[]),s=e.length;s--;)(i=o[s])&&(e[s]=!(t[s]=i))}):function(e,t,n){return r[0]=e,a(r,null,n,i),!i.pop()}}),has:L(function(t){return function(e){return 0<A(t,e).length}}),contains:L(function(t){return function(e){return-1<(e.textContent||e.innerText||o(e)).indexOf(t)}}),lang:L(function(n){return re.test(n||"")||A.error("unsupported lang: "+n),n=n.replace(D,f).toLowerCase(),function(e){var t;do{if(t=g?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=P.location&&P.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===t},focus:function(e){return e===T.activeElement&&(!T.hasFocus||T.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return oe.test(e.nodeName)},input:function(e){return ie.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:H(function(){return[0]}),last:H(function(e,t){return[t-1]}),eq:H(function(e,t,n){return[n<0?n+t:n]}),even:H(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:H(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:H(function(e,t,n){for(var r=n<0?n+t:n;0<=--r;)e.push(r);return e}),gt:H(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function de(){}function O(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function he(s,e,t){var a=e.dir,u=t&&"parentNode"===a,l=$++;return e.first?function(e,t,n){for(;e=e[a];)if(1===e.nodeType||u)return s(e,t,n)}:function(e,t,n){var r,i,o=[C,l];if(n){for(;e=e[a];)if((1===e.nodeType||u)&&s(e,t,n))return!0}else for(;e=e[a];)if(1===e.nodeType||u){if((r=(i=e[x]||(e[x]={}))[a])&&r[0]===C&&r[1]===l)return o[2]=r[2];if((i[a]=o)[2]=s(e,t,n))return!0}}}function ge(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function F(e,t,n,r,i){for(var o,s=[],a=0,u=e.length,l=null!=t;a<u;a++)(o=e[a])&&(!n||n(o,r,i))&&(s.push(o),l)&&t.push(a);return s}function me(d,h,g,m,v,e){return m&&!m[x]&&(m=me(m)),v&&!v[x]&&(v=me(v,e)),L(function(e,t,n,r){var i,o,s,a=[],u=[],l=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)A(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),f=!d||!e&&h?c:F(c,a,d,n,r),p=g?v||(e?d:l||m)?[]:t:f;if(g&&g(f,p,n,r),m)for(i=F(p,u),m(i,[],n,r),o=i.length;o--;)(s=i[o])&&(p[u[o]]=!(f[u[o]]=s));if(e){if(v||d){if(v){for(i=[],o=p.length;o--;)(s=p[o])&&i.push(f[o]=s);v(null,p=[],i,r)}for(o=p.length;o--;)(s=p[o])&&-1<(i=v?k.call(e,s):a[o])&&(e[i]=!(t[i]=s))}}else p=F(p===t?p.splice(l,p.length):p),v?v(null,t,p,r):N.apply(t,p)})}function ve(m,v){function e(e,t,n,r,i){var o,s,a,u=0,l="0",c=e&&[],f=[],p=w,d=e||x&&b.find.TAG("*",i),h=C+=null==p?1:Math.random()||.1,g=d.length;for(i&&(w=t!==T&&t);l!==g&&null!=(o=d[l]);l++){if(x&&o){for(s=0;a=m[s++];)if(a(o,t,n)){r.push(o);break}i&&(C=h)}y&&((o=!a&&o)&&u--,e)&&c.push(o)}if(u+=l,y&&l!==u){for(s=0;a=v[s++];)a(c,f,t,n);if(e){if(0<u)for(;l--;)c[l]||f[l]||(f[l]=U.call(r));f=F(f)}N.apply(r,f),i&&!e&&0<f.length&&1<u+v.length&&A.uniqueSort(r)}return i&&(C=h,w=p),c}var y=0<v.length,x=0<m.length;return y?L(e):e}return de.prototype=b.filters=b.pseudos,b.setFilters=new de,d=A.tokenize=function(e,t){var n,r,i,o,s,a,u,l=I[e+" "];if(l)return t?0:l.slice(0);for(s=e,a=[],u=b.preFilter;s;){for(o in n&&!(r=Z.exec(s))||(r&&(s=s.slice(r[0].length)||s),a.push(i=[])),n=!1,(r=ee.exec(s))&&(n=r.shift(),i.push({value:n,type:r[0].replace(E," ")}),s=s.slice(n.length)),b.filter)!(r=S[o].exec(s))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),s=s.slice(n.length));if(!n)break}return t?s.length:s?A.error(e):I(e,a).slice(0)},R=A.compile=function(e,t){var n,r=[],i=[],o=_[e+" "];if(!o){for(n=(t=t||d(e)).length;n--;)((o=function e(t){for(var r,n,i,o=t.length,s=b.relative[t[0].type],a=s||b.relative[" "],u=s?1:0,l=he(function(e){return e===r},a,!0),c=he(function(e){return-1<k.call(r,e)},a,!0),f=[function(e,t,n){return!s&&(n||t!==w)||((r=t).nodeType?l:c)(e,t,n)}];u<o;u++)if(n=b.relative[t[u].type])f=[he(ge(f),n)];else{if((n=b.filter[t[u].type].apply(null,t[u].matches))[x]){for(i=++u;i<o&&!b.relative[t[i].type];i++);return me(1<u&&ge(f),1<u&&O(t.slice(0,u-1).concat({value:" "===t[u-2].type?"*":""})).replace(E,"$1"),n,u<i&&e(t.slice(u,i)),i<o&&e(t=t.slice(i)),i<o&&O(t))}f.push(n)}return ge(f)}(t[n]))[x]?r:i).push(o);(o=_(e,ve(i,r))).selector=e}return o},W=A.select=function(e,t,n,r){var i,o,s,a,u,l="function"==typeof e&&e,c=!r&&d(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(s=o[0]).type&&p.getById&&9===t.nodeType&&g&&b.relative[o[1].type]){if(!(t=(b.find.ID(s.matches[0].replace(D,f),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=S.needsContext.test(e)?0:o.length;i--&&(s=o[i],!b.relative[a=s.type]);)if((u=b.find[a])&&(r=u(s.matches[0].replace(D,f),ae.test(o[0].type)&&pe(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&O(o))break;return N.apply(n,r),n}}return(l||R(e,c))(r,t,!g,n,ae.test(e)&&pe(t.parentNode)||t),n},p.sortStable=x.split("").sort(z).join("")===x,p.detectDuplicates=!!c,h(),p.sortDetached=q(function(e){return 1&e.compareDocumentPosition(T.createElement("div"))}),q(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||ce("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),p.attributes&&q(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||ce("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),q(function(e){return null==e.getAttribute("disabled")})||ce(G,function(e,t,n){return n?void 0:!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),A}(h),U=(w.find=e,w.expr=e.selectors,w.expr[":"]=w.expr.pseudos,w.unique=e.uniqueSort,w.text=e.getText,w.isXMLDoc=e.isXML,w.contains=e.contains,w.expr.match.needsContext),V=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,Y=/^.[^:#\[\.,]*$/;function G(e,n,r){if(w.isFunction(n))return w.grep(e,function(e,t){return!!n.call(e,t,e)!==r});if(n.nodeType)return w.grep(e,function(e){return e===n!==r});if("string"==typeof n){if(Y.test(n))return w.filter(n,e,r);n=w.filter(n,e)}return w.grep(e,function(e){return 0<=i.call(n,e)!==r})}w.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?w.find.matchesSelector(r,e)?[r]:[]:w.find.matches(e,w.grep(t,function(e){return 1===e.nodeType}))},w.fn.extend({find:function(e){var t,n=this.length,r=[],i=this;if("string"!=typeof e)return this.pushStack(w(e).filter(function(){for(t=0;t<n;t++)if(w.contains(i[t],this))return!0}));for(t=0;t<n;t++)w.find(e,i[t],r);return(r=this.pushStack(1<n?w.unique(r):r)).selector=this.selector?this.selector+" "+e:e,r},filter:function(e){return this.pushStack(G(this,e||[],!1))},not:function(e){return this.pushStack(G(this,e||[],!0))},is:function(e){return!!G(this,"string"==typeof e&&U.test(e)?w(e):e||[],!1).length}});var o,Q=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,J=((w.fn.init=function(e,t){var n,r;if(e){if("string"!=typeof e)return e.nodeType?(this.context=this[0]=e,this.length=1,this):w.isFunction(e)?void 0!==o.ready?o.ready(e):e(w):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),w.makeArray(e,this));if(!(n="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:Q.exec(e))||!n[1]&&t)return(!t||t.jquery?t||o:this.constructor(t)).find(e);if(n[1]){if(t=t instanceof w?t[0]:t,w.merge(this,w.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:m,!0)),V.test(n[1])&&w.isPlainObject(t))for(n in t)w.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n])}else(r=m.getElementById(n[2]))&&r.parentNode&&(this.length=1,this[0]=r),this.context=m,this.selector=e}return this}).prototype=w.fn,o=w(m),/^(?:parents|prev(?:Until|All))/),K={children:!0,contents:!0,next:!0,prev:!0};function Z(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}w.extend({dir:function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&w(e).is(n))break;r.push(e)}return r},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),w.fn.extend({has:function(e){var t=w(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(w.contains(this,t[e]))return!0})},closest:function(e,t){for(var n,r=0,i=this.length,o=[],s=U.test(e)||"string"!=typeof e?w(e,t||this.context):0;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&w.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?w.unique(o):o)},index:function(e){return e?"string"==typeof e?i.call(w(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(w.unique(w.merge(this.get(),w(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),w.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return w.dir(e,"parentNode")},parentsUntil:function(e,t,n){return w.dir(e,"parentNode",n)},next:function(e){return Z(e,"nextSibling")},prev:function(e){return Z(e,"previousSibling")},nextAll:function(e){return w.dir(e,"nextSibling")},prevAll:function(e){return w.dir(e,"previousSibling")},nextUntil:function(e,t,n){return w.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return w.dir(e,"previousSibling",n)},siblings:function(e){return w.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return w.sibling(e.firstChild)},contents:function(e){return e.contentDocument||w.merge([],e.childNodes)}},function(r,i){w.fn[r]=function(e,t){var n=w.map(this,i,e);return(t="Until"!==r.slice(-5)?e:t)&&"string"==typeof t&&(n=w.filter(t,n)),1<this.length&&(K[r]||w.unique(n),J.test(r))&&n.reverse(),this.pushStack(n)}});var r,T=/\S+/g,ee={};function s(){m.removeEventListener("DOMContentLoaded",s,!1),h.removeEventListener("load",s,!1),w.ready()}w.Callbacks=function(i){var e,n;i="string"==typeof i?ee[i]||(n=ee[e=i]={},w.each(e.match(T)||[],function(e,t){n[t]=!0}),n):w.extend({},i);function r(e){for(t=i.memory&&e,o=!0,l=a||0,a=0,u=c.length,s=!0;c&&l<u;l++)if(!1===c[l].apply(e[0],e[1])&&i.stopOnFalse){t=!1;break}s=!1,c&&(f?f.length&&r(f.shift()):t?c=[]:p.disable())}var t,o,s,a,u,l,c=[],f=!i.once&&[],p={add:function(){var e;return c&&(e=c.length,function r(e){w.each(e,function(e,t){var n=w.type(t);"function"===n?i.unique&&p.has(t)||c.push(t):t&&t.length&&"string"!==n&&r(t)})}(arguments),s?u=c.length:t&&(a=e,r(t))),this},remove:function(){return c&&w.each(arguments,function(e,t){for(var n;-1<(n=w.inArray(t,c,n));)c.splice(n,1),s&&(n<=u&&u--,n<=l)&&l--}),this},has:function(e){return e?-1<w.inArray(e,c):!(!c||!c.length)},empty:function(){return c=[],u=0,this},disable:function(){return c=f=t=void 0,this},disabled:function(){return!c},lock:function(){return f=void 0,t||p.disable(),this},locked:function(){return!f},fireWith:function(e,t){return!c||o&&!f||(t=[e,(t=t||[]).slice?t.slice():t],s?f.push(t):r(t)),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!o}};return p},w.extend({Deferred:function(e){var o=[["resolve","done",w.Callbacks("once memory"),"resolved"],["reject","fail",w.Callbacks("once memory"),"rejected"],["notify","progress",w.Callbacks("memory")]],i="pending",s={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},then:function(){var i=arguments;return w.Deferred(function(r){w.each(o,function(e,t){var n=w.isFunction(i[e])&&i[e];a[t[1]](function(){var e=n&&n.apply(this,arguments);e&&w.isFunction(e.promise)?e.promise().done(r.resolve).fail(r.reject).progress(r.notify):r[t[0]+"With"](this===s?r.promise():this,n?[e]:arguments)})}),i=null}).promise()},promise:function(e){return null!=e?w.extend(e,s):s}},a={};return s.pipe=s.then,w.each(o,function(e,t){var n=t[2],r=t[3];s[t[1]]=n.add,r&&n.add(function(){i=r},o[1^e][2].disable,o[2][2].lock),a[t[0]]=function(){return a[t[0]+"With"](this===a?s:this,arguments),this},a[t[0]+"With"]=n.fireWith}),s.promise(a),e&&e.call(a,a),a},when:function(e){function t(t,n,r){return function(e){n[t]=this,r[t]=1<arguments.length?c.call(arguments):e,r===i?l.notifyWith(n,r):--u||l.resolveWith(n,r)}}var i,n,r,o=0,s=c.call(arguments),a=s.length,u=1!==a||e&&w.isFunction(e.promise)?a:0,l=1===u?e:w.Deferred();if(1<a)for(i=new Array(a),n=new Array(a),r=new Array(a);o<a;o++)s[o]&&w.isFunction(s[o].promise)?s[o].promise().done(t(o,r,s)).fail(l.reject).progress(t(o,n,i)):--u;return u||l.resolveWith(r,s),l.promise()}}),w.fn.ready=function(e){return w.ready.promise().done(e),this},w.extend({isReady:!1,readyWait:1,holdReady:function(e){e?w.readyWait++:w.ready(!0)},ready:function(e){(!0===e?--w.readyWait:w.isReady)||(w.isReady=!0)!==e&&0<--w.readyWait||(r.resolveWith(m,[w]),w.fn.triggerHandler&&(w(m).triggerHandler("ready"),w(m).off("ready")))}}),w.ready.promise=function(e){return r||(r=w.Deferred(),"complete"===m.readyState?setTimeout(w.ready):(m.addEventListener("DOMContentLoaded",s,!1),h.addEventListener("load",s,!1))),r.promise(e)},w.ready.promise();var a=w.access=function(e,t,n,r,i,o,s){var a=0,u=e.length,l=null==n;if("object"===w.type(n))for(a in i=!0,n)w.access(e,t,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,w.isFunction(r)||(s=!0),t=l?s?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(w(e),n)}):t))for(;a<u;a++)t(e[a],n,s?r:r.call(e[a],a,t(e[a],n)));return i?e:l?t.call(e):u?t(e[0],n):o};function u(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=w.expando+Math.random()}w.acceptData=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType},u.uid=1,u.accepts=w.acceptData,u.prototype={key:function(t){if(!u.accepts(t))return 0;var n={},r=t[this.expando];if(!r){r=u.uid++;try{n[this.expando]={value:r},Object.defineProperties(t,n)}catch(e){n[this.expando]=r,w.extend(t,n)}}return this.cache[r]||(this.cache[r]={}),r},set:function(e,t,n){var r,e=this.key(e),i=this.cache[e];if("string"==typeof t)i[t]=n;else if(w.isEmptyObject(i))w.extend(this.cache[e],t);else for(r in t)i[r]=t[r];return i},get:function(e,t){e=this.cache[this.key(e)];return void 0===t?e:e[t]},access:function(e,t,n){var r;return void 0===t||t&&"string"==typeof t&&void 0===n?void 0!==(r=this.get(e,t))?r:this.get(e,w.camelCase(t)):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r,e=this.key(e),i=this.cache[e];if(void 0===t)this.cache[e]={};else{n=(r=w.isArray(t)?t.concat(t.map(w.camelCase)):(e=w.camelCase(t),t in i?[t,e]:(r=e)in i?[r]:r.match(T)||[])).length;for(;n--;)delete i[r[n]]}},hasData:function(e){return!w.isEmptyObject(this.cache[e[this.expando]]||{})},discard:function(e){e[this.expando]&&delete this.cache[e[this.expando]]}};var v=new u,l=new u,te=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ne=/([A-Z])/g;function re(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(ne,"-$1").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:te.test(n)?w.parseJSON(n):n)}catch(e){}l.set(e,t,n)}else n=void 0;return n}w.extend({hasData:function(e){return l.hasData(e)||v.hasData(e)},data:function(e,t,n){return l.access(e,t,n)},removeData:function(e,t){l.remove(e,t)},_data:function(e,t,n){return v.access(e,t,n)},_removeData:function(e,t){v.remove(e,t)}}),w.fn.extend({data:function(r,e){var t,n,i,o=this[0],s=o&&o.attributes;if(void 0!==r)return"object"==typeof r?this.each(function(){l.set(this,r)}):a(this,function(t){var e,n=w.camelCase(r);if(o&&void 0===t)return void 0!==(e=l.get(o,r))||void 0!==(e=l.get(o,n))||void 0!==(e=re(o,n,void 0))?e:void 0;this.each(function(){var e=l.get(this,n);l.set(this,n,t),-1!==r.indexOf("-")&&void 0!==e&&l.set(this,r,t)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=l.get(o),1===o.nodeType)&&!v.get(o,"hasDataAttrs")){for(t=s.length;t--;)s[t]&&0===(n=s[t].name).indexOf("data-")&&(n=w.camelCase(n.slice(5)),re(o,n,i[n]));v.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){l.remove(this,e)})}}),w.extend({queue:function(e,t,n){var r;return e?(r=v.get(e,t=(t||"fx")+"queue"),n&&(!r||w.isArray(n)?r=v.access(e,t,w.makeArray(n)):r.push(n)),r||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=w.queue(e,t),r=n.length,i=n.shift(),o=w._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){w.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v.get(e,n)||v.access(e,n,{empty:w.Callbacks("once memory").add(function(){v.remove(e,[t+"queue",n])})})}}),w.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?w.queue(this[0],t):void 0===n?this:this.each(function(){var e=w.queue(this,t,n);w._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&w.dequeue(this,t)})},dequeue:function(e){return this.each(function(){w.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(s,[s])}var r,i=1,o=w.Deferred(),s=this,a=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(r=v.get(s[a],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});function y(e,t){return"none"===w.css(e=t||e,"display")||!w.contains(e.ownerDocument,e)}var t=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,f=["Top","Right","Bottom","Left"],ie=/^(?:checkbox|radio)$/i,x=(e=m.createDocumentFragment().appendChild(m.createElement("div")),(A=m.createElement("input")).setAttribute("type","radio"),A.setAttribute("checked","checked"),A.setAttribute("name","t"),e.appendChild(A),g.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,e.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,"undefined"),oe=(g.focusinBubbles="onfocusin"in h,/^key/),se=/^(?:mouse|pointer|contextmenu)|click/,ae=/^(?:focusinfocus|focusoutblur)$/,ue=/^([^.]*)(?:\.(.+)|)$/;function p(){return!0}function d(){return!1}function le(){try{return m.activeElement}catch(e){}}w.event={global:{},add:function(t,e,n,r,i){var o,s,a,u,l,c,f,p,d,h=v.get(t);if(h)for(n.handler&&(n=(o=n).handler,i=o.selector),n.guid||(n.guid=w.guid++),a=(a=h.events)||(h.events={}),s=(s=h.handle)||(h.handle=function(e){return typeof w!=x&&w.event.triggered!==e.type?w.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(T)||[""]).length;u--;)f=d=(p=ue.exec(e[u])||[])[1],p=(p[2]||"").split(".").sort(),f&&(l=w.event.special[f]||{},f=(i?l.delegateType:l.bindType)||f,l=w.event.special[f]||{},d=w.extend({type:f,origType:d,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&w.expr.match.needsContext.test(i),namespace:p.join(".")},o),(c=a[f])||((c=a[f]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(t,r,p,s))||t.addEventListener&&t.addEventListener(f,s,!1),l.add&&(l.add.call(t,d),d.handler.guid||(d.handler.guid=n.guid)),i?c.splice(c.delegateCount++,0,d):c.push(d),w.event.global[f]=!0)},remove:function(e,t,n,r,i){var o,s,a,u,l,c,f,p,d,h,g,m=v.hasData(e)&&v.get(e);if(m&&(u=m.events)){for(l=(t=(t||"").match(T)||[""]).length;l--;)if(d=g=(a=ue.exec(t[l])||[])[1],h=(a[2]||"").split(".").sort(),d){for(f=w.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=p.length;o--;)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));s&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,m.handle)||w.removeEvent(e,d,m.handle),delete u[d])}else for(d in u)w.event.remove(e,d+t[l],n,r,!0);w.isEmptyObject(u)&&(delete m.handle,v.remove(e,"events"))}},trigger:function(e,t,n,r){var i,o,s,a,u,l,c=[n||m],f=B.call(e,"type")?e.type:e,p=B.call(e,"namespace")?e.namespace.split("."):[],d=o=n=n||m;if(3!==n.nodeType&&8!==n.nodeType&&!ae.test(f+w.event.triggered)&&(0<=f.indexOf(".")&&(f=(p=f.split(".")).shift(),p.sort()),a=f.indexOf(":")<0&&"on"+f,(e=e[w.expando]?e:new w.Event(f,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:w.makeArray(t,[e]),l=w.event.special[f]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){if(!r&&!l.noBubble&&!w.isWindow(n)){for(s=l.delegateType||f,ae.test(s+f)||(d=d.parentNode);d;d=d.parentNode)c.push(d),o=d;o===(n.ownerDocument||m)&&c.push(o.defaultView||o.parentWindow||h)}for(i=0;(d=c[i++])&&!e.isPropagationStopped();)e.type=1<i?s:l.bindType||f,(u=(v.get(d,"events")||{})[e.type]&&v.get(d,"handle"))&&u.apply(d,t),(u=a&&d[a])&&u.apply&&w.acceptData(d)&&(e.result=u.apply(d,t),!1===e.result)&&e.preventDefault();return e.type=f,r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(c.pop(),t)||!w.acceptData(n)||a&&w.isFunction(n[f])&&!w.isWindow(n)&&((o=n[a])&&(n[a]=null),n[w.event.triggered=f](),w.event.triggered=void 0,o)&&(n[a]=o),e.result}},dispatch:function(e){e=w.event.fix(e);var t,n,r,i,o,s=c.call(arguments),a=(v.get(this,"events")||{})[e.type]||[],u=w.event.special[e.type]||{};if((s[0]=e).delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,e)){for(o=w.event.handlers.call(this,e,a),t=0;(r=o[t++])&&!e.isPropagationStopped();)for(e.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!e.isImmediatePropagationStopped();)e.namespace_re&&!e.namespace_re.test(i.namespace)||(e.handleObj=i,e.data=i.data,void 0===(i=((w.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s)))||!1!==(e.result=i)||(e.preventDefault(),e.stopPropagation());return u.postDispatch&&u.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,o,s=[],a=t.delegateCount,u=e.target;if(a&&u.nodeType&&(!e.button||"click"!==e.type))for(;u!==this;u=u.parentNode||this)if(!0!==u.disabled||"click"!==e.type){for(r=[],n=0;n<a;n++)void 0===r[i=(o=t[n]).selector+" "]&&(r[i]=o.needsContext?0<=w(i,this).index(u):w.find(i,this,null,[u]).length),r[i]&&r.push(o);r.length&&s.push({elem:u,handlers:r})}return a<t.length&&s.push({elem:this,handlers:t.slice(a)}),s},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i=t.button;return null==e.pageX&&null!=t.clientX&&(n=(r=e.target.ownerDocument||m).documentElement,r=r.body,e.pageX=t.clientX+(n&&n.scrollLeft||r&&r.scrollLeft||0)-(n&&n.clientLeft||r&&r.clientLeft||0),e.pageY=t.clientY+(n&&n.scrollTop||r&&r.scrollTop||0)-(n&&n.clientTop||r&&r.clientTop||0)),e.which||void 0===i||(e.which=1&i?1:2&i?3:4&i?2:0),e}},fix:function(e){if(e[w.expando])return e;var t,n,r,i=e.type,o=e,s=this.fixHooks[i];for(s||(this.fixHooks[i]=s=se.test(i)?this.mouseHooks:oe.test(i)?this.keyHooks:{}),r=s.props?this.props.concat(s.props):this.props,e=new w.Event(o),t=r.length;t--;)e[n=r[t]]=o[n];return e.target||(e.target=m),3===e.target.nodeType&&(e.target=e.target.parentNode),s.filter?s.filter(e,o):e},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==le()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===le()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&w.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(e){return w.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){e=w.extend(new w.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?w.event.trigger(e,null,t):w.event.dispatch.call(t,e),e.isDefaultPrevented()&&n.preventDefault()}},w.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)},w.Event=function(e,t){return this instanceof w.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?p:d):this.type=e,t&&w.extend(this,t),this.timeStamp=e&&e.timeStamp||w.now(),void(this[w.expando]=!0)):new w.Event(e,t)},w.Event.prototype={isDefaultPrevented:d,isPropagationStopped:d,isImmediatePropagationStopped:d,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=p,e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=p,e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=p,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){w.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||w.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),g.focusinBubbles||w.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){w.event.simulate(r,e.target,w.event.fix(e),!0)}w.event.special[r]={setup:function(){var e=this.ownerDocument||this,t=v.access(e,r);t||e.addEventListener(n,i,!0),v.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this,t=v.access(e,r)-1;t?v.access(e,r,t):(e.removeEventListener(n,i,!0),v.remove(e,r))}}}),w.fn.extend({on:function(e,t,n,r,i){var o,s;if("object"==typeof e){for(s in"string"!=typeof t&&(n=n||t,t=void 0),e)this.on(s,t,n,e[s],i);return this}if(null==n&&null==r?(r=t,n=t=void 0):null==r&&("string"==typeof t?(r=n,n=void 0):(r=n,n=t,t=void 0)),!1===r)r=d;else if(!r)return this;return 1===i&&(o=r,(r=function(e){return w().off(e),o.apply(this,arguments)}).guid=o.guid||(o.guid=w.guid++)),this.each(function(){w.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)r=e.handleObj,w(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=d),this.each(function(){w.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i])}return this},trigger:function(e,t){return this.each(function(){w.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?w.event.trigger(e,t,n,!0):void 0}});var ce=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,fe=/<([\w:]+)/,pe=/<|&#?\w+;/,de=/<(?:script|style|link)/i,he=/checked\s*(?:[^=]|=\s*.checked.)/i,ge=/^$|\/(?:java|ecma)script/i,me=/^true\/(.*)/,ve=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,b={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ye(e,t){return w.nodeName(e,"table")&&w.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function xe(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function be(e){var t=me.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function we(e,t){for(var n=0,r=e.length;n<r;n++)v.set(e[n],"globalEval",!t||v.get(t[n],"globalEval"))}function Te(e,t){var n,r,i,o,s,a;if(1===t.nodeType){if(v.hasData(e)&&(o=v.access(e),s=v.set(t,o),a=o.events))for(i in delete s.handle,s.events={},a)for(n=0,r=a[i].length;n<r;n++)w.event.add(t,i,a[i][n]);l.hasData(e)&&(o=l.access(e),s=w.extend({},o),l.set(t,s))}}function C(e,t){var n=e.getElementsByTagName?e.getElementsByTagName(t||"*"):e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&w.nodeName(e,t)?w.merge([e],n):n}b.optgroup=b.option,b.tbody=b.tfoot=b.colgroup=b.caption=b.thead,b.th=b.td,w.extend({clone:function(e,t,n){var r,i,o,s,a,u,l,c=e.cloneNode(!0),f=w.contains(e.ownerDocument,e);if(!(g.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||w.isXMLDoc(e)))for(s=C(c),r=0,i=(o=C(e)).length;r<i;r++)a=o[r],u=s[r],l=void 0,"input"===(l=u.nodeName.toLowerCase())&&ie.test(a.type)?u.checked=a.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=a.defaultValue);if(t)if(n)for(o=o||C(e),s=s||C(c),r=0,i=o.length;r<i;r++)Te(o[r],s[r]);else Te(e,c);return 0<(s=C(c,"script")).length&&we(s,!f&&C(e,"script")),c},buildFragment:function(e,t,n,r){for(var i,o,s,a,u,l=t.createDocumentFragment(),c=[],f=0,p=e.length;f<p;f++)if((i=e[f])||0===i)if("object"===w.type(i))w.merge(c,i.nodeType?[i]:i);else if(pe.test(i)){for(o=o||l.appendChild(t.createElement("div")),s=(fe.exec(i)||["",""])[1].toLowerCase(),o.innerHTML=(s=b[s]||b._default)[1]+i.replace(ce,"<$1></$2>")+s[2],u=s[0];u--;)o=o.lastChild;w.merge(c,o.childNodes),(o=l.firstChild).textContent=""}else c.push(t.createTextNode(i));for(l.textContent="",f=0;i=c[f++];)if((!r||-1===w.inArray(i,r))&&(a=w.contains(i.ownerDocument,i),o=C(l.appendChild(i),"script"),a&&we(o),n))for(u=0;i=o[u++];)ge.test(i.type||"")&&n.push(i);return l},cleanData:function(e){for(var t,n,r,i,o=w.event.special,s=0;void 0!==(n=e[s]);s++){if(w.acceptData(n)&&(i=n[v.expando])&&(t=v.cache[i])){if(t.events)for(r in t.events)o[r]?w.event.remove(n,r):w.removeEvent(n,r,t.handle);v.cache[i]&&delete v.cache[i]}delete l.cache[n[l.expando]]}}}),w.fn.extend({text:function(e){return a(this,function(e){return void 0===e?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||ye(this,e).appendChild(e)})},prepend:function(){return this.domManip(arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=ye(this,e)).insertBefore(e,t.firstChild)})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,r=e?w.filter(e,this):this,i=0;null!=(n=r[i]);i++)t||1!==n.nodeType||w.cleanData(C(n)),n.parentNode&&(t&&w.contains(n.ownerDocument,n)&&we(C(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(w.cleanData(C(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return w.clone(this,e,t)})},html:function(e){return a(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!de.test(e)&&!b[(fe.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(ce,"<$1></$2>");try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(w.cleanData(C(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var t=arguments[0];return this.domManip(arguments,function(e){t=this.parentNode,w.cleanData(C(this)),t&&t.replaceChild(e,this)}),t&&(t.length||t.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(n,r){n=R.apply([],n);var e,t,i,o,s,a,u=0,l=this.length,c=this,f=l-1,p=n[0],d=w.isFunction(p);if(d||1<l&&"string"==typeof p&&!g.checkClone&&he.test(p))return this.each(function(e){var t=c.eq(e);d&&(n[0]=p.call(this,e,t.html())),t.domManip(n,r)});if(l&&(t=(e=w.buildFragment(n,this[0].ownerDocument,!1,this)).firstChild,1===e.childNodes.length&&(e=t),t)){for(o=(i=w.map(C(e,"script"),xe)).length;u<l;u++)s=e,u!==f&&(s=w.clone(s,!0,!0),o)&&w.merge(i,C(s,"script")),r.call(this[u],s,u);if(o)for(a=i[i.length-1].ownerDocument,w.map(i,be),u=0;u<o;u++)s=i[u],ge.test(s.type||"")&&!v.access(s,"globalEval")&&w.contains(a,s)&&(s.src?w._evalUrl&&w._evalUrl(s.src):w.globalEval(s.textContent.replace(ve,"")))}return this}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){w.fn[e]=function(e){for(var t,n=[],r=w(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),w(r[o])[s](t),W.apply(n,t.get());return this.pushStack(n)}});var Ce,Ne={};function ke(e,t){e=w(t.createElement(e)).appendTo(t.body),t=h.getDefaultComputedStyle&&(t=h.getDefaultComputedStyle(e[0]))?t.display:w.css(e[0],"display");return e.detach(),t}function Ee(e){var t=m,n=Ne[e];return n||("none"!==(n=ke(e,t))&&n||((t=(Ce=(Ce||w("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement))[0].contentDocument).write(),t.close(),n=ke(e,t),Ce.detach()),Ne[e]=n),n}function Se(e){return e.ownerDocument.defaultView.getComputedStyle(e,null)}var je,De,N,k,E,Ae=/^margin/,Le=new RegExp("^("+t+")(?!px)[a-z%]+$","i");function S(e,t,n){var r,i,o=e.style;return(n=n||Se(e))&&(i=n.getPropertyValue(t)||n[t]),n&&(""!==i||w.contains(e.ownerDocument,e)||(i=w.style(e,t)),Le.test(i))&&Ae.test(t)&&(e=o.width,t=o.minWidth,r=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=e,o.minWidth=t,o.maxWidth=r),void 0!==i?i+"":i}function qe(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function He(){E.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",E.innerHTML="",N.appendChild(k);var e=h.getComputedStyle(E,null);je="1%"!==e.top,De="4px"===e.width,N.removeChild(k)}N=m.documentElement,k=m.createElement("div"),(E=m.createElement("div")).style&&(E.style.backgroundClip="content-box",E.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===E.style.backgroundClip,k.style.cssText="border:0;width:0;height:0;top:0;left:-9999px;margin-top:1px;position:absolute",k.appendChild(E),h.getComputedStyle)&&w.extend(g,{pixelPosition:function(){return He(),je},boxSizingReliable:function(){return null==De&&He(),De},reliableMarginRight:function(){var e=E.appendChild(m.createElement("div"));return e.style.cssText=E.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",e.style.marginRight=e.style.width="0",E.style.width="1px",N.appendChild(k),e=!parseFloat(h.getComputedStyle(e,null).marginRight),N.removeChild(k),e}}),w.swap=function(e,t,n,r){var i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in n=n.apply(e,r||[]),t)e.style[i]=o[i];return n};var Oe=/^(none|table(?!-c[ea]).+)/,Fe=new RegExp("^("+t+")(.*)$","i"),Pe=new RegExp("^([+-])=("+t+")","i"),Me={position:"absolute",visibility:"hidden",display:"block"},Re={letterSpacing:"0",fontWeight:"400"},We=["Webkit","O","Moz","ms"];function $e(e,t){if(t in e)return t;for(var n=t[0].toUpperCase()+t.slice(1),r=t,i=We.length;i--;)if((t=We[i]+n)in e)return t;return r}function Be(e,t,n){var r=Fe.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function Ie(e,t,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===t?1:0,s=0;o<4;o+=2)"margin"===n&&(s+=w.css(e,n+f[o],!0,i)),r?("content"===n&&(s-=w.css(e,"padding"+f[o],!0,i)),"margin"!==n&&(s-=w.css(e,"border"+f[o]+"Width",!0,i))):(s+=w.css(e,"padding"+f[o],!0,i),"padding"!==n&&(s+=w.css(e,"border"+f[o]+"Width",!0,i)));return s}function _e(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,o=Se(e),s="border-box"===w.css(e,"boxSizing",!1,o);if(i<=0||null==i){if(((i=S(e,t,o))<0||null==i)&&(i=e.style[t]),Le.test(i))return i;r=s&&(g.boxSizingReliable()||i===e.style[t]),i=parseFloat(i)||0}return i+Ie(e,t,n||(s?"border":"content"),r,o)+"px"}function ze(e,t){for(var n,r,i,o=[],s=0,a=e.length;s<a;s++)(r=e[s]).style&&(o[s]=v.get(r,"olddisplay"),n=r.style.display,t?(o[s]||"none"!==n||(r.style.display=""),""===r.style.display&&y(r)&&(o[s]=v.access(r,"olddisplay",Ee(r.nodeName)))):(i=y(r),"none"===n&&i||v.set(r,"olddisplay",i?n:w.css(r,"display"))));for(s=0;s<a;s++)!(r=e[s]).style||t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?o[s]||"":"none");return e}function j(e,t,n,r,i){return new j.prototype.init(e,t,n,r,i)}w.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=S(e,"opacity"))?"1":t}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(e,t,n,r){var i,o,s,a,u;if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style)return a=w.camelCase(t),u=e.style,t=w.cssProps[a]||(w.cssProps[a]=$e(u,a)),s=w.cssHooks[t]||w.cssHooks[a],void 0===n?s&&"get"in s&&void 0!==(i=s.get(e,!1,r))?i:u[t]:("string"===(o=typeof n)&&(i=Pe.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(w.css(e,t)),o="number"),void(null!=n&&n==n&&("number"!==o||w.cssNumber[a]||(n+="px"),g.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,r))||(u[t]=n))))},css:function(e,t,n,r){var i,o=w.camelCase(t);return t=w.cssProps[o]||(w.cssProps[o]=$e(e.style,o)),"normal"===(i=void 0===(i=(o=w.cssHooks[t]||w.cssHooks[o])&&"get"in o?o.get(e,!0,n):i)?S(e,t,r):i)&&t in Re&&(i=Re[t]),(""===n||n)&&(o=parseFloat(i),!0===n||w.isNumeric(o))?o||0:i}}),w.each(["height","width"],function(e,i){w.cssHooks[i]={get:function(e,t,n){return t?Oe.test(w.css(e,"display"))&&0===e.offsetWidth?w.swap(e,Me,function(){return _e(e,i,n)}):_e(e,i,n):void 0},set:function(e,t,n){var r=n&&Se(e);return Be(0,t,n?Ie(e,i,n,"border-box"===w.css(e,"boxSizing",!1,r),r):0)}}}),w.cssHooks.marginRight=qe(g.reliableMarginRight,function(e,t){return t?w.swap(e,{display:"inline-block"},S,[e,"marginRight"]):void 0}),w.each({margin:"",padding:"",border:"Width"},function(i,o){w.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+f[t]+o]=r[t]||r[t-2]||r[0];return n}},Ae.test(i)||(w.cssHooks[i+o].set=Be)}),w.fn.extend({css:function(e,t){return a(this,function(e,t,n){var r,i,o={},s=0;if(w.isArray(t)){for(r=Se(e),i=t.length;s<i;s++)o[t[s]]=w.css(e,t[s],!1,r);return o}return void 0!==n?w.style(e,t,n):w.css(e,t)},e,t,1<arguments.length)},show:function(){return ze(this,!0)},hide:function(){return ze(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){y(this)?w(this).show():w(this).hide()})}}),((w.Tween=j).prototype={constructor:j,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(w.cssNumber[n]?"":"px")},cur:function(){var e=j.propHooks[this.prop];return(e&&e.get?e:j.propHooks._default).get(this)},run:function(e){var t=j.propHooks[this.prop];return this.pos=e=this.options.duration?w.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(t&&t.set?t:j.propHooks._default).set(this),this}}).init.prototype=j.prototype,(j.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=w.css(e.elem,e.prop,""))&&"auto"!==t?t:0:e.elem[e.prop]},set:function(e){w.fx.step[e.prop]?w.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[w.cssProps[e.prop]]||w.cssHooks[e.prop])?w.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}}).scrollTop=j.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},w.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},w.fx=j.prototype.init,w.fx.step={};var D,Xe,A,Ue=/^(?:toggle|show|hide)$/,Ve=new RegExp("^(?:([+-])=|)("+t+")([a-z%]*)$","i"),Ye=/queueHooks$/,Ge=[function(t,e,n){var r,i,o,s,a,u,l,c=this,f={},p=t.style,d=t.nodeType&&y(t),h=v.get(t,"fxshow");for(r in n.queue||(null==(a=w._queueHooks(t,"fx")).unqueued&&(a.unqueued=0,u=a.empty.fire,a.empty.fire=function(){a.unqueued||u()}),a.unqueued++,c.always(function(){c.always(function(){a.unqueued--,w.queue(t,"fx").length||a.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],l=w.css(t,"display"),"inline"===("none"===l?v.get(t,"olddisplay")||Ee(t.nodeName):l))&&"none"===w.css(t,"float")&&(p.display="inline-block"),n.overflow&&(p.overflow="hidden",c.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),e)if(i=e[r],Ue.exec(i)){if(delete e[r],o=o||"toggle"===i,i===(d?"hide":"show")){if("show"!==i||!h||void 0===h[r])continue;d=!0}f[r]=h&&h[r]||w.style(t,r)}else l=void 0;if(w.isEmptyObject(f))"inline"===("none"===l?Ee(t.nodeName):l)&&(p.display=l);else for(r in h?"hidden"in h&&(d=h.hidden):h=v.access(t,"fxshow",{}),o&&(h.hidden=!d),d?w(t).show():c.done(function(){w(t).hide()}),c.done(function(){for(var e in v.remove(t,"fxshow"),f)w.style(t,e,f[e])}),f)s=Ke(d?h[r]:0,r,c),r in h||(h[r]=s.start,d&&(s.end=s.start,s.start="width"===r||"height"===r?1:0))}],L={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),t=Ve.exec(t),i=t&&t[3]||(w.cssNumber[e]?"":"px"),o=(w.cssNumber[e]||"px"!==i&&+r)&&Ve.exec(w.css(n.elem,e)),s=1,a=20;if(o&&o[3]!==i)for(i=i||o[3],t=t||[],o=+r||1;w.style(n.elem,e,(o/=s=s||".5")+i),s!==(s=n.cur()/r)&&1!==s&&--a;);return t&&(o=n.start=+o||+r||0,n.unit=i,n.end=t[1]?o+(t[1]+1)*t[2]:+t[2]),n}]};function Qe(){return setTimeout(function(){D=void 0}),D=w.now()}function Je(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=f[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function Ke(e,t,n){for(var r,i=(L[t]||[]).concat(L["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function Ze(i,e,t){var n,o,r,s,a,u,l,c=0,f=Ge.length,p=w.Deferred().always(function(){delete d.elem}),d=function(){if(o)return!1;for(var e=D||Qe(),e=Math.max(0,h.startTime+h.duration-e),t=1-(e/h.duration||0),n=0,r=h.tweens.length;n<r;n++)h.tweens[n].run(t);return p.notifyWith(i,[h,t,e]),t<1&&r?e:(p.resolveWith(i,[h]),!1)},h=p.promise({elem:i,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{}},t),originalProperties:e,originalOptions:t,startTime:D||Qe(),duration:t.duration,tweens:[],createTween:function(e,t){t=w.Tween(i,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(t),t},stop:function(e){var t=0,n=e?h.tweens.length:0;if(!o){for(o=!0;t<n;t++)h.tweens[t].run(1);e?p.resolveWith(i,[h,e]):p.rejectWith(i,[h,e])}return this}}),g=h.props,m=g,v=h.opts.specialEasing;for(r in m)if(s=w.camelCase(r),a=v[s],u=m[r],w.isArray(u)&&(a=u[1],u=m[r]=u[0]),r!==s&&(m[s]=u,delete m[r]),l=w.cssHooks[s],l&&"expand"in l)for(r in u=l.expand(u),delete m[s],u)r in m||(m[r]=u[r],v[r]=a);else v[s]=a;for(;c<f;c++)if(n=Ge[c].call(h,i,g,h.opts))return n;return w.map(g,Ke,h),w.isFunction(h.opts.start)&&h.opts.start.call(i,h),w.fx.timer(w.extend(d,{elem:i,anim:h,queue:h.opts.queue})),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always)}w.Animation=w.extend(Ze,{tweener:function(e,t){for(var n,r=0,i=(e=w.isFunction(e)?(t=e,["*"]):e.split(" ")).length;r<i;r++)n=e[r],L[n]=L[n]||[],L[n].unshift(t)},prefilter:function(e,t){t?Ge.unshift(e):Ge.push(e)}}),w.speed=function(e,t,n){var r=e&&"object"==typeof e?w.extend({},e):{complete:n||!n&&t||w.isFunction(e)&&e,duration:e,easing:n&&t||t&&!w.isFunction(t)&&t};return r.duration=w.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in w.fx.speeds?w.fx.speeds[r.duration]:w.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){w.isFunction(r.old)&&r.old.call(this),r.queue&&w.dequeue(this,r.queue)},r},w.fn.extend({fadeTo:function(e,t,n,r){return this.filter(y).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){function i(){var e=Ze(this,w.extend({},t),s);(o||v.get(this,"finish"))&&e.stop(!0)}var o=w.isEmptyObject(t),s=w.speed(e,n,r);return i.finish=i,o||!1===s.queue?this.each(i):this.queue(s.queue,i)},stop:function(i,e,o){function s(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&!1!==i&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=w.timers,r=v.get(this);if(t)r[t]&&r[t].stop&&s(r[t]);else for(t in r)r[t]&&r[t].stop&&Ye.test(t)&&s(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||w.dequeue(this,i)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=v.get(this),n=t[s+"queue"],r=t[s+"queueHooks"],i=w.timers,o=n?n.length:0;for(t.finish=!0,w.queue(this,s,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===s&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),w.each(["toggle","show","hide"],function(e,r){var i=w.fn[r];w.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(Je(r,!0),e,t,n)}}),w.each({slideDown:Je("show"),slideUp:Je("hide"),slideToggle:Je("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){w.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),w.timers=[],w.fx.tick=function(){var e,t=0,n=w.timers;for(D=w.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||w.fx.stop(),D=void 0},w.fx.timer=function(e){w.timers.push(e),e()?w.fx.start():w.timers.pop()},w.fx.interval=13,w.fx.start=function(){Xe=Xe||setInterval(w.fx.tick,w.fx.interval)},w.fx.stop=function(){clearInterval(Xe),Xe=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(r,e){return r=w.fx&&w.fx.speeds[r]||r,this.queue(e=e||"fx",function(e,t){var n=setTimeout(e,r);t.stop=function(){clearTimeout(n)}})},A=m.createElement("input"),e=m.createElement("select"),t=e.appendChild(m.createElement("option")),A.type="checkbox",g.checkOn=""!==A.value,g.optSelected=t.selected,e.disabled=!0,g.optDisabled=!t.disabled,(A=m.createElement("input")).value="t",A.type="radio",g.radioValue="t"===A.value;var et,q=w.expr.attrHandle,tt=(w.fn.extend({attr:function(e,t){return a(this,w.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){w.removeAttr(this,e)})}}),w.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute==x?w.prop(e,t,n):(1===o&&w.isXMLDoc(e)||(t=t.toLowerCase(),r=w.attrHooks[t]||(w.expr.match.bool.test(t)?et:void 0)),void 0===n?!(r&&"get"in r&&null!==(i=r.get(e,t)))&&null==(i=w.find.attr(e,t))?void 0:i:null!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):void w.removeAttr(e,t))},removeAttr:function(e,t){var n,r,i=0,o=t&&t.match(T);if(o&&1===e.nodeType)for(;n=o[i++];)r=w.propFix[n]||n,w.expr.match.bool.test(n)&&(e[r]=!1),e.removeAttribute(n)},attrHooks:{type:{set:function(e,t){var n;if(!g.radioValue&&"radio"===t&&w.nodeName(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}}}),et={set:function(e,t,n){return!1===t?w.removeAttr(e,n):e.setAttribute(n,n),n}},w.each(w.expr.match.bool.source.match(/\w+/g),function(e,t){var o=q[t]||w.find.attr;q[t]=function(e,t,n){var r,i;return n||(i=q[t],q[t]=r,r=null!=o(e,t,n)?t.toLowerCase():null,q[t]=i),r}}),/^(?:input|select|textarea|button)$/i),nt=(w.fn.extend({prop:function(e,t){return a(this,w.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[w.propFix[e]||e]})}}),w.extend({propFix:{for:"htmlFor",class:"className"},prop:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return(1!==o||!w.isXMLDoc(e))&&(t=w.propFix[t]||t,i=w.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){return e.hasAttribute("tabindex")||tt.test(e.nodeName)||e.href?e.tabIndex:-1}}}}),g.optSelected||(w.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this}),/[\t\r\n\f]/g),rt=(w.fn.extend({addClass:function(t){var e,n,r,i,o,s,a="string"==typeof t&&t,u=0,l=this.length;if(w.isFunction(t))return this.each(function(e){w(this).addClass(t.call(this,e,this.className))});if(a)for(e=(t||"").match(T)||[];u<l;u++)if(r=1===(n=this[u]).nodeType&&(n.className?(" "+n.className+" ").replace(nt," "):" ")){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");s=w.trim(r),n.className!==s&&(n.className=s)}return this},removeClass:function(t){var e,n,r,i,o,s,a=0===arguments.length||"string"==typeof t&&t,u=0,l=this.length;if(w.isFunction(t))return this.each(function(e){w(this).removeClass(t.call(this,e,this.className))});if(a)for(e=(t||"").match(T)||[];u<l;u++)if(r=1===(n=this[u]).nodeType&&(n.className?(" "+n.className+" ").replace(nt," "):"")){for(o=0;i=e[o++];)for(;0<=r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");s=t?w.trim(r):"",n.className!==s&&(n.className=s)}return this},toggleClass:function(i,t){var o=typeof i;return"boolean"==typeof t&&"string"==o?t?this.addClass(i):this.removeClass(i):this.each(w.isFunction(i)?function(e){w(this).toggleClass(i.call(this,e,this.className,t),t)}:function(){if("string"==o)for(var e,t=0,n=w(this),r=i.match(T)||[];e=r[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else o!=x&&"boolean"!=o||(this.className&&v.set(this,"__className__",this.className),this.className=!this.className&&!1!==i&&v.get(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;n<r;n++)if(1===this[n].nodeType&&0<=(" "+this[n].className+" ").replace(nt," ").indexOf(t))return!0;return!1}}),/\r/g),it=(w.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=w.isFunction(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,w(this).val()):t)?e="":"number"==typeof e?e+="":w.isArray(e)&&(e=w.map(e,function(e){return null==e?"":e+""})),(n=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):i?(n=w.valHooks[i.type]||w.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(rt,""):null==e?"":e:void 0}}),w.extend({valHooks:{option:{get:function(e){var t=w.find.attr(e,"value");return null!=t?t:w.trim(w.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type||r<0,o=i?null:[],s=i?r+1:n.length,a=r<0?s:i?r:0;a<s;a++)if(!(!(t=n[a]).selected&&a!==r||(g.optDisabled?t.disabled:null!==t.getAttribute("disabled"))||t.parentNode.disabled&&w.nodeName(t.parentNode,"optgroup"))){if(t=w(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=w.makeArray(t),s=i.length;s--;)((r=i[s]).selected=0<=w.inArray(r.value,o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(e,t){return w.isArray(t)?e.checked=0<=w.inArray(w(e).val(),t):void 0}},g.checkOn||(w.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),w.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,n){w.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),w.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),w.now()),ot=/\?/;w.parseJSON=function(e){return JSON.parse(e+"")},w.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||w.error("Invalid XML: "+e),t};var H,O,st=/#.*$/,at=/([?&])_=[^&]*/,ut=/^(.*?):[ \t]*([^\r\n]*)$/gm,lt=/^(?:GET|HEAD)$/,ct=/^\/\//,ft=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,pt={},dt={},ht="*/".concat("*");try{O=location.href}catch(e){(O=m.createElement("a")).href="",O=O.href}function gt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(T)||[];if(w.isFunction(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function mt(t,r,i,o){var s={},a=t===dt;function u(e){var n;return s[e]=!0,w.each(t[e]||[],function(e,t){t=t(r,i,o);return"string"!=typeof t||a||s[t]?a?!(n=t):void 0:(r.dataTypes.unshift(t),u(t),!1)}),n}return u(r.dataTypes[0])||!s["*"]&&u("*")}function vt(e,t){var n,r,i=w.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&w.extend(!0,e,r),e}H=ft.exec(O.toLowerCase())||[],w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:O,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(H[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":ht,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":w.parseJSON,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?vt(vt(e,w.ajaxSettings),t):vt(w.ajaxSettings,e)},ajaxPrefilter:gt(pt),ajaxTransport:gt(dt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var u,l,c,n,f,p,r,d=w.ajaxSetup({},t=t||{}),h=d.context||d,g=d.context&&(h.nodeType||h.jquery)?w(h):w.event,m=w.Deferred(),v=w.Callbacks("once memory"),y=d.statusCode||{},i={},o={},x=0,s="canceled",b={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!n)for(n={};t=ut.exec(c);)n[t[1].toLowerCase()]=t[2];t=n[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?c:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=o[n]=o[n]||e,i[e]=t),this},overrideMimeType:function(e){return x||(d.mimeType=e),this},statusCode:function(e){if(e)if(x<2)for(var t in e)y[t]=[y[t],e[t]];else b.always(e[b.status]);return this},abort:function(e){e=e||s;return u&&u.abort(e),a(0,e),this}};if(m.promise(b).complete=v.add,b.success=b.done,b.error=b.fail,d.url=((e||d.url||O)+"").replace(st,"").replace(ct,H[1]+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=w.trim(d.dataType||"*").toLowerCase().match(T)||[""],null==d.crossDomain&&(e=ft.exec(d.url.toLowerCase()),d.crossDomain=!(!e||e[1]===H[1]&&e[2]===H[2]&&(e[3]||("http:"===e[1]?"80":"443"))===(H[3]||("http:"===H[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=w.param(d.data,d.traditional)),mt(pt,d,t,b),2!==x){for(r in(p=d.global)&&0==w.active++&&w.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!lt.test(d.type),l=d.url,d.hasContent||(d.data&&(l=d.url+=(ot.test(l)?"&":"?")+d.data,delete d.data),!1===d.cache&&(d.url=at.test(l)?l.replace(at,"$1_="+it++):l+(ot.test(l)?"&":"?")+"_="+it++)),d.ifModified&&(w.lastModified[l]&&b.setRequestHeader("If-Modified-Since",w.lastModified[l]),w.etag[l])&&b.setRequestHeader("If-None-Match",w.etag[l]),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&b.setRequestHeader("Content-Type",d.contentType),b.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+ht+"; q=0.01":""):d.accepts["*"]),d.headers)b.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(!1===d.beforeSend.call(h,b,d)||2===x))return b.abort();for(r in s="abort",{success:1,error:1,complete:1})b[r](d[r]);if(u=mt(dt,d,t,b)){b.readyState=1,p&&g.trigger("ajaxSend",[b,d]),d.async&&0<d.timeout&&(f=setTimeout(function(){b.abort("timeout")},d.timeout));try{x=1,u.send(i,a)}catch(e){if(!(x<2))throw e;a(-1,e)}}else a(-1,"No Transport")}return b;function a(e,t,n,r){var i,o,s,a=t;2!==x&&(x=2,f&&clearTimeout(f),u=void 0,c=r||"",b.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(s=function(e,t,n){for(var r,i,o,s,a=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}s=s||i}o=o||s}return o?(o!==u[0]&&u.unshift(o),n[o]):void 0}(d,b,n)),s=function(e,t,n,r){var i,o,s,a,u,l={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)l[s.toLowerCase()]=e.converters[s];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=l[u+" "+o]||l["* "+o]))for(i in l)if(a=i.split(" "),a[1]===o&&(s=l[u+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[i]:!0!==l[i]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(d,s,b,r),r?(d.ifModified&&((n=b.getResponseHeader("Last-Modified"))&&(w.lastModified[l]=n),n=b.getResponseHeader("etag"))&&(w.etag[l]=n),204===e||"HEAD"===d.type?a="nocontent":304===e?a="notmodified":(a=s.state,i=s.data,r=!(o=s.error))):(o=a,(e||!a)&&(a="error",e<0)&&(e=0)),b.status=e,b.statusText=(t||a)+"",r?m.resolveWith(h,[i,a,b]):m.rejectWith(h,[b,a,o]),b.statusCode(y),y=void 0,p&&g.trigger(r?"ajaxSuccess":"ajaxError",[b,d,r?i:o]),v.fireWith(h,[b,a]),p)&&(g.trigger("ajaxComplete",[b,d]),--w.active||w.event.trigger("ajaxStop"))}},getJSON:function(e,t,n){return w.get(e,t,n,"json")},getScript:function(e,t){return w.get(e,void 0,t,"script")}}),w.each(["get","post"],function(e,i){w[i]=function(e,t,n,r){return w.isFunction(t)&&(r=r||n,n=t,t=void 0),w.ajax({url:e,type:i,dataType:r,data:t,success:n})}}),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){w.fn[t]=function(e){return this.on(t,e)}}),w._evalUrl=function(e){return w.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},w.fn.extend({wrapAll:function(t){var e;return w.isFunction(t)?this.each(function(e){w(this).wrapAll(t.call(this,e))}):(this[0]&&(e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this)},wrapInner:function(n){return this.each(w.isFunction(n)?function(e){w(this).wrapInner(n.call(this,e))}:function(){var e=w(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=w.isFunction(t);return this.each(function(e){w(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(){return this.parent().each(function(){w.nodeName(this,"body")||w(this).replaceWith(this.childNodes)}).end()}}),w.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0},w.expr.filters.visible=function(e){return!w.expr.filters.hidden(e)};var yt=/%20/g,xt=/\[\]$/,bt=/\r?\n/g,wt=/^(?:submit|button|image|reset|file)$/i,Tt=/^(?:input|select|textarea|keygen)/i;w.param=function(e,t){function n(e,t){t=w.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)}var r,i=[];if(void 0===t&&(t=w.ajaxSettings&&w.ajaxSettings.traditional),w.isArray(e)||e.jquery&&!w.isPlainObject(e))w.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(w.isArray(e))w.each(e,function(e,t){i||xt.test(r)?o(r,t):n(r+"["+("object"==typeof t?e:"")+"]",t,i,o)});else if(i||"object"!==w.type(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&").replace(yt,"+")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=w.prop(this,"elements");return e?w.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!w(this).is(":disabled")&&Tt.test(this.nodeName)&&!wt.test(e)&&(this.checked||!ie.test(e))}).map(function(e,t){var n=w(this).val();return null==n?null:w.isArray(n)?w.map(n,function(e){return{name:t.name,value:e.replace(bt,"\r\n")}}):{name:t.name,value:n.replace(bt,"\r\n")}}).get()}}),w.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(e){}};var Ct=0,Nt={},kt={0:200,1223:204},F=w.ajaxSettings.xhr(),Et=(h.ActiveXObject&&w(h).on("unload",function(){for(var e in Nt)Nt[e]()}),g.cors=!!F&&"withCredentials"in F,g.ajax=F=!!F,w.ajaxTransport(function(o){var s;return g.cors||F&&!o.crossDomain?{send:function(e,t){var n,r=o.xhr(),i=++Ct;if(r.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(n in o.xhrFields)r[n]=o.xhrFields[n];for(n in o.mimeType&&r.overrideMimeType&&r.overrideMimeType(o.mimeType),o.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);s=function(e){return function(){s&&(delete Nt[i],s=r.onload=r.onerror=null,"abort"===e?r.abort():"error"===e?t(r.status,r.statusText):t(kt[r.status]||r.status,r.statusText,"string"==typeof r.responseText?{text:r.responseText}:void 0,r.getAllResponseHeaders()))}},r.onload=s(),r.onerror=s("error"),s=Nt[i]=s("abort");try{r.send(o.hasContent&&o.data||null)}catch(e){if(s)throw e}},abort:function(){s&&s()}}:void 0}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return w.globalEval(e),e}}}),w.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),w.ajaxTransport("script",function(n){var r,i;if(n.crossDomain)return{send:function(e,t){r=w("<script>").prop({async:!0,charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),m.head.appendChild(r[0])},abort:function(){i&&i()}}}),[]),St=/(=)\?(?=&|$)|\?\?/,jt=(w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Et.pop()||w.expando+"_"+it++;return this[e]=!0,e}}),w.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,s=!1!==e.jsonp&&(St.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&St.test(e.data)&&"data");return s||"jsonp"===e.dataTypes[0]?(r=e.jsonpCallback=w.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(St,"$1"+r):!1!==e.jsonp&&(e.url+=(ot.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||w.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=h[r],h[r]=function(){o=arguments},n.always(function(){h[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Et.push(r)),o&&w.isFunction(i)&&i(o[0]),o=i=void 0}),"script"):void 0}),w.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||m;var r=V.exec(e),n=!n&&[];return r?[t.createElement(r[1])]:(r=w.buildFragment([e],t,n),n&&n.length&&w(n).remove(),w.merge([],r.childNodes))},w.fn.load),Dt=(w.fn.load=function(e,t,n){var r,i,o,s,a;return"string"!=typeof e&&jt?jt.apply(this,arguments):(s=this,0<=(a=e.indexOf(" "))&&(r=w.trim(e.slice(a)),e=e.slice(0,a)),w.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),0<s.length&&w.ajax({url:e,type:i,dataType:"html",data:t}).done(function(e){o=arguments,s.html(r?w("<div>").append(w.parseHTML(e)).find(r):e)}).complete(n&&function(e,t){s.each(n,o||[e.responseText,t,e])}),this)},w.expr.filters.animated=function(t){return w.grep(w.timers,function(e){return t===e.elem}).length},h.document.documentElement);function At(e){return w.isWindow(e)?e:9===e.nodeType&&e.defaultView}w.offset={setOffset:function(e,t,n){var r,i,o,s,a=w.css(e,"position"),u=w(e),l={};"static"===a&&(e.style.position="relative"),o=u.offset(),r=w.css(e,"top"),s=w.css(e,"left"),a=("absolute"===a||"fixed"===a)&&-1<(r+s).indexOf("auto")?(i=(a=u.position()).top,a.left):(i=parseFloat(r)||0,parseFloat(s)||0),null!=(t=w.isFunction(t)?t.call(e,n,o):t).top&&(l.top=t.top-o.top+i),null!=t.left&&(l.left=t.left-o.left+a),"using"in t?t.using.call(e,l):u.css(l)}},w.fn.extend({offset:function(t){var e,n,r,i;return arguments.length?void 0===t?this:this.each(function(e){w.offset.setOffset(this,t,e)}):(r={top:0,left:0},(i=(n=this[0])&&n.ownerDocument)?(e=i.documentElement,w.contains(e,n)?(typeof n.getBoundingClientRect!=x&&(r=n.getBoundingClientRect()),n=At(i),{top:r.top+n.pageYOffset-e.clientTop,left:r.left+n.pageXOffset-e.clientLeft}):r):void 0)},position:function(){var e,t,n,r;if(this[0])return n=this[0],r={top:0,left:0},"fixed"===w.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),(r=w.nodeName(e[0],"html")?r:e.offset()).top+=w.css(e[0],"borderTopWidth",!0),r.left+=w.css(e[0],"borderLeftWidth",!0)),{top:t.top-r.top-w.css(n,"marginTop",!0),left:t.left-r.left-w.css(n,"marginLeft",!0)}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||Dt;e&&!w.nodeName(e,"html")&&"static"===w.css(e,"position");)e=e.offsetParent;return e||Dt})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;w.fn[t]=function(e){return a(this,function(e,t,n){var r=At(e);return void 0===n?r?r[i]:e[t]:void(r?r.scrollTo(o?h.pageXOffset:n,o?n:h.pageYOffset):e[t]=n)},t,e,arguments.length,null)}}),w.each(["top","left"],function(e,n){w.cssHooks[n]=qe(g.pixelPosition,function(e,t){return t?(t=S(e,n),Le.test(t)?w(e).position()[n]+"px":t):void 0})}),w.each({Height:"height",Width:"width"},function(o,s){w.each({padding:"inner"+o,content:s,"":"outer"+o},function(r,e){w.fn[e]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return a(this,function(e,t,n){var r;return w.isWindow(e)?e.document.documentElement["client"+o]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+o],r["scroll"+o],e.body["offset"+o],r["offset"+o],r["client"+o])):void 0===n?w.css(e,t,i):w.style(e,t,n,i)},s,n?e:void 0,n,null)}})}),w.fn.size=function(){return this.length},w.fn.andSelf=w.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Lt=h.jQuery,qt=h.$;return w.noConflict=function(e){return h.$===w&&(h.$=qt),e&&h.jQuery===w&&(h.jQuery=Lt),w},typeof P==x&&(h.jQuery=h.$=w),w});