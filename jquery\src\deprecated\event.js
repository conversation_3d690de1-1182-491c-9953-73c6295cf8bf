define(["../core","../event","../event/trigger"],function(n){"use strict";n.fn.extend({bind:function(e,n,t){return this.on(e,null,n,t)},unbind:function(e,n){return this.off(e,null,n)},delegate:function(e,n,t,u){return this.on(n,e,t,u)},undelegate:function(e,n,t){
// ( namespace ) or ( selector, types [, fn] )
return 1===arguments.length?this.off(e,"**"):this.off(n,e||"**",t)},hover:function(e,n){return this.mouseenter(e).mouseleave(n||e)}}),n.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){
// Handle event binding
n.fn[t]=function(e,n){return 0<arguments.length?this.on(t,null,e,n):this.trigger(t)}})});