/*!
  * Bootstrap v4.0.0 (https://getbootstrap.com)
  * Copyright 2011-2018 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],e):e(t.bootstrap={},t.jQuery)}(this,function(t,e){"use strict";function H(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function n(t,e,n){e&&H(t.prototype,e),n&&H(t,n)}function r(){return(r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n,i=arguments[e];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}h=e=e&&e.hasOwnProperty("default")?e.default:e,bt=!1,yt={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e=t.getAttribute("data-target");"#"===(e=e&&"#"!==e?e:t.getAttribute("href")||"").charAt(0)&&(t=e,e=t="function"==typeof h.escapeSelector?h.escapeSelector(t).substr(1):t.replace(/(:|\.|\[|\]|,|=|@)/g,"\\$1"));try{return 0<h(document).find(e).length?e:null}catch(t){return null}},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){h(t).trigger(bt.end)},supportsTransitionEnd:function(){return Boolean(bt)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var o=n[i],r=e[i],r=r&&yt.isElement(r)?"element":{}.toString.call(r).match(/\s([a-zA-Z]+)/)[1].toLowerCase();if(!new RegExp(o).test(r))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+r+'" but expected type "'+o+'".')}}},bt=("undefined"==typeof window||!window.QUnit)&&{end:"transitionend"},h.fn.emulateTransitionEnd=function(t){var e=this,n=!1;return h(this).one(yt.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||yt.triggerTransitionEnd(e)},t),this},yt.supportsTransitionEnd()&&(h.event.special[yt.TRANSITION_END]={bindType:bt.end,delegateType:bt.end,handle:function(t){if(h(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}});for(var i,W,M,R,U,B,s,F,K,Q,Y,V,q,a,z,G,Z,J,$,X,l,tt,et,nt,it,ot,rt,c,st,at,o,lt,ht,ct,ft,ut,f,u,dt,pt,gt,mt,_t,vt,Et,h,bt,yt,d=yt,p=(p="."+(M="bs.alert"),R=(i=e).fn[W="alert"],U={CLOSE:"close"+p,CLOSED:"closed"+p,CLICK_DATA_API:"click"+p+".data-api"},(p=Nt.prototype).close=function(t){t=t||this._element;t=this._getRootElement(t);this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},p.dispose=function(){i.removeData(this._element,M),this._element=null},p._getRootElement=function(t){var e=d.getSelectorFromElement(t),n=!1;return n=(n=e?i(e)[0]:n)||i(t).closest(".alert")[0]},p._triggerCloseEvent=function(t){var e=i.Event(U.CLOSE);return i(t).trigger(e),e},p._removeElement=function(e){var n=this;i(e).removeClass("show"),d.supportsTransitionEnd()&&i(e).hasClass("fade")?i(e).one(d.TRANSITION_END,function(t){return n._destroyElement(e,t)}).emulateTransitionEnd(150):this._destroyElement(e)},p._destroyElement=function(t){i(t).detach().trigger(U.CLOSED).remove()},Nt._jQueryInterface=function(n){return this.each(function(){var t=i(this),e=t.data(M);e||(e=new Nt(this),t.data(M,e)),"close"===n&&e[n](this)})},Nt._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},n(Nt,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),B=Nt,i(document).on(U.CLICK_DATA_API,'[data-dismiss="alert"]',B._handleDismiss(new B)),i.fn[W]=B._jQueryInterface,i.fn[W].Constructor=B,i.fn[W].noConflict=function(){return i.fn[W]=R,B._jQueryInterface},B),wt=(m="."+(K="bs.button"),Q=(s=e).fn[F="button"],Y="active",g='[data-toggle^="button"]',V=".btn",m={CLICK_DATA_API:"click"+m+(wt=".data-api"),FOCUS_BLUR_DATA_API:"focus"+m+wt+" blur"+m+wt},(wt=Ot.prototype).toggle=function(){var t=!0,e=!0,n=s(this._element).closest('[data-toggle="buttons"]')[0];if(n){var i,o=s(this._element).find("input")[0];if(o){if("radio"===o.type&&(o.checked&&s(this._element).hasClass(Y)?t=!1:(i=s(n).find(".active")[0])&&s(i).removeClass(Y)),t){if(o.hasAttribute("disabled")||n.hasAttribute("disabled")||o.classList.contains("disabled")||n.classList.contains("disabled"))return;o.checked=!s(this._element).hasClass(Y),s(o).trigger("change")}o.focus(),e=!1}}e&&this._element.setAttribute("aria-pressed",!s(this._element).hasClass(Y)),t&&s(this._element).toggleClass(Y)},wt.dispose=function(){s.removeData(this._element,K),this._element=null},Ot._jQueryInterface=function(e){return this.each(function(){var t=s(this).data(K);t||(t=new Ot(this),s(this).data(K,t)),"toggle"===e&&t[e]()})},n(Ot,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),q=Ot,s(document).on(m.CLICK_DATA_API,g,function(t){t.preventDefault();t=t.target;s(t).hasClass("btn")||(t=s(t).closest(V)),q._jQueryInterface.call(s(t),"toggle")}).on(m.FOCUS_BLUR_DATA_API,g,function(t){var e=s(t.target).closest(V)[0];s(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),s.fn[F]=q._jQueryInterface,s.fn[F].Constructor=q,s.fn[F].noConflict=function(){return s.fn[F]=Q,q._jQueryInterface},q),g=(st="carousel",o="."+(at="bs.carousel"),lt=(c=e).fn[st],ht={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0},ct={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean"},ft="next",ut="prev",f={SLIDE:"slide"+o,SLID:"slid"+o,KEYDOWN:"keydown"+o,MOUSEENTER:"mouseenter"+o,MOUSELEAVE:"mouseleave"+o,TOUCHEND:"touchend"+o,LOAD_DATA_API:"load"+o+".data-api",CLICK_DATA_API:"click"+o+".data-api"},u="active",dt=".active",pt=".active.carousel-item",gt=".carousel-item",mt=".carousel-item-next, .carousel-item-prev",_t=".carousel-indicators",m="[data-slide], [data-slide-to]",vt='[data-ride="carousel"]',(g=St.prototype).next=function(){this._isSliding||this._slide(ft)},g.nextWhenVisible=function(){!document.hidden&&c(this._element).is(":visible")&&"hidden"!==c(this._element).css("visibility")&&this.next()},g.prev=function(){this._isSliding||this._slide(ut)},g.pause=function(t){t||(this._isPaused=!0),c(this._element).find(mt)[0]&&d.supportsTransitionEnd()&&(d.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},g.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},g.to=function(t){var e=this,n=(this._activeElement=c(this._element).find(pt)[0],this._getItemIndex(this._activeElement));t>this._items.length-1||t<0||(this._isSliding?c(this._element).one(f.SLID,function(){return e.to(t)}):n===t?(this.pause(),this.cycle()):this._slide(n<t?ft:ut,this._items[t]))},g.dispose=function(){c(this._element).off(o),c.removeData(this._element,at),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},g._getConfig=function(t){return t=r({},ht,t),d.typeCheckConfig(st,t,ct),t},g._addEventListeners=function(){var e=this;this._config.keyboard&&c(this._element).on(f.KEYDOWN,function(t){return e._keydown(t)}),"hover"===this._config.pause&&(c(this._element).on(f.MOUSEENTER,function(t){return e.pause(t)}).on(f.MOUSELEAVE,function(t){return e.cycle(t)}),"ontouchstart"in document.documentElement)&&c(this._element).on(f.TOUCHEND,function(){e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout(function(t){return e.cycle(t)},500+e._config.interval)})},g._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},g._getItemIndex=function(t){return this._items=c.makeArray(c(t).parent().find(gt)),this._items.indexOf(t)},g._getItemByDirection=function(t,e){var n=t===ft,i=t===ut,o=this._getItemIndex(e),r=this._items.length-1;return(i&&0===o||n&&o===r)&&!this._config.wrap?e:-1==(i=(o+(t===ut?-1:1))%this._items.length)?this._items[this._items.length-1]:this._items[i]},g._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(c(this._element).find(pt)[0]),t=c.Event(f.SLIDE,{relatedTarget:t,direction:e,from:i,to:n});return c(this._element).trigger(t),t},g._setActiveIndicatorElement=function(t){this._indicatorsElement&&(c(this._indicatorsElement).find(dt).removeClass(u),t=this._indicatorsElement.children[this._getItemIndex(t)])&&c(t).addClass(u)},g._slide=function(t,e){var n,i,o,r=this,s=c(this._element).find(pt)[0],a=this._getItemIndex(s),l=e||s&&this._getItemByDirection(t,s),e=this._getItemIndex(l),h=Boolean(this._interval),t=t===ft?(n="carousel-item-left",i="carousel-item-next","left"):(n="carousel-item-right",i="carousel-item-prev","right");l&&c(l).hasClass(u)?this._isSliding=!1:!this._triggerSlideEvent(l,t).isDefaultPrevented()&&s&&l&&(this._isSliding=!0,h&&this.pause(),this._setActiveIndicatorElement(l),o=c.Event(f.SLID,{relatedTarget:l,direction:t,from:a,to:e}),d.supportsTransitionEnd()&&c(this._element).hasClass("slide")?(c(l).addClass(i),d.reflow(l),c(s).addClass(n),c(l).addClass(n),c(s).one(d.TRANSITION_END,function(){c(l).removeClass(n+" "+i).addClass(u),c(s).removeClass(u+" "+i+" "+n),r._isSliding=!1,setTimeout(function(){return c(r._element).trigger(o)},0)}).emulateTransitionEnd(600)):(c(s).removeClass(u),c(l).addClass(u),this._isSliding=!1,c(this._element).trigger(o)),h)&&this.cycle()},St._jQueryInterface=function(i){return this.each(function(){var t=c(this).data(at),e=r({},ht,c(this).data()),n=("object"==typeof i&&(e=r({},e,i)),"string"==typeof i?i:e.slide);if(t||(t=new St(this,e),c(this).data(at,t)),"number"==typeof i)t.to(i);else if("string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}else e.interval&&(t.pause(),t.cycle())})},St._dataApiClickHandler=function(t){var e,n,i=d.getSelectorFromElement(this);i&&(i=c(i)[0])&&c(i).hasClass("carousel")&&(e=r({},c(i).data(),c(this).data()),(n=this.getAttribute("data-slide-to"))&&(e.interval=!1),St._jQueryInterface.call(c(i),e),n&&c(i).data(at).to(n),t.preventDefault())},n(St,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return ht}}]),Et=St,c(document).on(f.CLICK_DATA_API,m,Et._dataApiClickHandler),c(window).on(f.LOAD_DATA_API,function(){c(vt).each(function(){var t=c(this);Et._jQueryInterface.call(t,t.data())})}),c.fn[st]=Et._jQueryInterface,c.fn[st].Constructor=Et,c.fn[st].noConflict=function(){return c.fn[st]=lt,Et._jQueryInterface},Et),m=(z="collapse",m="."+(G="bs.collapse"),Z=(a=e).fn[z],J={toggle:!0,parent:""},$={toggle:"boolean",parent:"(string|element)"},X={SHOW:"show"+m,SHOWN:"shown"+m,HIDE:"hide"+m,HIDDEN:"hidden"+m,CLICK_DATA_API:"click"+m+".data-api"},l="show",tt="collapse",et="collapsing",nt="collapsed",it=".show, .collapsing",ot='[data-toggle="collapse"]',(m=At.prototype).toggle=function(){a(this._element).hasClass(l)?this.hide():this.show()},m.show=function(){var t,e,n,i,o=this;this._isTransitioning||a(this._element).hasClass(l)||(t=this._parent&&0===(t=a.makeArray(a(this._parent).find(it).filter('[data-parent="'+this._config.parent+'"]'))).length?null:t)&&(i=a(t).not(this._selector).data(G))&&i._isTransitioning||(n=a.Event(X.SHOW),a(this._element).trigger(n),n.isDefaultPrevented()||(t&&(At._jQueryInterface.call(a(t).not(this._selector),"hide"),i||a(t).data(G,null)),e=this._getDimension(),a(this._element).removeClass(tt).addClass(et),(this._element.style[e]=0)<this._triggerArray.length&&a(this._triggerArray).removeClass(nt).attr("aria-expanded",!0),this.setTransitioning(!0),n=function(){a(o._element).removeClass(et).addClass(tt).addClass(l),o._element.style[e]="",o.setTransitioning(!1),a(o._element).trigger(X.SHOWN)},d.supportsTransitionEnd()?(i="scroll"+(e[0].toUpperCase()+e.slice(1)),a(this._element).one(d.TRANSITION_END,n).emulateTransitionEnd(600),this._element.style[e]=this._element[i]+"px"):n()))},m.hide=function(){var t=this;if(!this._isTransitioning&&a(this._element).hasClass(l)){var e=a.Event(X.HIDE);if(a(this._element).trigger(e),!e.isDefaultPrevented()){e=this._getDimension();if(this._element.style[e]=this._element.getBoundingClientRect()[e]+"px",d.reflow(this._element),a(this._element).addClass(et).removeClass(tt).removeClass(l),0<this._triggerArray.length)for(var n=0;n<this._triggerArray.length;n++){var i=this._triggerArray[n],o=d.getSelectorFromElement(i);null===o||a(o).hasClass(l)||a(i).addClass(nt).attr("aria-expanded",!1)}this.setTransitioning(!0);var r=function(){t.setTransitioning(!1),a(t._element).removeClass(et).addClass(tt).trigger(X.HIDDEN)};this._element.style[e]="",d.supportsTransitionEnd()?a(this._element).one(d.TRANSITION_END,r).emulateTransitionEnd(600):r()}}},m.setTransitioning=function(t){this._isTransitioning=t},m.dispose=function(){a.removeData(this._element,G),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},m._getConfig=function(t){return(t=r({},J,t)).toggle=Boolean(t.toggle),d.typeCheckConfig(z,t,$),t},m._getDimension=function(){return a(this._element).hasClass("width")?"width":"height"},m._getParent=function(){var n=this,t=null,e=(d.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=a(this._config.parent)[0],'[data-toggle="collapse"][data-parent="'+this._config.parent+'"]');return a(t).find(e).each(function(t,e){n._addAriaAndCollapsedClass(At._getTargetFromElement(e),[e])}),t},m._addAriaAndCollapsedClass=function(t,e){t&&(t=a(t).hasClass(l),0<e.length)&&a(e).toggleClass(nt,!t).attr("aria-expanded",t)},At._getTargetFromElement=function(t){t=d.getSelectorFromElement(t);return t?a(t)[0]:null},At._jQueryInterface=function(i){return this.each(function(){var t=a(this),e=t.data(G),n=r({},J,t.data(),"object"==typeof i&&i);if(!e&&n.toggle&&/show|hide/.test(i)&&(n.toggle=!1),e||(e=new At(this,n),t.data(G,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},n(At,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return J}}]),rt=At,a(document).on(X.CLICK_DATA_API,ot,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var n=a(this),t=d.getSelectorFromElement(this);a(t).each(function(){var t=a(this),e=t.data(G)?"toggle":n.data();rt._jQueryInterface.call(t,e)})}),a.fn[z]=rt._jQueryInterface,a.fn[z].Constructor=rt,a.fn[z].noConflict=function(){return a.fn[z]=Z,rt._jQueryInterface},rt),Ct="undefined"!=typeof window&&"undefined"!=typeof document,Tt=["Edge","Trident","Firefox"],It=0,Dt=0;Dt<Tt.length;Dt+=1)if(Ct&&0<=navigator.userAgent.indexOf(Tt[Dt])){It=1;break}function At(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=a.makeArray(a('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=a(ot),i=0;i<n.length;i++){var o=n[i],r=d.getSelectorFromElement(o);null!==r&&0<a(r).filter(t).length&&(this._selector=r,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}function St(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this._config=this._getConfig(e),this._element=c(t)[0],this._indicatorsElement=c(this._element).find(_t)[0],this._addEventListeners()}function Ot(t){this._element=t}function Nt(t){this._element=t}var kt=Ct&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then(function(){e=!1,t()}))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},It))}};function Pt(t){return t&&"[object Function]"==={}.toString.call(t)}function xt(t,e){return 1!==t.nodeType?[]:(t=getComputedStyle(t,null),e?t[e]:t)}function Lt(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function jt(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=xt(t),n=e.overflow,i=e.overflowX,e=e.overflowY;return/(auto|scroll)/.test(n+e+i)?t:jt(Lt(t))}function Ht(t){var e=t&&t.offsetParent,n=e&&e.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TD","TABLE"].indexOf(e.nodeName)&&"static"===xt(e,"position")?Ht(e):e:(t?t.ownerDocument:document).documentElement}function Wt(t){return null!==t.parentNode?Wt(t.parentNode):t}function Mt(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?t:e,n=n?e:t,o=document.createRange();o.setStart(i,0),o.setEnd(n,0);o=o.commonAncestorContainer;return t!==o&&e!==o||i.contains(n)?"BODY"===(n=(i=o).nodeName)||"HTML"!==n&&Ht(i.firstElementChild)!==i?Ht(o):o:(n=Wt(t)).host?Mt(n.host,e):Mt(t,Wt(e).host)}function Rt(t,e){var e="top"===(1<arguments.length&&void 0!==e?e:"top")?"scrollTop":"scrollLeft",n=t.nodeName;return("BODY"===n||"HTML"===n?(n=t.ownerDocument.documentElement,t.ownerDocument.scrollingElement||n):t)[e]}function Ut(t,e){var e="x"===e?"Left":"Top",n="Left"==e?"Right":"Bottom";return parseFloat(t["border"+e+"Width"],10)+parseFloat(t["border"+n+"Width"],10)}var Bt=void 0,Ft=function(){return Bt=void 0===Bt?-1!==navigator.appVersion.indexOf("MSIE 10"):Bt};function Kt(t,e,n,i){return Math.max(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],Ft()?n["offset"+t]+i["margin"+("Height"===t?"Top":"Left")]+i["margin"+("Height"===t?"Bottom":"Right")]:0)}function Qt(){var t=document.body,e=document.documentElement,n=Ft()&&getComputedStyle(e);return{height:Kt("Height",t,e,n),width:Kt("Width",t,e,n)}}function Yt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var _=function(t,e,n){return e&&Vt(t.prototype,e),n&&Vt(t,n),t},v=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n,i=arguments[e];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t};function Vt(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function qt(t){return v({},t,{right:t.left+t.width,bottom:t.top+t.height})}function zt(t){var e={};if(Ft())try{var e=t.getBoundingClientRect(),n=Rt(t,"top"),i=Rt(t,"left");e.top+=n,e.left+=i,e.bottom+=n,e.right+=i}catch(t){}else e=t.getBoundingClientRect();var o,n={left:e.left,top:e.top,width:e.right-e.left,height:e.bottom-e.top},i="HTML"===t.nodeName?Qt():{},e=i.width||t.clientWidth||n.right-n.left,i=i.height||t.clientHeight||n.bottom-n.top,e=t.offsetWidth-e,i=t.offsetHeight-i;return(e||i)&&(e-=Ut(o=xt(t),"x"),i-=Ut(o,"y"),n.width-=e,n.height-=i),qt(n)}function Gt(t,e){var n=Ft(),i="HTML"===e.nodeName,o=zt(t),r=zt(e),t=jt(t),s=xt(e),a=parseFloat(s.borderTopWidth,10),l=parseFloat(s.borderLeftWidth,10),r=qt({top:o.top-r.top-a,left:o.left-r.left-l,width:o.width,height:o.height});return r.marginTop=0,r.marginLeft=0,!n&&i&&(o=parseFloat(s.marginTop,10),i=parseFloat(s.marginLeft,10),r.top-=a-o,r.bottom-=a-o,r.left-=l-i,r.right-=l-i,r.marginTop=o,r.marginLeft=i),r=(n?e.contains(t):e===t&&"BODY"!==t.nodeName)?function(t,e,n){var n=2<arguments.length&&void 0!==n&&n,i=Rt(e,"top"),e=Rt(e,"left"),n=n?-1:1;return t.top+=i*n,t.bottom+=i*n,t.left+=e*n,t.right+=e*n,t}(r,e):r}function Zt(t,e,n,i){var o,r,s,a,l,h={top:0,left:0},c=Mt(t,e);return"viewport"===i?(s=Gt(c,r=c.ownerDocument.documentElement),a=Math.max(r.clientWidth,window.innerWidth||0),l=Math.max(r.clientHeight,window.innerHeight||0),o=Rt(r),r=Rt(r,"left"),h=qt({top:o-s.top+s.marginTop,left:r-s.left+s.marginLeft,width:a,height:l})):(o=void 0,"scrollParent"===i?"BODY"===(o=jt(Lt(e))).nodeName&&(o=t.ownerDocument.documentElement):o="window"===i?t.ownerDocument.documentElement:i,r=Gt(o,c),"HTML"!==o.nodeName||function t(e){var n=e.nodeName;return"BODY"!==n&&"HTML"!==n&&("fixed"===xt(e,"position")||t(Lt(e)))}(c)?h=r:(a=(s=Qt()).height,l=s.width,h.top+=r.top-r.marginTop,h.bottom=a+r.top,h.left+=r.left-r.marginLeft,h.right=l+r.left)),h.left+=n,h.top+=n,h.right-=n,h.bottom-=n,h}function Jt(t,e,n,i,o,r){var s,r=5<arguments.length&&void 0!==r?r:0;return-1===t.indexOf("auto")?t:(i=Zt(n,i,r,o),s={top:{width:i.width,height:e.top-i.top},right:{width:i.right-e.right,height:i.height},bottom:{width:i.width,height:i.bottom-e.bottom},left:{width:e.left-i.left,height:i.height}},(0<(o=(r=Object.keys(s).map(function(t){return v({key:t},s[t],{area:(t=s[t]).width*t.height})}).sort(function(t,e){return e.area-t.area})).filter(function(t){var e=t.width,t=t.height;return e>=n.clientWidth&&t>=n.clientHeight})).length?o:r)[0].key+((e=t.split("-")[1])?"-"+e:""))}function $t(t,e,n){return Gt(n,Mt(e,n))}function Xt(t){var e=getComputedStyle(t),n=parseFloat(e.marginTop)+parseFloat(e.marginBottom),e=parseFloat(e.marginLeft)+parseFloat(e.marginRight);return{width:t.offsetWidth+e,height:t.offsetHeight+n}}function te(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function ee(t,e,n){n=n.split("-")[0];var t=Xt(t),i={width:t.width,height:t.height},o=-1!==["right","left"].indexOf(n),r=o?"top":"left",s=o?"left":"top",a=o?"height":"width",o=o?"width":"height";return i[r]=e[r]+e[a]/2-t[a]/2,i[s]=n===s?e[s]-t[o]:e[te(s)],i}function ne(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function ie(t,n,e){return(void 0===e?t:t.slice(0,(t=t,i=e,Array.prototype.findIndex?t.findIndex(function(t){return t.name===i}):(e=ne(t,function(t){return t.name===i}),t.indexOf(e))))).forEach(function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var e=t.function||t.fn;t.enabled&&Pt(e)&&(n.offsets.popper=qt(n.offsets.popper),n.offsets.reference=qt(n.offsets.reference),n=e(n,t))}),n;var i}function oe(t,n){return t.some(function(t){var e=t.name;return t.enabled&&e===n})}function re(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),i=0;i<e.length-1;i++){var o=e[i],o=o?""+o+n:t;if(void 0!==document.body.style[o])return o}return null}function se(t){t=t.ownerDocument;return t?t.defaultView:window}function ae(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function le(n,i){Object.keys(i).forEach(function(t){var e="";-1!==["width","height","top","right","bottom","left"].indexOf(t)&&ae(i[t])&&(e="px"),n.style[t]=i[t]+e})}function he(t,e,n){var i,o=ne(t,function(t){return t.name===e}),t=!!o&&t.some(function(t){return t.name===n&&t.enabled&&t.order<o.order});return t||(i="`"+e+"`",console.warn("`"+n+"`"+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")),t}var ce=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],fe=ce.slice(3);function ue(t,e){e=1<arguments.length&&void 0!==e&&e,t=fe.indexOf(t),t=fe.slice(t+1).concat(fe.slice(0,t));return e?t.reverse():t}var de="flip",pe="clockwise",ge="counterclockwise";function me(t,a,l,e){var o=[0,0],i=-1!==["right","left"].indexOf(e),e=t.split(/(\+|\-)/).map(function(t){return t.trim()}),t=e.indexOf(ne(e,function(t){return-1!==t.search(/,|\s/)})),n=(e[t]&&-1===e[t].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead."),/\s*,\s*|\s+/);return(-1!==t?[e.slice(0,t).concat([e[t].split(n)[0]]),[e[t].split(n)[1]].concat(e.slice(t+1))]:[e]).map(function(t,e){var s=(1===e?!i:i)?"height":"width",n=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,n=!0,t):n?(t[t.length-1]+=e,n=!1,t):t.concat(e)},[]).map(function(t){return e=s,n=a,i=l,o=(t=t).match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+o[1],o=o[2],r?0===o.indexOf("%")?qt("%p"===o?n:i)[e]/100*r:"vh"===o||"vw"===o?("vh"===o?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*r:r:t;var e,n,i,o,r})}).forEach(function(n,i){n.forEach(function(t,e){ae(t)&&(o[i]+=t*("-"===n[e-1]?-1:1))})}),o}var E={placement:"bottom",eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e,n,i,o=t.placement,r=o.split("-")[0],o=o.split("-")[1];return o&&(e=(n=t.offsets).reference,n=n.popper,i=(r=-1!==["bottom","top"].indexOf(r))?"width":"height",r={start:Yt({},r=r?"left":"top",e[r]),end:Yt({},r,e[r]+e[i]-n[i])},t.offsets.popper=v({},n,r[o])),t}},offset:{order:200,enabled:!0,fn:function(t,e){var e=e.offset,n=t.placement,i=t.offsets,o=i.popper,i=i.reference,n=n.split("-")[0],e=ae(+e)?[+e,0]:me(e,o,i,n);return"left"===n?(o.top+=e[0],o.left-=e[1]):"right"===n?(o.top+=e[0],o.left+=e[1]):"top"===n?(o.left+=e[0],o.top-=e[1]):"bottom"===n&&(o.left+=e[0],o.top+=e[1]),t.popper=o,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,i){var e=i.boundariesElement||Ht(t.instance.popper),o=(t.instance.reference===e&&(e=Ht(e)),Zt(t.instance.popper,t.instance.reference,i.padding,e)),e=(i.boundaries=o,i.priority),r=t.offsets.popper,n={primary:function(t){var e=r[t];return r[t]<o[t]&&!i.escapeWithReference&&(e=Math.max(r[t],o[t])),Yt({},t,e)},secondary:function(t){var e="right"===t?"left":"top",n=r[e];return r[t]>o[t]&&!i.escapeWithReference&&(n=Math.min(r[e],o[t]-("right"===t?r.width:r.height))),Yt({},e,n)}};return e.forEach(function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";r=v({},r,n[e](t))}),t.offsets.popper=r,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,e=e.reference,i=t.placement.split("-")[0],o=Math.floor,i=-1!==["top","bottom"].indexOf(i),r=i?"right":"bottom",s=i?"left":"top",i=i?"width":"height";return n[r]<o(e[s])&&(t.offsets.popper[s]=o(e[s])-n[i]),n[s]>o(e[r])&&(t.offsets.popper[s]=o(e[r])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){if(he(t.instance.modifiers,"arrow","keepTogether")){e=e.element;if("string"==typeof e){if(!(e=t.instance.popper.querySelector(e)))return t}else if(!t.instance.popper.contains(e))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var n=t.placement.split("-")[0],i=t.offsets,o=i.popper,i=i.reference,n=-1!==["left","right"].indexOf(n),r=n?"height":"width",s=n?"Top":"Left",a=s.toLowerCase(),l=n?"left":"top",n=n?"bottom":"right",h=Xt(e)[r],n=(i[n]-h<o[a]&&(t.offsets.popper[a]-=o[a]-(i[n]-h)),i[a]+h>o[n]&&(t.offsets.popper[a]+=i[a]+h-o[n]),t.offsets.popper=qt(t.offsets.popper),i[a]+i[r]/2-h/2),i=xt(t.instance.popper),c=parseFloat(i["margin"+s],10),i=parseFloat(i["border"+s+"Width"],10),s=n-t.offsets.popper[a]-c-i,s=Math.max(Math.min(o[r]-h,s),0);t.arrowElement=e,t.offsets.arrow=(Yt(n={},a,Math.round(s)),Yt(n,l,""),n)}return t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(l,h){if(!(oe(l.instance.modifiers,"inner")||l.flipped&&l.placement===l.originalPlacement)){var c=Zt(l.instance.popper,l.instance.reference,h.padding,h.boundariesElement),f=l.placement.split("-")[0],u=te(f),d=l.placement.split("-")[1]||"",p=[];switch(h.behavior){case de:p=[f,u];break;case pe:p=ue(f);break;case ge:p=ue(f,!0);break;default:p=h.behavior}p.forEach(function(t,e){if(f!==t||p.length===e+1)return l;f=l.placement.split("-")[0],u=te(f);var t=l.offsets.popper,n=l.offsets.reference,i=Math.floor,n="left"===f&&i(t.right)>i(n.left)||"right"===f&&i(t.left)<i(n.right)||"top"===f&&i(t.bottom)>i(n.top)||"bottom"===f&&i(t.top)<i(n.bottom),o=i(t.left)<i(c.left),r=i(t.right)>i(c.right),s=i(t.top)<i(c.top),t=i(t.bottom)>i(c.bottom),i="left"===f&&o||"right"===f&&r||"top"===f&&s||"bottom"===f&&t,a=-1!==["top","bottom"].indexOf(f),o=!!h.flipVariations&&(a&&"start"===d&&o||a&&"end"===d&&r||!a&&"start"===d&&s||!a&&"end"===d&&t);(n||i||o)&&(l.flipped=!0,(n||i)&&(f=p[e+1]),o&&(d="end"===d?"start":"start"===d?"end":d),l.placement=f+(d?"-"+d:""),l.offsets.popper=v({},l.offsets.popper,ee(l.instance.popper,l.offsets.reference,l.placement)),l=ie(l.instance.modifiers,l,"flip"))})}return l},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],i=t.offsets,o=i.popper,i=i.reference,r=-1!==["left","right"].indexOf(n),s=-1===["top","left"].indexOf(n);return o[r?"left":"top"]=i[n]-(s?o[r?"width":"height"]:0),t.placement=te(e),t.offsets.popper=qt(o),t}},hide:{order:800,enabled:!0,fn:function(t){if(he(t.instance.modifiers,"hide","preventOverflow")){var e=t.offsets.reference,n=ne(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,i=e.y,o=t.offsets.popper,r=ne(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration,r=(void 0!==r&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!"),void 0!==r?r:e.gpuAcceleration),e=zt(Ht(t.instance.popper)),s={position:o.position},o={left:Math.floor(o.left),top:Math.floor(o.top),bottom:Math.floor(o.bottom),right:Math.floor(o.right)},n="bottom"===n?"top":"bottom",i="right"===i?"left":"right",a=re("transform"),l="bottom"==n?-e.height+o.bottom:o.top,e="right"==i?-e.width+o.right:o.left,r=(r&&a?(s[a]="translate3d("+e+"px, "+l+"px, 0)",s[n]=0,s[i]=0,s.willChange="transform"):(o="right"==i?-1:1,s[n]=l*("bottom"==n?-1:1),s[i]=e*o,s.willChange=n+", "+i),{"x-placement":t.placement});return t.attributes=v({},r,t.attributes),t.styles=v({},s,t.styles),t.arrowStyles=v({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,n;return le(t.instance.popper,t.styles),e=t.instance.popper,n=t.attributes,Object.keys(n).forEach(function(t){!1!==n[t]?e.setAttribute(t,n[t]):e.removeAttribute(t)}),t.arrowElement&&Object.keys(t.arrowStyles).length&&le(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,i,o){var r=$t(0,e,t),r=Jt(n.placement,r,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",r),le(e,{position:"absolute"}),n},gpuAcceleration:void 0}}},_e=(_(ve,[{key:"update",value:function(){return function(){var t;this.state.isDestroyed||((t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=$t(this.state,this.popper,this.reference),t.placement=Jt(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.offsets.popper=ee(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position="absolute",t=ie(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,oe(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.left="",this.popper.style.position="",this.popper.style.top="",this.popper.style[re("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){var t,e,n;this.state.eventsEnabled||(this.state=(t=this.reference,this.options,e=this.state,n=this.scheduleUpdate,e.updateBound=n,se(t).addEventListener("resize",e.updateBound,{passive:!0}),function t(e,n,i,o){var r="BODY"===e.nodeName,e=r?e.ownerDocument.defaultView:e;e.addEventListener(n,i,{passive:!0}),r||t(jt(e.parentNode),n,i,o),o.push(e)}(n=jt(t),"scroll",e.updateBound,e.scrollParents),e.scrollElement=n,e.eventsEnabled=!0,e))}.call(this)}},{key:"disableEventListeners",value:function(){return function(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,se(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}.call(this)}}]),ve);function ve(t,e){var n=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},o=this,r=ve;if(!(o instanceof r))throw new TypeError("Cannot call a class as a function");this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=kt(this.update.bind(this)),this.options=v({},ve.Defaults,i),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=e&&e.jquery?e[0]:e,this.options.modifiers={},Object.keys(v({},ve.Defaults.modifiers,i.modifiers)).forEach(function(t){n.options.modifiers[t]=v({},ve.Defaults.modifiers[t]||{},i.modifiers?i.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return v({name:t},n.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(t){t.enabled&&Pt(t.onLoad)&&t.onLoad(n.reference,n.popper,n.options,t,n.state)}),this.update();o=this.options.eventsEnabled;o&&this.enableEventListeners(),this.state.eventsEnabled=o}_e.Utils=("undefined"!=typeof window?window:global).PopperUtils,_e.placements=ce,_e.Defaults=E;An="dropdown",N="."+(Sn="bs.dropdown"),On=(O=e).fn[An],Nn=new RegExp("38|40|27"),k={HIDE:"hide"+N,HIDDEN:"hidden"+N,SHOW:"show"+N,SHOWN:"shown"+N,CLICK:"click"+N,CLICK_DATA_API:"click"+N+".data-api",KEYDOWN_DATA_API:"keydown"+N+".data-api",KEYUP_DATA_API:"keyup"+N+".data-api"},kn="disabled",Pn="show",xn="dropdown-menu-right",Ln='[data-toggle="dropdown"]',jn=".dropdown-menu",Hn={offset:0,flip:!0,boundary:"scrollParent"},Wn={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)"},(_=j.prototype).toggle=function(){if(!this._element.disabled&&!O(this._element).hasClass(kn)){var t=j._getParentFromElement(this._element),e=O(this._menu).hasClass(Pn);if(j._clearMenus(),!e){var e={relatedTarget:this._element},n=O.Event(k.SHOW,e);if(O(t).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar){if(void 0===_e)throw new TypeError("Bootstrap dropdown require Popper.js (https://popper.js.org)");n=this._element;O(t).hasClass("dropup")&&(O(this._menu).hasClass("dropdown-menu-left")||O(this._menu).hasClass(xn))&&(n=t),"scrollParent"!==this._config.boundary&&O(t).addClass("position-static"),this._popper=new _e(n,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===O(t).closest(".navbar-nav").length&&O("body").children().on("mouseover",null,O.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),O(this._menu).toggleClass(Pn),O(t).toggleClass(Pn).trigger(O.Event(k.SHOWN,e))}}}},_.dispose=function(){O.removeData(this._element,Sn),O(this._element).off(N),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},_.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},_._addEventListeners=function(){var e=this;O(this._element).on(k.CLICK,function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},_._getConfig=function(t){return t=r({},this.constructor.Default,O(this._element).data(),t),d.typeCheckConfig(An,t,this.constructor.DefaultType),t},_._getMenuElement=function(){var t;return this._menu||(t=j._getParentFromElement(this._element),this._menu=O(t).find(jn)[0]),this._menu},_._getPlacement=function(){var t=O(this._element).parent(),e="bottom-start";return t.hasClass("dropup")?(e="top-start",O(this._menu).hasClass(xn)&&(e="top-end")):t.hasClass("dropright")?e="right-start":t.hasClass("dropleft")?e="left-start":O(this._menu).hasClass(xn)&&(e="bottom-end"),e},_._detectNavbar=function(){return 0<O(this._element).closest(".navbar").length},_._getPopperConfig=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=r({},t.offsets,e._config.offset(t.offsets)||{}),t}:t.offset=this._config.offset,{placement:this._getPlacement(),modifiers:{offset:t,flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}}},j._jQueryInterface=function(e){return this.each(function(){var t=O(this).data(Sn);if(t||(t=new j(this,"object"==typeof e?e:null),O(this).data(Sn,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},j._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=O.makeArray(O(Ln)),n=0;n<e.length;n++){var i,o=j._getParentFromElement(e[n]),r=O(e[n]).data(Sn),s={relatedTarget:e[n]};r&&(r=r._menu,!O(o).hasClass(Pn)||t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&O.contains(o,t.target)||(i=O.Event(k.HIDE,s),O(o).trigger(i),i.isDefaultPrevented())||("ontouchstart"in document.documentElement&&O("body").children().off("mouseover",null,O.noop),e[n].setAttribute("aria-expanded","false"),O(r).removeClass(Pn),O(o).removeClass(Pn).trigger(O.Event(k.HIDDEN,s))))}},j._getParentFromElement=function(t){var e,n=d.getSelectorFromElement(t);return(e=n?O(n)[0]:e)||t.parentNode},j._dataApiKeydownHandler=function(t){var e,n,i;(/input|textarea/i.test(t.target.tagName)?32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||O(t.target).closest(jn).length):!Nn.test(t.which))||(t.preventDefault(),t.stopPropagation(),this.disabled)||O(this).hasClass(kn)||(e=j._getParentFromElement(this),((i=O(e).hasClass(Pn))||27===t.which&&32===t.which)&&(!i||27!==t.which&&32!==t.which)?0!==(i=O(e).find(".dropdown-menu .dropdown-item:not(.disabled)").get()).length&&(n=i.indexOf(t.target),38===t.which&&0<n&&n--,40===t.which&&n<i.length-1&&n++,i[n=n<0?0:n].focus()):(27===t.which&&(i=O(e).find(Ln)[0],O(i).trigger("focus")),O(this).trigger("click")))},n(j,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return Hn}},{key:"DefaultType",get:function(){return Wn}}]),P=j,O(document).on(k.KEYDOWN_DATA_API,Ln,P._dataApiKeydownHandler).on(k.KEYDOWN_DATA_API,jn,P._dataApiKeydownHandler).on(k.CLICK_DATA_API+" "+k.KEYUP_DATA_API,P._clearMenus).on(k.CLICK_DATA_API,Ln,function(t){t.preventDefault(),t.stopPropagation(),P._jQueryInterface.call(O(this),"toggle")}).on(k.CLICK_DATA_API,".dropdown form",function(t){t.stopPropagation()}),O.fn[An]=P._jQueryInterface,O.fn[An].Constructor=P,O.fn[An].noConflict=function(){return O.fn[An]=On,P._jQueryInterface};var b,Ee,be,ye,we,Ce,Te,y,Ie,De,Ae,Se,Oe,Ne,ke,Pe,xe,Le,je,He,We,Me,Re,Ue,Be,Fe,Ke,w,Qe,Ye,Ve,C,qe,ze,Ge,Ze,Je,$e,T,Xe,tn,I,en,nn,on,rn,sn,an,ln,hn,cn,fn,un,dn,D,pn,A,gn,mn,_n,S,vn,En,bn,yn,wn,Cn,Tn,In,Dn,O,An,Sn,N,On,Nn,k,kn,Pn,xn,Ln,jn,Hn,Wn,P,ce=P,_=(A="."+(pn="bs.modal"),gn=(D=e).fn.modal,mn={backdrop:!0,keyboard:!0,focus:!0,show:!0},_n={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},S={HIDE:"hide"+A,HIDDEN:"hidden"+A,SHOW:"show"+A,SHOWN:"shown"+A,FOCUSIN:"focusin"+A,RESIZE:"resize"+A,CLICK_DISMISS:"click.dismiss"+A,KEYDOWN_DISMISS:"keydown.dismiss"+A,MOUSEUP_DISMISS:"mouseup.dismiss"+A,MOUSEDOWN_DISMISS:"mousedown.dismiss"+A,CLICK_DATA_API:"click.bs.modal.data-api"},vn="modal-open",En="fade",bn="show",yn=".modal-dialog",E='[data-toggle="modal"]',wn='[data-dismiss="modal"]',Cn=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Tn=".sticky-top",In=".navbar-toggler",(_=Kn.prototype).toggle=function(t){return this._isShown?this.hide():this.show(t)},_.show=function(t){var e,n=this;this._isTransitioning||this._isShown||(d.supportsTransitionEnd()&&D(this._element).hasClass(En)&&(this._isTransitioning=!0),e=D.Event(S.SHOW,{relatedTarget:t}),D(this._element).trigger(e),this._isShown)||e.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),D(document.body).addClass(vn),this._setEscapeEvent(),this._setResizeEvent(),D(this._element).on(S.CLICK_DISMISS,wn,function(t){return n.hide(t)}),D(this._dialog).on(S.MOUSEDOWN_DISMISS,function(){D(n._element).one(S.MOUSEUP_DISMISS,function(t){D(t.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(t)}))},_.hide=function(t){var e=this;t&&t.preventDefault(),!this._isTransitioning&&this._isShown&&(t=D.Event(S.HIDE),D(this._element).trigger(t),this._isShown)&&!t.isDefaultPrevented()&&(this._isShown=!1,(t=d.supportsTransitionEnd()&&D(this._element).hasClass(En))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),D(document).off(S.FOCUSIN),D(this._element).removeClass(bn),D(this._element).off(S.CLICK_DISMISS),D(this._dialog).off(S.MOUSEDOWN_DISMISS),t?D(this._element).one(d.TRANSITION_END,function(t){return e._hideModal(t)}).emulateTransitionEnd(300):this._hideModal())},_.dispose=function(){D.removeData(this._element,pn),D(window,document,this._element,this._backdrop).off(A),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._scrollbarWidth=null},_.handleUpdate=function(){this._adjustDialog()},_._getConfig=function(t){return t=r({},mn,t),d.typeCheckConfig("modal",t,_n),t},_._showElement=function(t){function e(){n._config.focus&&n._element.focus(),n._isTransitioning=!1,D(n._element).trigger(o)}var n=this,i=d.supportsTransitionEnd()&&D(this._element).hasClass(En),o=(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.scrollTop=0,i&&d.reflow(this._element),D(this._element).addClass(bn),this._config.focus&&this._enforceFocus(),D.Event(S.SHOWN,{relatedTarget:t}));i?D(this._dialog).one(d.TRANSITION_END,e).emulateTransitionEnd(300):e()},_._enforceFocus=function(){var e=this;D(document).off(S.FOCUSIN).on(S.FOCUSIN,function(t){document!==t.target&&e._element!==t.target&&0===D(e._element).has(t.target).length&&e._element.focus()})},_._setEscapeEvent=function(){var e=this;this._isShown&&this._config.keyboard?D(this._element).on(S.KEYDOWN_DISMISS,function(t){27===t.which&&(t.preventDefault(),e.hide())}):this._isShown||D(this._element).off(S.KEYDOWN_DISMISS)},_._setResizeEvent=function(){var e=this;this._isShown?D(window).on(S.RESIZE,function(t){return e.handleUpdate(t)}):D(window).off(S.RESIZE)},_._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._isTransitioning=!1,this._showBackdrop(function(){D(document.body).removeClass(vn),t._resetAdjustments(),t._resetScrollbar(),D(t._element).trigger(S.HIDDEN)})},_._removeBackdrop=function(){this._backdrop&&(D(this._backdrop).remove(),this._backdrop=null)},_._showBackdrop=function(t){var e,n=this,i=D(this._element).hasClass(En)?En:"";this._isShown&&this._config.backdrop?(e=d.supportsTransitionEnd()&&i,this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",i&&D(this._backdrop).addClass(i),D(this._backdrop).appendTo(document.body),D(this._element).on(S.CLICK_DISMISS,function(t){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"===n._config.backdrop?n._element.focus():n.hide())}),e&&d.reflow(this._backdrop),D(this._backdrop).addClass(bn),t&&(e?D(this._backdrop).one(d.TRANSITION_END,t).emulateTransitionEnd(150):t())):!this._isShown&&this._backdrop?(D(this._backdrop).removeClass(bn),i=function(){n._removeBackdrop(),t&&t()},d.supportsTransitionEnd()&&D(this._element).hasClass(En)?D(this._backdrop).one(d.TRANSITION_END,i).emulateTransitionEnd(150):i()):t&&t()},_._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},_._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},_._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},_._setScrollbar=function(){var t,e,o=this;this._isBodyOverflowing&&(D(Cn).each(function(t,e){var n=D(e)[0].style.paddingRight,i=D(e).css("padding-right");D(e).data("padding-right",n).css("padding-right",parseFloat(i)+o._scrollbarWidth+"px")}),D(Tn).each(function(t,e){var n=D(e)[0].style.marginRight,i=D(e).css("margin-right");D(e).data("margin-right",n).css("margin-right",parseFloat(i)-o._scrollbarWidth+"px")}),D(In).each(function(t,e){var n=D(e)[0].style.marginRight,i=D(e).css("margin-right");D(e).data("margin-right",n).css("margin-right",parseFloat(i)+o._scrollbarWidth+"px")}),t=document.body.style.paddingRight,e=D("body").css("padding-right"),D("body").data("padding-right",t).css("padding-right",parseFloat(e)+this._scrollbarWidth+"px"))},_._resetScrollbar=function(){D(Cn).each(function(t,e){var n=D(e).data("padding-right");void 0!==n&&D(e).css("padding-right",n).removeData("padding-right")}),D(Tn+", "+In).each(function(t,e){var n=D(e).data("margin-right");void 0!==n&&D(e).css("margin-right",n).removeData("margin-right")});var t=D("body").data("padding-right");void 0!==t&&D("body").css("padding-right",t).removeData("padding-right")},_._getScrollbarWidth=function(){var t=document.createElement("div"),e=(t.className="modal-scrollbar-measure",document.body.appendChild(t),t.getBoundingClientRect().width-t.clientWidth);return document.body.removeChild(t),e},Kn._jQueryInterface=function(n,i){return this.each(function(){var t=D(this).data(pn),e=r({},Kn.Default,D(this).data(),"object"==typeof n&&n);if(t||(t=new Kn(this,e),D(this).data(pn,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](i)}else e.show&&t.show(i)})},n(Kn,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return mn}}]),Dn=Kn,D(document).on(S.CLICK_DATA_API,E,function(t){var e,n=this,i=d.getSelectorFromElement(this),i=(i&&(e=D(i)[0]),D(e).data(pn)?"toggle":r({},D(e).data(),D(this).data())),o=("A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault(),D(e).one(S.SHOW,function(t){t.isDefaultPrevented()||o.one(S.HIDDEN,function(){D(n).is(":visible")&&n.focus()})}));Dn._jQueryInterface.call(D(e),i,this)}),D.fn.modal=Dn._jQueryInterface,D.fn.modal.Constructor=Dn,D.fn.modal.noConflict=function(){return D.fn.modal=gn,Dn._jQueryInterface},Dn),E=(Xe="tooltip",I="."+(tn="bs.tooltip"),en=(T=e).fn[Xe],nn=new RegExp("(^|\\s)bs-tooltip\\S+","g"),sn={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!(rn={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"}),selector:!(on={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)"}),placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent"},ln={HIDE:"hide"+I,HIDDEN:"hidden"+I,SHOW:(an="show")+I,SHOWN:"shown"+I,INSERTED:"inserted"+I,CLICK:"click"+I,FOCUSIN:"focusin"+I,FOCUSOUT:"focusout"+I,MOUSEENTER:"mouseenter"+I,MOUSELEAVE:"mouseleave"+I},hn="fade",cn="show",fn="hover",un="focus",(E=Fn.prototype).enable=function(){this._isEnabled=!0},E.disable=function(){this._isEnabled=!1},E.toggleEnabled=function(){this._isEnabled=!this._isEnabled},E.toggle=function(t){var e,n;this._isEnabled&&(t?(e=this.constructor.DATA_KEY,(n=T(t.currentTarget).data(e))||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),T(t.currentTarget).data(e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)):T(this.getTipElement()).hasClass(cn)?this._leave(null,this):this._enter(null,this))},E.dispose=function(){clearTimeout(this._timeout),T.removeData(this.element,this.constructor.DATA_KEY),T(this.element).off(this.constructor.EVENT_KEY),T(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&T(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,(this._activeTrigger=null)!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},E.show=function(){var e=this;if("none"===T(this.element).css("display"))throw new Error("Please use show on visible elements");var t,n,i=T.Event(this.constructor.Event.SHOW);this.isWithContent()&&this._isEnabled&&(T(this.element).trigger(i),t=T.contains(this.element.ownerDocument.documentElement,this.element),!i.isDefaultPrevented())&&t&&(i=this.getTipElement(),t=d.getUID(this.constructor.NAME),i.setAttribute("id",t),this.element.setAttribute("aria-describedby",t),this.setContent(),this.config.animation&&T(i).addClass(hn),t="function"==typeof this.config.placement?this.config.placement.call(this,i,this.element):this.config.placement,t=this._getAttachment(t),this.addAttachmentClass(t),n=!1===this.config.container?document.body:T(this.config.container),T(i).data(this.constructor.DATA_KEY,this),T.contains(this.element.ownerDocument.documentElement,this.tip)||T(i).appendTo(n),T(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new _e(this.element,i,{placement:t,modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){e._handlePopperPlacementChange(t)}}),T(i).addClass(cn),"ontouchstart"in document.documentElement&&T("body").children().on("mouseover",null,T.noop),n=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,T(e.element).trigger(e.constructor.Event.SHOWN),"out"===t&&e._leave(null,e)},d.supportsTransitionEnd()&&T(this.tip).hasClass(hn)?T(this.tip).one(d.TRANSITION_END,n).emulateTransitionEnd(Fn._TRANSITION_DURATION):n())},E.hide=function(t){function e(){n._hoverState!==an&&i.parentNode&&i.parentNode.removeChild(i),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),T(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),t&&t()}var n=this,i=this.getTipElement(),o=T.Event(this.constructor.Event.HIDE);T(this.element).trigger(o),o.isDefaultPrevented()||(T(i).removeClass(cn),"ontouchstart"in document.documentElement&&T("body").children().off("mouseover",null,T.noop),this._activeTrigger.click=!1,this._activeTrigger[un]=!1,this._activeTrigger[fn]=!1,d.supportsTransitionEnd()&&T(this.tip).hasClass(hn)?T(i).one(d.TRANSITION_END,e).emulateTransitionEnd(150):e(),this._hoverState="")},E.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},E.isWithContent=function(){return Boolean(this.getTitle())},E.addAttachmentClass=function(t){T(this.getTipElement()).addClass("bs-tooltip-"+t)},E.getTipElement=function(){return this.tip=this.tip||T(this.config.template)[0],this.tip},E.setContent=function(){var t=T(this.getTipElement());this.setElementContent(t.find(".tooltip-inner"),this.getTitle()),t.removeClass(hn+" "+cn)},E.setElementContent=function(t,e){var n=this.config.html;"object"==typeof e&&(e.nodeType||e.jquery)?n?T(e).parent().is(t)||t.empty().append(e):t.text(T(e).text()):t[n?"html":"text"](e)},E.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},E._getAttachment=function(t){return rn[t.toUpperCase()]},E._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(t){var e;"click"===t?T(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(t){return n.toggle(t)}):"manual"!==t&&(e=t===fn?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,t=t===fn?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,T(n.element).on(e,n.config.selector,function(t){return n._enter(t)}).on(t,n.config.selector,function(t){return n._leave(t)})),T(n.element).closest(".modal").on("hide.bs.modal",function(){return n.hide()})}),this.config.selector?this.config=r({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},E._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},E._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||T(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),T(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusin"===t.type?un:fn]=!0),T(e.getTipElement()).hasClass(cn)||e._hoverState===an?e._hoverState=an:(clearTimeout(e._timeout),e._hoverState=an,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===an&&e.show()},e.config.delay.show):e.show())},E._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||T(t.currentTarget).data(n))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),T(t.currentTarget).data(n,e)),t&&(e._activeTrigger["focusout"===t.type?un:fn]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){"out"===e._hoverState&&e.hide()},e.config.delay.hide):e.hide())},E._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},E._getConfig=function(t){return"number"==typeof(t=r({},this.constructor.Default,T(this.element).data(),t)).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),d.typeCheckConfig(Xe,t,this.constructor.DefaultType),t},E._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},E._cleanTipClass=function(){var t=T(this.getTipElement()),e=t.attr("class").match(nn);null!==e&&0<e.length&&t.removeClass(e.join(""))},E._handlePopperPlacementChange=function(t){this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},E._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(T(t).removeClass(hn),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},Fn._jQueryInterface=function(n){return this.each(function(){var t=T(this).data(tn),e="object"==typeof n&&n;if((t||!/dispose|hide/.test(n))&&(t||(t=new Fn(this,e),T(this).data(tn,t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},n(Fn,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return sn}},{key:"NAME",get:function(){return Xe}},{key:"DATA_KEY",get:function(){return tn}},{key:"Event",get:function(){return ln}},{key:"EVENT_KEY",get:function(){return I}},{key:"DefaultType",get:function(){return on}}]),dn=Fn,T.fn[Xe]=dn._jQueryInterface,T.fn[Xe].Constructor=dn,T.fn[Xe].noConflict=function(){return T.fn[Xe]=en,dn._jQueryInterface},dn),Mn=(Ye="popover",C="."+(Ve="bs.popover"),qe=(w=e).fn[Ye],ze=new RegExp("(^|\\s)bs-popover\\S+","g"),Ge=r({},E.Default,{placement:"right",trigger:"click",content:"",template:'<div class="ixb-popover" role="tooltip"><div class="arrow"></div><h3 class="ixb-popover-header"></h3><div class="ixb-popover-body"></div></div>'}),Ze=r({},E.DefaultType,{content:"(string|element|function)"}),Je={HIDE:"hide"+C,HIDDEN:"hidden"+C,SHOW:"show"+C,SHOWN:"shown"+C,INSERTED:"inserted"+C,CLICK:"click"+C,FOCUSIN:"focusin"+C,FOCUSOUT:"focusout"+C,MOUSEENTER:"mouseenter"+C,MOUSELEAVE:"mouseleave"+C},Mn=Qe=E,(x=Bn).prototype=Object.create(Mn.prototype),(x.prototype.constructor=x).__proto__=Mn,(x=Bn.prototype).isWithContent=function(){return this.getTitle()||this._getContent()},x.addAttachmentClass=function(t){w(this.getTipElement()).addClass("bs-popover-"+t)},x.getTipElement=function(){return this.tip=this.tip||w(this.config.template)[0],this.tip},x.setContent=function(){var t=w(this.getTipElement()),e=(this.setElementContent(t.find(".ixb-popover-header"),this.getTitle()),this._getContent());"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(".ixb-popover-body"),e),t.removeClass("fade show")},x._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},x._cleanTipClass=function(){var t=w(this.getTipElement()),e=t.attr("class").match(ze);null!==e&&0<e.length&&t.removeClass(e.join(""))},Bn._jQueryInterface=function(n){return this.each(function(){var t=w(this).data(Ve),e="object"==typeof n?n:null;if((t||!/destroy|hide/.test(n))&&(t||(t=new Bn(this,e),w(this).data(Ve,t)),"string"==typeof n)){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},n(Bn,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return Ge}},{key:"NAME",get:function(){return Ye}},{key:"DATA_KEY",get:function(){return Ve}},{key:"Event",get:function(){return Je}},{key:"EVENT_KEY",get:function(){return C}},{key:"DefaultType",get:function(){return Ze}}]),$e=Bn,w.fn[Ye]=$e._jQueryInterface,w.fn[Ye].Constructor=$e,w.fn[Ye].noConflict=function(){return w.fn[Ye]=qe,$e._jQueryInterface},$e),x=(Ie="scrollspy",Ae="."+(De="bs.scrollspy"),Se=(y=e).fn[Ie],Oe={offset:10,method:"auto",target:""},Ne={offset:"number",method:"string",target:"(string|element)"},ke={ACTIVATE:"activate"+Ae,SCROLL:"scroll"+Ae,LOAD_DATA_API:"load"+Ae+".data-api"},Pe="active",xe='[data-spy="scroll"]',Le=".active",je=".nav, .list-group",He=".nav-link",We=".nav-item",Me=".list-group-item",Re=".dropdown",Ue=".dropdown-item",Be=".dropdown-toggle",Fe="position",(x=Un.prototype).refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?"offset":Fe,i="auto"===this._config.method?t:this._config.method,o=i===Fe?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),y.makeArray(y(this._selector)).map(function(t){var e,t=d.getSelectorFromElement(t);if(e=t?y(t)[0]:e){var n=e.getBoundingClientRect();if(n.width||n.height)return[y(e)[i]().top+o,t]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},x.dispose=function(){y.removeData(this._element,De),y(this._scrollElement).off(Ae),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},x._getConfig=function(t){var e;return"string"!=typeof(t=r({},Oe,t)).target&&((e=y(t.target).attr("id"))||(e=d.getUID(Ie),y(t.target).attr("id",e)),t.target="#"+e),d.typeCheckConfig(Ie,t,Ne),t},x._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},x._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},x._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},x._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),n<=t){e=this._targets[this._targets.length-1];this._activeTarget!==e&&this._activate(e)}else if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])this._activeTarget=null,this._clear();else for(var i=this._offsets.length;i--;)this._activeTarget!==this._targets[i]&&t>=this._offsets[i]&&(void 0===this._offsets[i+1]||t<this._offsets[i+1])&&this._activate(this._targets[i])},x._activate=function(e){this._activeTarget=e,this._clear();var t=(t=this._selector.split(",")).map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),t=y(t.join(","));(t.hasClass("dropdown-item")?(t.closest(Re).find(Be).addClass(Pe),t):(t.addClass(Pe),t.parents(je).prev(He+", "+Me).addClass(Pe),t.parents(je).prev(We).children(He))).addClass(Pe),y(this._scrollElement).trigger(ke.ACTIVATE,{relatedTarget:e})},x._clear=function(){y(this._selector).filter(Le).removeClass(Pe)},Un._jQueryInterface=function(e){return this.each(function(){var t=y(this).data(De);if(t||(t=new Un(this,"object"==typeof e&&e),y(this).data(De,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},n(Un,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return Oe}}]),Ke=Un,y(window).on(ke.LOAD_DATA_API,function(){for(var t=y.makeArray(y(xe)),e=t.length;e--;){var n=y(t[e]);Ke._jQueryInterface.call(n,n.data())}}),y.fn[Ie]=Ke._jQueryInterface,y.fn[Ie].Constructor=Ke,y.fn[Ie].noConflict=function(){return y.fn[Ie]=Se,Ke._jQueryInterface},Ke),L=(L=".bs.tab",Ee=(b=e).fn.tab,be={HIDE:"hide"+L,HIDDEN:"hidden"+L,SHOW:"show"+L,SHOWN:"shown"+L,CLICK_DATA_API:"click.bs.tab.data-api"},ye="active",we=".active",Ce="> li > .active",(L=Rn.prototype).show=function(){var t,e,n,i,o,r,s=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&b(this._element).hasClass(ye)||b(this._element).hasClass("disabled")||(e=b(this._element).closest(".nav, .list-group")[0],n=d.getSelectorFromElement(this._element),e&&(o="UL"===e.nodeName?Ce:we,i=(i=b.makeArray(b(e).find(o)))[i.length-1]),o=b.Event(be.HIDE,{relatedTarget:this._element}),r=b.Event(be.SHOW,{relatedTarget:i}),i&&b(i).trigger(o),b(this._element).trigger(r),r.isDefaultPrevented())||o.isDefaultPrevented()||(n&&(t=b(n)[0]),this._activate(this._element,e),r=function(){var t=b.Event(be.HIDDEN,{relatedTarget:s._element}),e=b.Event(be.SHOWN,{relatedTarget:i});b(i).trigger(t),b(s._element).trigger(e)},t?this._activate(t,t.parentNode,r):r())},L.dispose=function(){b.removeData(this._element,"bs.tab"),this._element=null},L._activate=function(t,e,n){function i(){return o._transitionComplete(t,r,n)}var o=this,r=("UL"===e.nodeName?b(e).find(Ce):b(e).children(we))[0],e=n&&d.supportsTransitionEnd()&&r&&b(r).hasClass("fade");r&&e?b(r).one(d.TRANSITION_END,i).emulateTransitionEnd(150):i()},L._transitionComplete=function(t,e,n){var i;e&&(b(e).removeClass("show "+ye),(i=b(e.parentNode).find("> .dropdown-menu .active")[0])&&b(i).removeClass(ye),"tab"===e.getAttribute("role"))&&e.setAttribute("aria-selected",!1),b(t).addClass(ye),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),d.reflow(t),b(t).addClass("show"),t.parentNode&&b(t.parentNode).hasClass("dropdown-menu")&&((i=b(t).closest(".dropdown")[0])&&b(i).find(".dropdown-toggle").addClass(ye),t.setAttribute("aria-expanded",!0)),n&&n()},Rn._jQueryInterface=function(n){return this.each(function(){var t=b(this),e=t.data("bs.tab");if(e||(e=new Rn(this),t.data("bs.tab",e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},n(Rn,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),Te=Rn,b(document).on(be.CLICK_DATA_API,'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(t){t.preventDefault(),Te._jQueryInterface.call(b(this),"show")}),b.fn.tab=Te._jQueryInterface,b.fn.tab.Constructor=Te,b.fn.tab.noConflict=function(){return b.fn.tab=Ee,Te._jQueryInterface},Te);function Rn(t){this._element=t}function Un(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+He+","+this._config.target+" "+Me+","+this._config.target+" "+Ue,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,y(this._scrollElement).on(ke.SCROLL,function(t){return n._process(t)}),this.refresh(),this._process()}function Bn(){return Qe.apply(this,arguments)||this}function Fn(t,e){if(void 0===_e)throw new TypeError("Bootstrap tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}function Kn(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=D(t).find(yn)[0],this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._originalBodyPadding=0,this._scrollbarWidth=0}function j(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}if(void 0===e)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");if((e=e.fn.jquery.split(" ")[0].split("."))[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0");t.Util=d,t.Alert=p,t.Button=wt,t.Carousel=g,t.Collapse=m,t.Dropdown=ce,t.Modal=_,t.Popover=Mn,t.Scrollspy=x,t.Tab=L,t.Tooltip=E,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=bootstrap.bundle.min.js.map