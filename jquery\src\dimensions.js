define(["./core","./core/access","./var/isWindow","./css"],function(s,u,f){"use strict";
// Create innerHeight, innerWidth, height, width, outerHeight and outerWidth methods
return s.each({Height:"height",Width:"width"},function(r,d){s.each({padding:"inner"+r,content:d,"":"outer"+r},function(o,c){
// Margin is only for outerHeight, outerWidth
s.fn[c]=function(e,n){var t=arguments.length&&(o||"boolean"!=typeof e),i=o||(!0===e||!0===n?"margin":"border");return u(this,function(e,n,t){var o;return f(e)?0===c.indexOf("outer")?e["inner"+r]:e.document.documentElement["client"+r]:
// Get document width or height
9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+r],o["scroll"+r],e.body["offset"+r],o["offset"+r],o["client"+r])):void 0===t?
// Get width or height on the element, requesting but not forcing parseFloat
s.css(e,n,i):
// Set width or height on the element
s.style(e,n,t,i)},d,t?e:void 0,t)}})}),s});