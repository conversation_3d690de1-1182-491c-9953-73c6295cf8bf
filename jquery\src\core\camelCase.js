define([],function(){"use strict";
// Matches dashed string for camelizing
var r=/^-ms-/,n=/-([a-z])/g;
// Used by camelCase as callback to replace()
function t(e,r){return r.toUpperCase()}
// Convert dashed to camelCase; used by the css and data modules
// Support: IE <=9 - 11, Edge 12 - 15
// Microsoft forgot to hump their vendor prefix (#9572)
return function(e){return e.replace(r,"ms-").replace(n,t)}});