define(["./core","./var/document","./var/documentElement","./var/hasOwn","./var/indexOf"],function(i,u,e,o,r){"use strict";
/*
 * Optional (non-Sizzle) selector module for custom builds.
 *
 * Note that this DOES NOT SUPPORT many documented jQuery
 * features in exchange for its smaller size:
 *
 * Attribute not equal selector
 * Positional selectors (:first; :eq(n); :odd; etc.)
 * Type selectors (:input; :checkbox; :button; etc.)
 * State-based selectors (:animated; :visible; :hidden; etc.)
 * :has(selector)
 * :not(complex selector)
 * custom selectors via Sizzle extensions
 * Leading combinators (e.g., $collection.find("> *"))
 * Reliable functionality on XML fragments
 * Requiring all parts of a selector to match elements under context
 *   (e.g., $div.find("div > *") now matches children of $div)
 * Matching against non-elements
 * Reliable sorting of disconnected nodes
 * querySelectorAll bug fixes (e.g., unreliable :focus on WebKit)
 *
 * If any of these are unacceptable tradeoffs, either use Sizzle or
 * customize this stub for the project's specific needs.
 */function t(e,t){return t?
// U+0000 NULL becomes U+FFFD REPLACEMENT CHARACTER
"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e;
// Other potentially-special ASCII characters get backslash-escaped
}var c,a,n=/HTML$/i,l=i.expando.split("").sort(f).join("")===i.expando,s=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.msMatchesSelector,
// CSS string/identifier serialization
// https://drafts.csswg.org/cssom/#common-serializing-idioms
d=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function f(e,t){
// Flag for duplicate removal
var n;
// Sort on method existence if only one input has compareDocumentPosition
return e===t?(c=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(
// Disconnected nodes
1&(
// Calculate position if both inputs belong to the same document
n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):
// Otherwise we know they are disconnected
1)?
// Choose the first element that is related to our preferred document
e===u||e.ownerDocument===u&&i.contains(u,e)?-1:t===u||t.ownerDocument===u&&i.contains(u,t)?1:a?r.call(a,e)-r.call(a,t):0:4&n?-1:1)}function m(e){var t,n=[],o=0,r=0;if(c=!1,a=!l&&e.slice(0),e.sort(f),c){for(;t=e[r++];)t===e[r]&&(o=n.push(r));for(;o--;)e.splice(n[o],1)}
// Clear input after sorting to release objects
// See https://github.com/jquery/sizzle/pull/225
return a=null,e}i.extend({uniqueSort:m,unique:m,escapeSelector:function(e){return(e+"").replace(d,t)},find:function(e,t,n,o){var r,c,a=0;
// Same basic safeguard as Sizzle
if(n=n||[],t=t||u,e&&"string"==typeof e){
// Early return if context is not an element or document
if(1!==(c=t.nodeType)&&9!==c)return[];if(o)for(;r=o[a++];)i.find.matchesSelector(r,e)&&n.push(r);else i.merge(n,t.querySelectorAll(e))}return n},text:function(e){var t,n="",o=0,r=e.nodeType;if(r){if(1===r||9===r||11===r)
// Use textContent for elements
return e.textContent;
// Do not include comment or processing instruction nodes
if(3===r||4===r)return e.nodeValue}else
// If no nodeType, this is expected to be an array
for(;t=e[o++];)
// Do not traverse comment nodes
n+=i.text(t);return n},contains:function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!n.contains(t))},isXMLDoc:function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;
// Assume HTML when documentElement doesn't yet exist, such as inside
// document fragments.
return!n.test(t||e&&e.nodeName||"HTML")},expr:{attrHandle:{},match:{bool:new RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:/^[\x20\t\r\n\f]*[>+~]/}}}),i.extend(i.find,{matches:function(e,t){return i.find(e,null,null,t)},matchesSelector:function(e,t){return s.call(e,t)},attr:function(e,t){var n=i.expr.attrHandle[t.toLowerCase()],
// Don't get fooled by Object.prototype properties (jQuery #13807)
n=n&&o.call(i.expr.attrHandle,t.toLowerCase())?n(e,t,i.isXMLDoc(e)):void 0;return void 0!==n?n:e.getAttribute(t)}})});