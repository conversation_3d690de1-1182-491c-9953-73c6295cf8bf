define(["../core","../var/document","../var/documentElement","../var/support"],function(e,n,i,t){"use strict";
// Executing both pixelPosition & boxSizingReliable tests require only one layout
// so they're executed at the same time to save the second computation.
function o(){
// This is a singleton, we need to execute it only once
var e;b&&(u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",b.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",i.appendChild(u).appendChild(b),e=window.getComputedStyle(b),l="1%"!==e.top,
// Support: Android 4.0 - 4.3 only, Firefox <=3 - 44
c=12===r(e.marginLeft),
// Support: Android 4.0 - 4.3 only, Safari <=9.1 - 10.1, iOS <=7.0 - 9.3
// Some styles come back with percentage values, even though they shouldn't
b.style.right="60%",a=36===r(e.right),
// Support: IE 9 - 11 only
// Detect misreporting of content dimensions for box-sizing:border-box elements
s=36===r(e.width),
// Support: IE 9 only
// Detect overflow:scroll screwiness (gh-3699)
// Support: Chrome <=64
// Don't get tricked when zoom affects offsetWidth (gh-4029)
b.style.position="absolute",d=12===r(b.offsetWidth/3),i.removeChild(u),
// Nullify the div so it wouldn't be stored in the memory and
// it will also be a sign that checks already performed
b=null)}function r(e){return Math.round(parseFloat(e))}var l,s,d,a,p,c,u,b;return u=n.createElement("div"),
// Finish early in limited (non-browser) environments
(b=n.createElement("div")).style&&(
// Support: IE <=9 - 11 only
// Style of cloned element affects source element cloned (#8908)
b.style.backgroundClip="content-box",b.cloneNode(!0).style.backgroundClip="",t.clearCloneStyle="content-box"===b.style.backgroundClip,e.extend(t,{boxSizingReliable:function(){return o(),s},pixelBoxStyles:function(){return o(),a},pixelPosition:function(){return o(),l},reliableMarginLeft:function(){return o(),c},scrollboxSize:function(){return o(),d},
// Support: IE 9 - 11+, Edge 15 - 18+
// IE/Edge misreport `getComputedStyle` of table rows with width/height
// set in CSS while `offset*` properties report correct values.
// Behavior in IE 9 is more subtle than in newer versions & it passes
// some versions of this test; make sure not to make it pass there!
//
// Support: Firefox 70+
// Only Firefox includes border widths
// in computed dimensions. (gh-4529)
reliableTrDimensions:function(){var e,t,o;return null==p&&(e=n.createElement("table"),t=n.createElement("tr"),o=n.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",
// Support: Chrome 86+
// Height set through cssText does not get applied.
// Computed height then comes back as 0.
t.style.height="1px",o.style.height="9px",
// Support: Android 8 Chrome 86+
// In our bodyBackground.html iframe,
// display for all div elements is set to "inline",
// which causes a problem only in Android 8 Chrome 86.
// Ensuring the div is display: block
// gets around this issue.
o.style.display="block",i.appendChild(e).appendChild(t).appendChild(o),o=window.getComputedStyle(t),p=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===t.offsetHeight,i.removeChild(e)),p}})),t});