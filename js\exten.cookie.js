/*!
 * j<PERSON><PERSON><PERSON>ie Plugin v1.4.1
 * https://github.com/carhartl/jquery-cookie
 *
 * Copyright 2013 <PERSON>
 * Released under the MIT license
 */
!function(e){"function"==typeof define&&define.amd?
// AMD
define(["jquery"],e):"object"==typeof exports?
// CommonJS
e(require("jquery")):
// Browser globals
e(jQuery)}(function(s){var n=/\+/g;function f(e){return x.raw?e:encodeURIComponent(e)}function m(e,o){e=x.raw?e:function(e){0===e.indexOf('"')&&(
// This is a quoted cookie as according to RFC2068, unescape...
e=e.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{
// Replace server-side written pluses with spaces.
// If we can't decode the cookie, ignore it, it's unusable.
// If we can't parse the cookie, ignore it, it's unusable.
return e=decodeURIComponent(e.replace(n," ")),x.json?JSON.parse(e):e}catch(e){}}(e);return s.isFunction(o)?o(e):e}var x=s.cookie=function(e,o,n){
// Write
var i,r;
// Read
if(void 0!==o&&!s.isFunction(o))return"number"==typeof(n=s.extend({},x.defaults,n)).expires&&(i=n.expires,(r=n.expires=new Date).setTime(+r+864e5*i)),document.cookie=[f(e),"=",(r=o,f(x.json?JSON.stringify(r):String(r))),n.expires?"; expires="+n.expires.toUTCString():"",// use expires attribute, max-age is not supported by IE
n.path?"; path="+n.path:"",n.domain?"; domain="+n.domain:"",n.secure?"; secure":""].join("");for(var t=e?void 0:{},c=document.cookie?document.cookie.split("; "):[],u=0,a=c.length
// To prevent the for loop in the first place assign an empty array
// in case there are no cookies at all. Also prevents odd result when
// calling $.cookie().
;u<a;u++){var d=c[u].split("="),p=(p=d.shift(),x.raw?p:decodeURIComponent(p)),d=d.join("=");if(e&&e===p){
// If second argument (value) is a function it's a converter...
t=m(d,o);break}
// Prevent storing a cookie that we couldn't decode.
e||void 0===(d=m(d))||(t[p]=d)}return t};x.defaults={},s.removeCookie=function(e,o){return void 0!==s.cookie(e)&&(
// Must not alter options, thus extending a fresh object...
s.cookie(e,"",s.extend({},o,{expires:-1})),!s.cookie(e))}});