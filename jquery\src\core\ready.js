define(["../core","../var/document","../core/readyException","../deferred"],function(t,n){"use strict";
// The deferred used on DOM ready
var d=t.Deferred();
// The ready event handler and self cleanup method
function e(){n.removeEventListener("DOMContentLoaded",e),window.removeEventListener("load",e),t.ready()}
// Catch cases where $(document).ready() is called
// after the browser event has already occurred.
// Support: IE <=9 - 10 only
// Older IE sometimes signals "interactive" too soon
t.fn.ready=function(e){return d.then(e).catch(function(e){t.readyException(e)}),this},t.extend({
// Is the DOM ready to be used? Set to true once it occurs.
isReady:!1,
// A counter to track how many items to wait for before
// the ready event fires. See #6781
readyWait:1,
// Handle when the DOM is ready
ready:function(e){
// Abort if there are pending holds or we're already ready
(!0===e?--t.readyWait:t.isReady)||(
// Remember that the DOM is ready
t.isReady=!0)!==e&&0<--t.readyWait||
// If there are functions bound, to execute
d.resolveWith(n,[t])}}),t.ready.then=d.then,"complete"===n.readyState||"loading"!==n.readyState&&!n.documentElement.doScroll?
// Handle it asynchronously to allow scripts the opportunity to delay ready
window.setTimeout(t.ready):(
// Use the handy event callback
n.addEventListener("DOMContentLoaded",e),
// A fallback to window.onload, that will always work
window.addEventListener("load",e))});