define(["./core","./var/isFunction","./var/slice","./callbacks"],function(s,u,a){"use strict";function d(e){return e}function h(e){throw e}function l(e,n,t,r){var o;try{
// Check for promise aspect first to privilege synchronous behavior
e&&u(o=e.promise)?o.call(e).done(n).fail(t):e&&u(o=e.then)?o.call(e,n,t):
// Control `resolve` arguments by letting Array#slice cast boolean `noValue` to integer:
// * false: [ value ].slice( 0 ) => resolve( value )
// * true: [ value ].slice( 1 ) => resolve()
n.apply(void 0,[e].slice(r));
// For Promises/A+, convert exceptions into rejections
// Since jQuery.when doesn't unwrap thenables, we can skip the extra checks appearing in
// Deferred#then to conditionally suppress rejection.
}catch(e){
// Support: Android 4.0 only
// Strict mode functions invoked without .call/.apply get global-object context
t.apply(void 0,[e])}}return s.extend({Deferred:function(e){var i=[
// action, add listener, callbacks,
// ... .then handlers, argument index, [final state]
["notify","progress",s.Callbacks("memory"),s.Callbacks("memory"),2],["resolve","done",s.Callbacks("once memory"),s.Callbacks("once memory"),0,"resolved"],["reject","fail",s.Callbacks("once memory"),s.Callbacks("once memory"),1,"rejected"]],o="pending",c={state:function(){return o},always:function(){return a.done(arguments).fail(arguments),this},catch:function(e){return c.then(null,e)},
// Keep pipe for back-compat
pipe:function(){var o=arguments;return s.Deferred(function(r){s.each(i,function(e,n){
// Map tuples (progress, done, fail) to arguments (done, fail, progress)
var t=u(o[n[4]])&&o[n[4]];
// deferred.progress(function() { bind to newDefer or newDefer.notify })
// deferred.done(function() { bind to newDefer or newDefer.resolve })
// deferred.fail(function() { bind to newDefer or newDefer.reject })
a[n[1]](function(){var e=t&&t.apply(this,arguments);e&&u(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[n[0]+"With"](this,t?[e]:arguments)})}),o=null}).promise()},then:function(n,t,r){var l=0;function f(o,i,c,a){return function(){function e(){var e,n;
// Support: Promises/A+ section *******.3
// https://promisesaplus.com/#point-59
// Ignore double-resolution attempts
if(!(o<l)){
// Support: Promises/A+ section 2.3.1
// https://promisesaplus.com/#point-48
if((e=c.apply(t,r))===i.promise())throw new TypeError("Thenable self-resolution");
// Support: Promises/A+ sections 2.3.3.1, 3.5
// https://promisesaplus.com/#point-54
// https://promisesaplus.com/#point-75
// Retrieve `then` only once
n=e&&(
// Support: Promises/A+ section 2.3.4
// https://promisesaplus.com/#point-64
// Only check objects and functions for thenability
"object"==typeof e||"function"==typeof e)&&e.then,
// Handle a returned thenable
u(n)?
// Special processors (notify) just wait for resolution
a?n.call(e,f(l,i,d,a),f(l,i,h,a)):(
// ...and disregard older resolution values
l++,n.call(e,f(l,i,d,a),f(l,i,h,a),f(l,i,d,i.notifyWith))):(
// Only substitute handlers pass on context
// and multiple values (non-spec behavior)
c!==d&&(t=void 0,r=[e]),
// Process the value(s)
// Default process is resolve
(a||i.resolveWith)(t,r))}}var t=this,r=arguments,
// Only normal processors (resolve) catch and reject exceptions
n=a?e:function(){try{e()}catch(e){s.Deferred.exceptionHook&&s.Deferred.exceptionHook(e,n.stackTrace),
// Support: Promises/A+ section *******.4.1
// https://promisesaplus.com/#point-61
// Ignore post-resolution exceptions
l<=o+1&&(
// Only substitute handlers pass on context
// and multiple values (non-spec behavior)
c!==h&&(t=void 0,r=[e]),i.rejectWith(t,r))}};
// Support: Promises/A+ section *******.1
// https://promisesaplus.com/#point-57
// Re-resolve promises immediately to dodge false rejection from
// subsequent errors
o?n():(
// Call an optional hook to record the stack, in case of exception
// since it's otherwise lost when execution goes async
s.Deferred.getStackHook&&(n.stackTrace=s.Deferred.getStackHook()),window.setTimeout(n))}}return s.Deferred(function(e){
// progress_handlers.add( ... )
i[0][3].add(f(0,e,u(r)?r:d,e.notifyWith)),
// fulfilled_handlers.add( ... )
i[1][3].add(f(0,e,u(n)?n:d)),
// rejected_handlers.add( ... )
i[2][3].add(f(0,e,u(t)?t:h))}).promise()},
// Get a promise for this deferred
// If obj is provided, the promise aspect is added to the object
promise:function(e){return null!=e?s.extend(e,c):c}},a={};
// Add list-specific methods
// All done!
return s.each(i,function(e,n){var t=n[2],r=n[5];
// promise.progress = list.add
// promise.done = list.add
// promise.fail = list.add
c[n[1]]=t.add,
// Handle state
r&&t.add(function(){
// state = "resolved" (i.e., fulfilled)
// state = "rejected"
o=r},
// rejected_callbacks.disable
// fulfilled_callbacks.disable
i[3-e][2].disable,
// rejected_handlers.disable
// fulfilled_handlers.disable
i[3-e][3].disable,
// progress_callbacks.lock
i[0][2].lock,
// progress_handlers.lock
i[0][3].lock),
// progress_handlers.fire
// fulfilled_handlers.fire
// rejected_handlers.fire
t.add(n[3].fire),
// deferred.notify = function() { deferred.notifyWith(...) }
// deferred.resolve = function() { deferred.resolveWith(...) }
// deferred.reject = function() { deferred.rejectWith(...) }
a[n[0]]=function(){return a[n[0]+"With"](this===a?void 0:this,arguments),this},
// deferred.notifyWith = list.fireWith
// deferred.resolveWith = list.fireWith
// deferred.rejectWith = list.fireWith
a[n[0]+"With"]=t.fireWith}),
// Make the deferred a promise
c.promise(a),
// Call given func if any
e&&e.call(a,a),a},
// Deferred helper
when:function(e){function
// subordinate callback factory
n(n){return function(e){o[n]=this,i[n]=1<arguments.length?a.call(arguments):e,--t||c.resolveWith(o,i)}}var
// count of uncompleted subordinates
t=arguments.length,
// count of unprocessed arguments
r=t,
// subordinate fulfillment data
o=Array(r),i=a.call(arguments),
// the primary Deferred
c=s.Deferred();
// Single- and empty arguments are adopted like Promise.resolve
if(t<=1&&(l(e,c.done(n(r)).resolve,c.reject,!t),"pending"===c.state()||u(i[r]&&i[r].then)))return c.then();
// Multiple arguments are aggregated like Promise.all array elements
for(;r--;)l(i[r],n(r),c.reject);return c.promise()}}),s});