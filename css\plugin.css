.inject-button{
    all: unset;
    box-sizing: border-box;
    position: fixed;
    z-index: 99999;
    bottom: 0px;
    transition: all 2s;
}
.inject-button-1{
    opacity: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    all: initial;
    box-sizing: border-box;
    font-family: noto-sans, sans-serif;
    font-weight: 400;
}
.inject-button-2{
    margin-bottom: 10px;
    box-shadow: var(--hter-tab-shadow) !important;
    border-radius: 6px;
    all: initial;
    box-sizing: border-box;
    font-family: noto-sans, sans-serif;
    font-weight: 400;
}
.clear-cookie-button{
    box-sizing: border-box;
    position: fixed;
    z-index: 99999;
    top: 50px;
    height: 80px;
    width: 80px;
    background-color: black;
    border-radius: 20%;
    right: 50px;
    color: #fff;
    text-align: center;
    cursor: pointer;
}
.auto-expand{
    display: inline-block;
    box-sizing: border-box;
    /* z-index: 100; */
    top: 450px;
    height: 30px;
    line-height: 30px;
    width: 110px;
    background-color: black;
    border-radius: 5px;
    right: 50px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    margin-left: 35px;
}
.jump-button{
    position: relative;
    overflow-wrap: anywhere;
    top: 10px;
    width: 70px;
    height: 70px;
    display: inline-block;
    font-size: 14px;
}
.inject-show-content-div{
    height: auto;
    background-color: white;
    /* width: 530px; */
    overflow: auto;
    box-shadow: black 0px 0px 7px;
}
.is-active{
    display: none;
}
.inject-hide-style{
    height: 45px;
    border-bottom: 1px solid rgb(212, 212, 212);
}
.new{
    width: auto;
    height: 32px;
    background: black;
    color:#fff;
    border-radius: 15px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 12px;
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: -35px;
}
.hide-title-info{
    margin-left: 10px;
    font-size: 20px;
    line-height: 2.4;
    font-weight: bold;
}
.float-right{
    float: right;
}
.cursor{
    cursor: pointer;
}
.chahca{
    margin-top: 15px;
    margin-right: 10px;
}
.join-group{
    margin-bottom: 20px;
    font-size: 15px;
    font-weight: bolder;
    text-align: right;
    margin-top: 20px;
    margin-right: 48px;
    text-decoration: underline;
}
.black{
    color: black;
}
.displayMenu{
    display: inline-block;
    color: #9d9d9d;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    width: 90px !important;
    text-align: center;
    height: 65px;
    
}
.tools-width{
    width: 610px;
}
.displayMenu img{
    margin-top: 10px;
}

.dy-white{
    width: 25px;
}

.tool-nav-class{
    background: black;
    border-radius: 5px;
    margin-bottom: 10px;
    height: 65px;
}

.inject-active{
    color:white !important;
    background-color: #000;
}

.inline{
    display: inline-block;
}

.product-img-1{
    width: 80px;
    border-radius: 6px;
    border-width: 1px;
    border-style: solid;
}
.product-div-1{
    width: 80px;
}
.product-div-2{
    width:400px;
    vertical-align: middle;
}
.product-div-2 span{
    /* display:block;
    width: 400px;
    text-overflow :ellipsis;
    white-space :nowrap;
    overflow : hidden; */

    display: block;
    width: 500px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 600;


}
.product-part-1{
    padding:40px;
    max-height: 650px;
    height: 580px;
    overflow: auto;
    padding-top:0px;
}

.product-total-div{
    width: 30%;
    vertical-align: middle;
}
.mgtop10{
    margin-top:10px;
}
.font-13{
    font-size: 13px;
}
.font-c-r{
    color:red;
}

.topBrand{

}
.w3{
    width: 45%;
    text-align: center;
    margin-top: 5px;
}
.w6{
    width: 32%;
    text-align: center;
    margin-top: 15px;
}
.w7{
    width: 120px;
    text-align: left;
    margin-top: 15px;
}

.w7_en{
    width: 138px;
    text-align: left;
    margin-top: 15px;
}

.product-img-2{
    width: auto;
    border-radius: 6px;
    border-width: 1px;
    border-style: solid;
    height: 145px;
}

.valign-b{
    vertical-align: bottom;
}
.valign-s{
    vertical-align: super;
}
.font-w-600{
    font-weight: 600;
}

.from-supply div{
    line-height: 1.7;
    width: 147px;
    text-overflow :ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
    white-space :nowrap; /*让文字不换行*/
    overflow : hidden; /*超出要隐藏*/
}

.mg-left-40{
    margin-left:5px;
}
.mg-top-20{
    margin-top:20px;
}
.mg-top-30{
    margin-top:30px;
}
.mg-left-5{
    margin-left:5px;
}
.store-title{

    width: 60%;
    /* margin-left: 10px; */
    word-break: break-word;
    color: black;

    /* width: 50%;
    text-overflow :ellipsis; 
    white-space :nowrap; 
    overflow : hidden;  */

    /* width: 50%;
    margin-left: 10px;
    word-break: break-word;
    color:black;
    font-weight: 600;
    line-height:1.4; */
}

.button-black{
    color: white;
    white-space: nowrap;
    font-size: 11px;
    background-color: gray;
    color: rgb(255, 255, 255);
    height: 35px;
    /* display: flex; */
    /* align-items: center; */
    /* pointer-events: none; */
    /* user-select: none; */
    /* padding: 3px 12px; */
    border-radius: 5px;
    width: 100%;
    cursor: pointer;
    text-align: center !important;
    font-size: 17px;
    margin: 0 auto;
    margin-top: 20px;
    line-height: 2.2;
}

.tools-height{
    /* max-height: 650px;
    overflow: auto; */
}

.echart_div{
    /* height:350px; */
    width: 100%;
}
.echart_div .div_ec{
    height:340px;
    width: 470px;
    margin-left: 20px;
}
.product-img-3{
    width:140px;
}

.img-ul{
    list-style: none;
}
.img-ul li{
    display: inline-block;
    margin-top: 15px;
    margin-left: 20px;
}

.same-products{
    margin-top:15px;
}

.btn-style{
    text-align: right;
    margin-top: 20px;
    /* margin-right: 48px; */
}
.btn-active{
    background: black !important;
    color: white !important;
}
.btn{
    border: none;
    padding: 3px;
    cursor: pointer;
}
.store_logo_div{
    position: relative;
    margin-left: 40px;
}
.store-logo-div-div2{
    position: absolute;
    top: 5px;
}

.smt-loading img{
    width: 30px;
}
.endupdate{
    position: absolute;
    /* left: 0; */
    right: -100px;
    top: -30px;
    background: #000;
    color: #fff;
    width: 150px;
    height: 24px;
    line-height: 24px;
    border-radius: 3px;
    text-align: center;
    display: none;
}
.smt-loading{
    position: relative;
    top: 40%;
    left: 45%;
    width: 50px;
    margin-top:30px;
    margin-bottom:60px;
}

.no_info_tip{
    text-align: center;
    margin-top: 15px;
}

.no_login_none{
    display: none;
}

.no_login_block{
    display: block;
}
/* .rating {
    display: inline-block;
    font-size: 0;
    position: relative;
  } */
  
  .rating {
    position: relative;
    display: inline-block;
    width: 90px;
    height: 100%; /* 替换为您自己的星星图像路径 */
    background-size: cover;
  }
  .starsimg{
    position: absolute;
    top: -15px;
    z-index: 2;
    /* clip: rect(0px 0px 0px 60px); */
    clip-path: inset(0 60px 0 0px);
  }
  
  /* .star::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/img/star.png'); 
    background-size: cover;
    overflow: hidden;
  } */
.chahca{
    margin-top: 15px;
    margin-right: 10px;
}

.download-video{
    position: absolute;
    bottom: 10px;
    right: 30px;
    z-index: 1000;
}
.dimg{
    transform:inherit!important;
    width: auto!important;
    height: 20px;
    cursor: pointer !important;
    vertical-align: sub;
}

.six-div-1{
    /* font-size: 17px; */
   
}
.six-div-2{
     margin-top: 10px;
    /*font-size: 20px; */
}

.product-title-name{
    font-size: 17px;
}

.total-order{
    font-size: 15px;
    font-weight: 600;
    color: gray;
}
.trade_count_nums{
    font-size: 16px;
    font-weight: 600;
    color: black;
    /* margin-top: 8px; */
}

.smt-alert{
    position: fixed;
    z-index: 300000;
    top: 30%;
    font-size: 20px;
    font-weight: 600;
    background: black;
    width: 350px;
    text-align: center;
    left: 40%;
    color:white;
}

.h3-button{
    display: inline-block;
    vertical-align: super;
    float: right;
}

.store-new-div{
    margin-bottom: 0px;
    vertical-align: top;
    position: relative;
    top: 12px;
}

/* .text-ixspy{
    margin-left: 25px !important;
    margin-right: 20px !important;
    vertical-align: sub !important;
} */

.download-div-left{
    /* width: 50px;
    height: 50px;
    border: 1px solid gray;
    vertical-align: middle; */
}
.switch {
    position: relative;
    display: inline-block;
    width: 56px;
    height: 22px;
  }
  
  .switch input {
    display: none;
  }
  
  .switch .slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    border-radius: 22px;
    transition: .4s;
    cursor: pointer;
  }
  
  .switch .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 1px;
    background-color: white;
    border-radius: 50%;
    transition: .4s;
  }
  
  .switch input:checked + .slider {
    background-color: #007bff;
  }
  
  .switch input:checked + .slider:before {
    transform: translateX(32px);
  }
  
  .switch .slider.round {
    border-radius: 22px;
  }
  
  .switch .slider.round:before {
    border-radius: 50%;
  }
#big-img{
    display: none;
    position: absolute;
    background: gray;
}

#big-img img{
    width:278px;
}
.store-name{
    /* width: 192px; */
    font-size: 18px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
    color: black;
    border-bottom: 1px;
    font-weight: 600;
}

.no_info_tip{
    text-align: center;
    margin-top: 60px;
    margin-bottom: 60px;
    font-size: 15px;
    color: gray;
}

.h4{
    font-weight: 600;
}

.download-div{
    background-color: rgb(232, 232, 232);
    padding:0 6px;
    border-radius: 5px;
    height: 30px;
    line-height: 30px;
    vertical-align: sub;
    cursor: pointer;
}

.hover-titip_update_tip,.hover-titip_chart,.hover-titip_pic,.hover-titip_product,.hover-titip_store,.hover-titip,.hover-titip_myfavted,.hover-titip_persion{
    display: none;
    position: absolute;
    top: -68px;
    border: 1px solid black;
    padding: 10px;
    font-size: 14px;
    z-index: 1000;
    background: black;
    color: white;
    /* left: 5px; */
    border-radius: 5px;
}

.hover-titip_ixspy{
    display: none;
    position: absolute;
    top: -120px;
    border: 1px solid black;
    padding: 10px;
    font-size: 14px;
    z-index: 1000;
    background: black;
    color: white;
    /* left: 5px; */
    border-radius: 5px;
}

.turn-to-ixspy{
    float: right;
    /* border-bottom: 1px solid gainsboro; */
    cursor: pointer;
    margin-left: 15px;
}

.update-tip{
    position: absolute;
    z-index: 1000;
    /* top: 35%; */
    background: gray;
    /* left: 35%; */
    color: white;
    /* text-align: center; */
    /* padding: 25px; */
    /* width: 700px; */
    /* height: 85%; */

    /* padding: 20px;
    max-height: 650px;
    overflow: auto;
    padding-top: 0px; */
    overflow: auto;
}

.d-button-chrome{
    display: inline-block;
    background: antiquewhite;
    border-radius: 15px;
    padding: 6px;
    height: 36px;
    font-size: 18px;
    cursor: pointer;
    color: gray;
    font-weight: bolder;
    line-height: 1.5;
}

.upGrade-button{
    display: none;
}

.a_1688{
    float: right;
    margin-top: 16px;
    color: black !important;
    text-decoration: revert;
    font-size: 15px;
    margin-left:12px;
}

.site-notify{
    position: absolute;
    left: -302px;
    background: white;
    box-shadow: black 0px 0px 7px;
    width: 300px;
    top: 0;
    overflow: auto;
    
    max-height: 713px;

}

.site-notify h3{
    text-align: center;
}
.site-notify h4{
    text-align: center;
    margin-bottom:0 px;
}
.notify-content{
    padding-left: 5px;
    padding-right: 5px;
    margin-top: 10px;
    margin-bottom:10px;
}

.notify-border{
    border: 1px solid rgb(212, 212, 212);
    padding-left: 5px;
}

.notify-time{
    text-align: center;
    margin-bottom: 10px;
    margin-top: 10px;
    font-size:14px;
}

.notify-close{
    position: absolute !important;
    right: 0px;
    top: -10px !important;
}

.chart-year{
    text-align: right;
}

.dialog_tj{
    position: absolute;
    top: 0px;
    z-index: 1000;
    
    left: 30%;
    margin-top: 35px;
    width: 650px;
    text-align: center;
    box-shadow: 0 1px 3px rgb(0 0 0);
    background: white;
}

.inline-data{
    display: inline-block;
    width: 250px;
    margin-top: 15px;
    font-weight: bold;
    font-size: 18px;
}

.plug-table th{
    padding:15px;
}

.smt-tj-loading{
    position: fixed;
    top: 161px;
    z-index: 1000;
    left: 39%;
    background: white;
    padding: 20px;
    width: 500px;
    text-align: center;
    box-shadow: 0 1px 3px rgb(0 0 0);
    height: 200px;
}

.w-button{
    background-color: black !important;
    color:white !important;
}

.zhexiantu{
    width:30px;
    cursor: pointer;
}

.favted-products{
    float: right;
    width: 25px;
    margin-left: 10px;
    cursor: pointer;
}
.maskwechat{
    height: 450px;
    width: 610px;
    background-color: rgba(0,0,0,0.7);
    position: absolute;
    top: 0px;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 10px;
    display: none;
}
.login-k-r{
    position: fixed;
    background-color: black;
    z-index: 100000000000;
    top: 40%;
    min-height: 100px;
    max-height:200px;
    width: 250px;
    left: 45%;
    padding: 25px;
    color: white;
}
.cancel{
    position: relative;
    top: -30px;
    right: -20px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
}
.important-none{
    display: none !important;
}

.tools-width-1{
    width: 154px !important;
    line-height: 2;
}

.update-tip p{
    font-size: 16px !important;
    line-height: 2 !important;
}

.country-many-info-show{
    position: absolute;
    z-index: 10000;
    top: 10%;
    left: 20%;
    width:65%;
    max-width: 1070px;
    color: black;
    padding: 20px;
    text-align: center; 
    box-shadow: 0 1px 3px rgb(0 0 0);
    background: white;
    width: 60%;
    min-width:730px;
}

.export-span{
    display: block;
    text-align: right;
    cursor: pointer;
    position: absolute;
    right: 25px;
    top: 80px;
}
.shuoming{
    text-align: left;
    padding-top: 40px;
    color: red;
}

.check-country-price{
    background: black;
    color: white;
    padding: 6px;
    border-radius: 13px;
    font-size: 12px;
    cursor: pointer;
    display: inline-block;
}
.check-orders{
    display: inline-block;
    font-size: 12px;
    padding: 6px;
    color: black;
}
.tr_RU{
    color: red;
}

.collection-loading{
    position: absolute;
    z-index: 10000;
    top: 47%;
    left: 50%;
    color: white;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgb(0 0 0);
    background: black;
}

.span-button{
    background: black;
    color: white;
    padding: 8px;
    font-size: 12px;
    border-radius: 21px;
    cursor: pointer;
}

.class-carrier{
    width: 240px;
    text-align: left;
    padding-left: 5px;
}

.over_free_ships{
    text-align: left;
    word-break: break-word;
    padding-left: 6px;
}

tr:hover{
    background: lightgray;
    cursor: pointer;
}

.chose_country_checkboxs{
    width: 43%;
    min-height: 300px;
    box-shadow: 0 1px 3px rgb(0 0 0);
    background: white;
    position: fixed;
    top: 0px;
    z-index: 10000;
    left: 28%;
    padding: 25px;
    min-width: 820px;
}

.country-groups-list{
    display: inline-block;
    margin-left: 20px;
    min-width: 169px;
    margin-top: 10px;
}

.start-collection{
    background: black;
    color: white;
    padding: 6px;
    border-radius: 13px;
    font-size: 12px;
    cursor: pointer;
    display: inline-block;
    /* width: 200px; */
    text-align: center;
    margin: 0 auto;
    margin-top: 25px;
    min-width: 150px;
}


.chose_country_checkboxs_zh {
    width: 43%;
    min-height: 300px;
    box-shadow: 0 1px 3px rgb(0 0 0);
    background: white;
    position: fixed;
    top: 100px;
    z-index: 12220000;
    left: 30%;
    padding: 25px;
}

.country-groups-list_zh {
    display: inline-block;
    margin-left: 20px;
    min-width: 86px;
    margin-top: 10px;
}

.chose_country_checkboxs .sku-property-list li{
    display: inline-block !important; 
    margin-left:15px;
    margin-top:15px;
    cursor: pointer;
    border: 1px solid gray;
    padding: 2px;
    margin-bottom: 10px;
}

.active-select{
    border:2px solid red !important;
}

.class-7-30{
    margin-top:10px;
    font-size:13px;
}

.inline{
    display: inline-block;
}

.w-60{
    width:57px
}


.look-more{
    font-size: 13px;
    font-weight: bold;
    text-decoration-line: underline;
    cursor: pointer;
    float: right;
    margin-right: 15px;
    margin-bottom: 10px;
}

.mbottom-5{
    margin-bottom: 5px;
}

.color-red{
    color:red
}

.seven-label{
    display: inline-block;
    /* width: 90px; */
    text-align: right;
    margin-right:3px;
}

.seven-value{
    display: inline-block;
    /* width: 75px; */
    margin-left:10px;
    color:red;
    margin-top:2px;
}

.seven-value-en{
    display: inline-block;
    /* width: 73px; */
    margin-left:0px;
    color:red;
    margin-top:2px;
}

.seven-label-en{
    display: inline-block;
    /* width: 127px; */
    text-align: right;
    margin-right:0px;
}

.seven_label_title_en{
    color:red;
}

.seven_label_title{
    display: none;
}

.event-notify-extention{
    position: absolute;
    z-index: 100000;
    background-color: #444;
    width: 450px;
    color: white;
    top:15%;
    left:40%;
    padding:25px;
}

.web-page-product-info{
    width: 100%;
    margin: 0 auto;
    margin-top: 10px;
    background: white;
    color: black;
    padding: 15px;
    box-shadow: black 0px 0px 5px;
    margin-bottom: 10px;
}

.dialog-product-show{
    position: absolute;
    top: 25%;
    background: white;
   
    z-index: 100000;
    padding: 25px;

    width:700px;

    left: calc((100% - 1000px)/2);
    box-shadow: black 0px 0px 7px;
}

.my-favted-ul li{
    display: inline-block;
    margin-left: 14px !important;
    margin-top: 20px !important;
    float:left;
}

.dialog-product-show-monitor {
    position: absolute;
    top: 10%;
    background: white;
 
    z-index: 100000;
    padding: 25px;
    width: 1000px;
    left: calc((100% - 1000px)/2);
    box-shadow: black 0px 0px 7px;
}

.container{
    padding-left: 8px !important;
}

.describe-text{
    width: 75%;
    margin: 0 auto;
    font-size: 17px;
    /* padding: 0px; */
    margin-bottom: 5px;
    font-weight: 700;
    color: gray;
}

.describe-text-persion{
    width: 75%;
    margin: 0 auto;
    font-size: 17px;
    /* padding: 0px; */
    margin-bottom: 5px;
    font-weight: 700;
    color: gray;
}

.two-button{
    font-size: 15px;
    /* font-weight: 600; */
    width: 49%;
    text-align: center;
    margin-bottom: 10px;
    background: #444;
    line-height: 2.6;
    height: 38px;
    border-bottom: 1px;
    color:white
}

.user-pic-div{
    margin-bottom: 20px;
    width: 80%;
    margin: 0 auto;
    text-align: center;
    height: 38px;
    line-height: 2.6;
    margin-bottom: 20px;
    margin-top:15px;
    background: #444;
    color:white
    
}

.country-chose-input{
    opacity: unset !important;
    -webkit-appearance: checkbox !important;
}

.tags-span{
    background: lightgray;
    padding: 5px;
    margin-top: 10px;
    display: inline-block;
    cursor: pointer;
    border-radius: 5px;
}


.tag-span-c{
    background: black;
    padding: 5px;
    margin-top: 10px;
    display: inline-block;
    cursor: pointer;
    color: white;
}

.active-tags{
    background: black !important;
    color: white !important;
}

.show-my-words{
    background: gainsboro;
    position: absolute;
    z-index: 1000;
    width: 800px;
    left: 30%;
    top: 105px;
    padding: 25px;
}

.line-words{
    margin-top:20px;
}

.words_like_span{
    background: darkgray;
    margin-right: 5px;
    margin-top: 5px;
    display: inline-block;
    padding: 5px;
    color: white;
}

.words-title{
    font-size: 16px;
    font-weight: bold;
}

.center{
    text-align: center;
    margin-top: 0;
}

.download-txt{
    text-align: right;
    margin-right: 10px;
    cursor: pointer;
    font-size: 15px;
    color: black;
    font-weight: bold;
    text-decoration: underline;
}

.tip_price_collect{
    position: absolute;
    top:45%;
    left: 44%;
    width: 350px;
    background: black;
    z-index: 1000;
    color: white;
    padding:25px;
}

.tip_price_collect_button{
    padding: 5px;
    background: white;
    color: black;  
    cursor:pointer;
    margin-left:35px;
}

#tableau{
    border: 1px solid !important;
}

#tableau tr, th ,td{
    border: 1px solid !important;
}


#checkall{
    opacity: 1 !important;
    -webkit-appearance: checkbox !important;
}

.show-keywords-pic{
    position: fixed;
    background-color: whitesmoke;
    z-index: 1000000;
    top: 10%;
    left: 35%;
    padding: 25px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)
}

.tip_product{
    font-size: 12px;
    color:#fff;
    margin: 0;
    padding: 0;
}
.border_bottom{
    border-bottom: 4px solid #0076f1;
}
.hover_turn_left,.hover_turn_right,.hover_turn_open,.hover_turn_stow{
    display: none;
    position: absolute;
    top: -30px;
    border: 1px solid #0076f1;
    padding: 5px;
    font-size: 12px;
    z-index: 1000;
    background: #fff;
    color: #0076f1;
    border-radius: 5px;
    min-width: 40px;
}
.imgDock{
    width: 25px;
    position: absolute;
    bottom: 5px;
    cursor: pointer;
}
.imgOpen{
    width: 25px;
    position: absolute;
    top: 5px;
}

.smt_img_search_choise_item{
    padding:5px;
    cursor: pointer;
    width:100%;line-height: 30px;background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
}

.smt_img_search_choise_item:hover{
    background-color: rgba(60, 60, 60, 1);
    color: white;
}


.smt_img_search_choise_item_list{
    cursor: pointer;
    padding:5px;
    width:100%;line-height: 30px;background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
}

.smt_img_search_choise_item_list:hover{
    background-color: rgba(60, 60, 60, 1);
    color: white;
}

.smt_favted_goods_box{
    padding: 5px; width: 300px;
    box-shadow: -4px 4px 10px rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 30%;
    left: 40%;
    background-color: white;
    z-index: 999;
}
.smt_favted_goods_box_span {
    min-width: 6rem;
    text-align: center;
    margin: 5px 10px;
}
.smt_favted_goods_box_button_box{
    text-align: right;
    margin: 15px auto 10px;
}
.smt_favted_goods_box_button{
    min-width: 6rem;
    font-size: 18px;
    padding: 5px;
    margin: 0 5px;
    border-radius: 8px;
}

.self-my-icon,.test-small-chart{
    max-width: 258px;
}
.TjDataButton{
    height: 30px;
    width: 110px;
}

/* 隐藏登录相关元素 */
.login-k-r, 
.no_login_block, 
.lang_login_more {
    display: none !important;
}

/* 确保已登录元素始终显示 */
.no_login_none, 
.h3-button {
    display: block !important;
}
