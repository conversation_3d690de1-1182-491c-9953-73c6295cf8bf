{"lang_product": {"message": "Product Overview"}, "lang_store": {"message": "Store Overview"}, "lang_price": {"message": "Product Charts"}, "lang_img": {"message": "Buyers Photos"}, "lang_order": {"message": "Order Details"}, "lang_top_2_products": {"message": "Trending Products"}, "lang_top_2_new_products": {"message": "New Products"}, "lang_store_sales_chart": {"message": "Sales Trend"}, "lang_store_favted_chart": {"message": "Favorites Trend"}, "lang_store_reviews_chart": {"message": "Reviews Trend"}, "lang_total": {"message": "Total"}, "lang_inc": {"message": "Increment"}, "lang_total_order": {"message": "Total Number of Orders"}, "lang_add_favted": {"message": "Add To Favorites"}, "lang_same_products": {"message": "Similar Products"}, "lang_shopify_products": {"message": "Shopify Dropshipping Import"}, "lang_sales_chart": {"message": "Daily change of 180-day sales"}, "lang_review_chart": {"message": "Reviews Trending Chart"}, "lang_favted_chart": {"message": "Favorites Trending Chart"}, "lang_price_chart": {"message": "Prices Trending Chart"}, "lang_success": {"message": "Success"}, "lang_fail": {"message": "Failed"}, "lang_age": {"message": "Has been opened"}, "lang_reviews": {"message": "Comments"}, "lang_favted": {"message": "Favorites"}, "lang_positive": {"message": "Positive Feedback"}, "lang_currency": {"message": "Currency Type"}, "lang_low_price": {"message": "Lowest Price"}, "lang_max_price": {"message": "Highest Price"}, "lang_ratings": {"message": "Ratings"}, "lang_search_on_shopify": {"message": "Find competing products in Shopify"}, "lang_no_login_button": {"message": "Login/Register To Add Favorite"}, "lang_currey": {"message": "Currency Type"}, "lang_product_price": {"message": "Price"}, "lang_product_rating": {"message": "Rating"}, "lang_product_total_orders": {"message": "Orders"}, "lang_product_total_reviews": {"message": "Comments Number"}, "lang_product_wishlist": {"message": "Favorite Number"}, "lang_same_as_product": {"message": "Same Products"}, "lang_no_info": {"message": "Unable to access the information"}, "lang_login_see": {"message": "Login/Register To View All"}, "lang_already_here": {"message": "Already existed"}, "lang_wait_download": {"message": "Downloading, please wait..."}, "lang_login_more": {"message": "Login/Register to try more functions"}, "lang_cow": {"message": "HOT! Explore over 300k+ dropshipping stores to find winning products"}, "lang_my_favted": {"message": "My Favorites"}, "lang_join_group": {"message": "Join Facebook Group"}, "lang_shop_age": {"message": " Years Old"}, "lang_shop_age_month": {"message": "months old"}, "lang_goods_rate": {"message": "<PERSON><PERSON><PERSON>"}, "lang_no_info_tip": {"message": "No relevant information found!"}, "lang_download_img": {"message": "Batch Download"}, "lang_download_video": {"message": "Download Video"}, "lang_tip_ixspy": {"message": "Welcome to free try AliExpress product research, competitive analysis tool."}, "lang_more_shop_info": {"message": "More analysis of stores..."}, "lang_more_product_info": {"message": "More analysis of products..."}, "lang_install_func": {"message": "Installation method"}, "lang_download_zip": {"message": "Download the extension installation file (zip compressed file), unzip"}, "lang_open_right": {"message": "Open the application center in the upper right corner of the browser, enter the 'Extensions' (or 'My App') page, and check the 'Developer Mode' option in the upper right corner"}, "lang_move_to_page": {"message": "Pull the unzipped folder directly into the page. If the corresponding extension card appears on the page, the installation is successful"}, "lang_install_attention": {"message": "Note: The folder cannot be deleted and moved after installation, otherwise it will be unavailable"}, "lang_plugin_update": {"message": "Extension Update"}, "lang_best_new": {"message": "Download the latest version"}, "lang_hot_store": {"message": "Similar Stores"}, "lang_tip_check_upgrade": {"message": "Click Update"}, "lang_1688": {"message": "Find the Same Paragraph"}, "lang_more": {"message": "More"}, "lang_auto_expand": {"message": "Auto Expand"}, "lang_category": {"message": "Category"}, "lang_a": {"message": "en"}, "lang_notify": {"message": "Notification"}, "lang_first_discover": {"message": "First Discovery Time"}, "lang_last_update": {"message": "Update"}, "lang_year_chart": {"message": "View data for a whole year"}, "lang_data_tj": {"message": "Statistics Current Data"}, "lang_180_sales": {"message": "180 Days' Sales"}, "lang_7_sales": {"message": "7 Days' Sales"}, "lang_total_wishlist": {"message": "Total Wishlists"}, "lang_total_reviews": {"message": "Total Reviews"}, "lang_product_word": {"message": "Product Words Cloud"}, "lang_180_sales_chart": {"message": "180 Days' Sales Trend Chart"}, "lang_store_name": {"message": "Store"}, "lang_products": {"message": "Products"}, "lang_view_data": {"message": "View Sales Trend Chart By IXSPY"}, "lang_no_data": {"message": "No Data"}, "lang_no_login": {"message": "Please Login in First"}, "lang_no_permissions": {"message": "BUSSINESS And Aliexpress Member Can be Use"}, "lang_product_ids_empty": {"message": "There is Some Error <PERSON>ppen"}, "lang_est_chart": {"message": "Estimated Sales Trend Chart"}, "lang_no_favted": {"message": "Add to Favorites By IXSPY"}, "lang_already_favted": {"message": "Collected"}, "lang_no_lu": {"message": "The product cannot be added to the collection without income"}, "lang_login_for_more": {"message": "Unlock more functions after Sign In"}, "lang_close": {"message": "Close"}, "lang_to_login": {"message": "Sign Up"}, "lang_to_reg": {"message": "Sign In"}, "lang_video_jc": {"message": "Video Tutorial"}, "lang_set_up": {"message": "Extension Installation"}, "lang_update_up": {"message": "Extension Update"}, "lang_click_play": {"message": "Click to play video"}, "lang_360_set": {"message": "360 Browser installation tutorial"}, "lang_chrome_set": {"message": "Google Chrome installation tutorial"}, "lang_qq_set": {"message": "QQ Browser installation tutorial"}, "lang_360_update": {"message": "360 Browser update tutorial"}, "lang_chrome_update": {"message": "Google Chrome update tutorial"}, "lang_qq_update": {"message": "QQ Browser update tutorial"}, "lang_is_collecting": {"message": "Collecting..."}, "lang_process": {"message": "Progress"}, "lang_yes": {"message": "Yes"}, "lang_no": {"message": "No"}, "lang_country": {"message": "Country"}, "lang_c_price": {"message": "Price difference"}, "lang_carrier": {"message": "Preferred logistics"}, "lang_is_by": {"message": "Free Shipping"}, "lang_carrier_fee": {"message": "Freight"}, "lang_c_msg": {"message": "The calculation of the spread is based on Russian prices"}, "lang_check_country_price": {"message": "Product Regional Pricing - Beta"}, "lang_export_excel": {"message": "Export to Excel"}, "lang_level_user": {"message": "You have used up the number of times, please upgrade to Aliexpress Supplier/Business member or wait until the next natural month to use"}, "lang_begin_collection_tip": {"message": "Currently used AAA times, and BBB times are available. This collection requires multiple page refreshes. It will take a few minutes. Please wait patiently..."}, "lang_ships_from": {"message": "Ships From"}, "lang_is_stop": {"message": "Stop Collection"}, "lang_begin_collection_tip_no_limit": {"message": "This collection requires multiple page refreshes. It will take a few minutes. Please wait patiently..."}, "lang_shuoming": {"message": "Description"}, "lang_shuoming_p_1": {"message": "1. To facilitate comparison, collect the first style uniformly"}, "lang_shuoming_p_2": {"message": "2. Uniformly calculate the price difference based on the Russian price"}, "lang_carrier_date": {"message": "Estimated Delivery on"}, "lang_repeat_collection": {"message": "Recollect"}, "lang_update_title": {"message": "Update Content"}, "RU": {"message": "Russian Federation"}, "ES": {"message": "Spain"}, "FR": {"message": "France"}, "US": {"message": "America"}, "CL": {"message": "Chile"}, "UA": {"message": "Ukraine"}, "NL": {"message": "Netherlands"}, "PL": {"message": "Poland"}, "BR": {"message": "Brazil"}, "IT": {"message": "Italy"}, "DE": {"message": "Germany"}, "KR": {"message": "Korea"}, "CA": {"message": "Canada"}, "UK": {"message": "United Kingdom"}, "IL": {"message": "Israel"}, "AU": {"message": "Australia"}, "BY": {"message": "Belarus"}, "JP": {"message": "Japan"}, "TH": {"message": "Thailand"}, "SG": {"message": "Singapore"}, "ID": {"message": "Indonesia"}, "MY": {"message": "Malaysia"}, "PH": {"message": "Philippines"}, "VN": {"message": "Viet Nam"}, "SA": {"message": "Saudi Arabia"}, "AE": {"message": "United Arab Emirates"}, "PT": {"message": "Portugal"}, "TR": {"message": "Turkey"}, "lang_all_country": {"message": "All Countrys"}, "lang_start_collection": {"message": "Start"}, "chose_country_checkboxs": {"message": "chose_country_checkboxs"}, "country_groups_list": {"message": "country-groups-list"}, "need_chose_countrys": {"message": "In addition to the required country, at least one country must be selected"}, "MX": {"message": "Mexico"}, "KZ": {"message": "Kazakhstan"}, "PE": {"message": "Peru"}, "CO": {"message": "Colombia"}, "DZ": {"message": "Algeria"}, "MA": {"message": "Morocco"}, "NZ": {"message": "New Zealand"}, "LV": {"message": "Latvia"}, "LT": {"message": "Lithuania"}, "lang_sku": {"message": "Based on XXX conditional collection"}, "day_7": {"message": "<div class='inline w-60'>7 Days</div>: <div class='inline color-red'> xxx sold </div>"}, "day_30": {"message": "<div class='inline w-60'>30 Days</div>: <div class='inline color-red'> xxx sold </div>"}, "look_more": {"message": "More..."}, "order_sales": {"message": "Estimated Sales"}, "lang_180_day_sales_bh": {"message": "180天销量日变化"}, "order_avg_7": {"message": "Avg Orders"}, "order_rate_7": {"message": "Order Growth Rate"}, "favted_avg_7": {"message": "Avg Wishlists"}, "favted_rate_7": {"message": "Wishlist Growth Rate"}, "first_date": {"message": "First Discovery Time"}, "class_seven_value": {"message": "seven-value-en"}, "class_seven_label": {"message": "seven-label-en"}, "seven_label_title": {"message": "seven_label_title_en"}, "curr_lang": {"message": "en"}, "country_dis": {"message": "TOP3 Country"}, "click_favted_product": {"message": "Click on Favorites"}, "already_favted_product": {"message": "Collected"}, "already_favted_store": {"message": "Collected"}, "my_persion_center": {"message": "Personal Center"}, "lang_favted_product": {"message": "Aliexpress Product"}, "lang_favted_store": {"message": "Aliexpress Store"}, "lang_open_monitor": {"message": "Turn on Monitoring"}, "lang_check_monitor": {"message": "View Monitoring"}, "lang_monitor_res": {"message": "Monitoring Results Display"}, "lang_support": {"message": "Support By IXSPY.COM"}, "lang_monitor_time": {"message": "Monitoring Time"}, "lang_more_monitor_res": {"message": "More Monitoring Results..."}, "lang_analytics": {"message": "Analytics"}}