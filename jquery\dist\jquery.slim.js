/*!
 * jQuery JavaScript Library v3.6.0 -ajax,-ajax/jsonp,-ajax/load,-ajax/script,-ajax/var/location,-ajax/var/nonce,-ajax/var/rquery,-ajax/xhr,-manipulation/_evalUrl,-deprecated/ajax-event-alias,-effects,-effects/Tween,-effects/animatedSelector
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2021-03-02T17:08Z
 */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?
// For CommonJS and CommonJS-like environments where a proper `window`
// is present, execute the factory and get jQuery.
// For environments that do not have a `window` with a `document`
// (such as Node.js), expose a factory as module.exports.
// This accentuates the need for the creation of a real `window`.
// e.g. var jQuery = require("jquery")(window);
// See ticket #14549 for more info.
module.exports=e.document?t(e,!0):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e);
// Pass this if window is not defined yet
}("undefined"!=typeof window?window:this,function(g,I){
// Edge <= 12 - 13+, Firefox <=18 - 45+, IE 10 - 11, Safari 5.1 - 9+, iOS 6 - 9.1
// throw exceptions when non-strict code (e.g., ASP.NET 4.5) accesses strict mode
// arguments.callee.caller (trac-13335). But as of jQuery 3.0 (2016), strict mode should be common
// enough that all such attempts are guarded in a try block.
"use strict";function v(e){
// Support: Chrome <=57, Firefox <=52
// In some browsers, typeof returns "function" for HTML <object> elements
// (i.e., `typeof document.createElement( "object" ) === "function"`).
// We don't want to classify *any* DOM node as a function.
// Support: QtWeb <=3.8.5, WebKit <=534.34, wkhtmltopdf tool <=0.12.5
// Plus for old WebKit, typeof returns "function" for HTML collections
// (e.g., `typeof document.getElementsByTagName("div") === "function"`). (gh-4756)
return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item}function m(e){return null!=e&&e===e.window}var t=[],R=Object.getPrototypeOf,s=t.slice,B=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},M=t.push,W=t.indexOf,F={},$=F.toString,z=F.hasOwnProperty,_=z.toString,U=_.call(Object),y={},b=g.document,V={type:!0,src:!0,nonce:!0,noModule:!0};function X(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in V)
// Support: Firefox 64+, Edge 18+
// Some browsers don't support the "nonce" property on scripts.
// On the other hand, just using `getAttribute` is not enough as
// the `nonce` attribute is reset to an empty string whenever it
// becomes browsing-context connected.
// See https://github.com/whatwg/html/issues/2369
// See https://html.spec.whatwg.org/#nonce-attributes
// The `node.getAttribute` check was added for the sake of
// `jQuery.globalEval` so that it can fake a nonce-containing node
// via an object.
(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function h(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?F[$.call(e)]||"object":typeof e;
// Support: Android <=2.3 only (functionish RegExp)
}
/* global Symbol */
// Defining this global in .eslintrc.json would create a danger of using the global
// unguarded in another place, it seems safer to define global only for this module
var e="3.6.0 -ajax,-ajax/jsonp,-ajax/load,-ajax/script,-ajax/var/location,-ajax/var/nonce,-ajax/var/rquery,-ajax/xhr,-manipulation/_evalUrl,-deprecated/ajax-event-alias,-effects,-effects/Tween,-effects/animatedSelector",
// Define a local copy of jQuery
x=function(e,t){
// The jQuery object is actually just the init constructor 'enhanced'
// Need init if jQuery is called (just allow error to be thrown if not included)
return new x.fn.init(e,t)};function Q(e){
// Support: real iOS 8.2 only (not reproducible in simulator)
// `in` check used to prevent JIT error (gh-2145)
// hasOwn isn't used here due to false negatives
// regarding Nodelist length in IE
var t=!!e&&"length"in e&&e.length,n=h(e);return!v(e)&&!m(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}x.fn=x.prototype={
// The current version of jQuery being used
jquery:e,constructor:x,
// The default length of a jQuery object is 0
length:0,toArray:function(){return s.call(this)},
// Get the Nth element in the matched element set OR
// Get the whole matched element set as a clean array
get:function(e){
// Return all the elements in a clean array
return null==e?s.call(this):e<0?this[e+this.length]:this[e];
// Return just the one element from the set
},
// Take an array of elements and push it onto the stack
// (returning the new matched element set)
pushStack:function(e){
// Build a new jQuery matched element set
e=x.merge(this.constructor(),e);
// Add the old object onto the stack (as a reference)
// Return the newly-formed element set
return e.prevObject=this,e},
// Execute a callback for every element in the matched set.
each:function(e){return x.each(this,e)},map:function(n){return this.pushStack(x.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(x.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(x.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},
// For internal use only.
// Behaves like an Array's method, not like a jQuery method.
push:M,sort:t.sort,splice:t.splice},x.extend=x.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,u=!1;
// Handle a deep copy situation
for("boolean"==typeof o&&(u=o,
// Skip the boolean and the target
o=arguments[a]||{},a++),
// Handle case when target is a string or something (possible in deep copy)
"object"==typeof o||v(o)||(o={}),
// Extend jQuery itself if only one argument is passed
a===s&&(o=this,a--);a<s;a++)
// Only deal with non-null/undefined values
if(null!=(e=arguments[a]))
// Extend the base object
for(t in e)n=e[t],
// Prevent Object.prototype pollution
// Prevent never-ending loop
"__proto__"!==t&&o!==n&&(
// Recurse if we're merging plain objects or arrays
u&&n&&(x.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[t],
// Ensure proper type for the source value
i=r&&!Array.isArray(i)?[]:r||x.isPlainObject(i)?i:{},r=!1,
// Never move original objects, clone them
o[t]=x.extend(u,i,n)):void 0!==n&&(o[t]=n));
// Return the modified object
return o},x.extend({
// Unique for each copy of jQuery on the page
expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),
// Assume jQuery is ready without the ready module
isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){
// Detect obvious negatives
// Use toString instead of jQuery.type to catch host objects
return!(!e||"[object Object]"!==$.call(e)||
// Objects with no prototype (e.g., `Object.create( null )`) are plain
(e=R(e))&&("function"!=typeof(
// Objects with prototype are plain iff they were constructed by a global Object function
e=z.call(e,"constructor")&&e.constructor)||_.call(e)!==U))},isEmptyObject:function(e){for(var t in e)return!1;return!0},
// Evaluates a script in a provided context; falls back to the global one
// if not specified.
globalEval:function(e,t,n){X(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(Q(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},
// results is for internal usage only
makeArray:function(e,t){t=t||[];return null!=e&&(Q(Object(e))?x.merge(t,"string"==typeof e?[e]:e):M.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:W.call(t,e,n)},
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){
// Go through the array, only saving the items
// that pass the validator function
for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},
// arg is for internal usage only
map:function(e,t,n){var r,i,o=0,a=[];
// Go through the array, translating each of the items to their new values
if(Q(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);
// Go through every key on the object,
else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);
// Flatten any nested arrays
return B(a)},
// A global GUID counter for objects
guid:1,
// jQuery.support is not used in Core but other projects attach their
// properties to it so it needs to exist.
support:y}),"function"==typeof Symbol&&(x.fn[Symbol.iterator]=t[Symbol.iterator]),
// Populate the class2type map
x.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){F["[object "+t+"]"]=t.toLowerCase()});function r(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&x(e).is(n))break;r.push(e)}return r}function Y(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var e=
/*!
 * Sizzle CSS Selector Engine v2.3.6
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://js.foundation/
 *
 * Date: 2021-02-16
 */
function(I){function f(e,t){return e="0x"+e.slice(1)-65536,t||(
// Replace a hexadecimal escape sequence with the encoded Unicode code point
// Support: IE <=11+
// For values outside the Basic Multilingual Plane (BMP), manually construct a
// surrogate pair
e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function R(e,t){return t?
// U+0000 NULL becomes U+FFFD REPLACEMENT CHARACTER
"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e;
// Other potentially-special ASCII characters get backslash-escaped
}function
// Used for iframes
// See setDocument()
// Removing the function wrapper causes a "Permission Denied"
// error in IE
B(){C()}var e,d,x,o,M,p,W,F,w,u,l,
// Local document vars
C,T,n,E,h,r,i,g,
// Instance-specific data
A="sizzle"+ +new Date,c=I.document,N=0,$=0,z=j(),_=j(),U=j(),v=j(),V=function(e,t){return e===t&&(l=!0),0},
// Instance methods
X={}.hasOwnProperty,t=[],Q=t.pop,Y=t.push,S=t.push,G=t.slice,
// Use a stripped-down indexOf as it's faster than native
// https://jsperf.com/thor-indexof-vs-for/5
y=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},K="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",
// Regular expressions
// http://www.w3.org/TR/css3-selectors/#whitespace
a="[\\x20\\t\\r\\n\\f]",
// https://www.w3.org/TR/css-syntax-3/#ident-token-diagram
s="(?:\\\\[\\da-fA-F]{1,6}"+a+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",
// Attribute selectors: http://www.w3.org/TR/selectors/#attribute-selectors
J="\\["+a+"*("+s+")(?:"+a+
// Operator (capture 2)
"*([*^$|!~]?=)"+a+
// "Attribute values must be CSS identifiers [capture 5]
// or strings [capture 3 or capture 4]"
"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+s+"))|)"+a+"*\\]",Z=":("+s+
// 2. simple (capture 6)
")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+J+")*)|.*)\\)|)",
// Leading and non-escaped trailing whitespace, capturing some non-whitespace characters preceding the latter
ee=new RegExp(a+"+","g"),m=new RegExp("^"+a+"+|((?:^|[^\\\\])(?:\\\\.)*)"+a+"+$","g"),te=new RegExp("^"+a+"*,"+a+"*"),ne=new RegExp("^"+a+"*([>+~]|"+a+")"+a+"*"),re=new RegExp(a+"|>"),ie=new RegExp(Z),oe=new RegExp("^"+s+"$"),b={ID:new RegExp("^#("+s+")"),CLASS:new RegExp("^\\.("+s+")"),TAG:new RegExp("^("+s+"|[*])"),ATTR:new RegExp("^"+J),PSEUDO:new RegExp("^"+Z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+a+"*(even|odd|(([+-]|)(\\d*)n|)"+a+"*(?:([+-]|)"+a+"*(\\d+)|))"+a+"*\\)|)","i"),bool:new RegExp("^(?:"+K+")$","i"),
// For use in libraries implementing .is()
// We use this for POS matching in `select`
needsContext:new RegExp("^"+a+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+a+"*((?:-\\d)?\\d*)"+a+"*\\)|)(?=[^-]|$)","i")},ae=/HTML$/i,se=/^(?:input|select|textarea|button)$/i,ue=/^h\d$/i,k=/^[^{]+\{\s*\[native \w/,
// Easily-parseable/retrievable ID or TAG or CLASS selectors
le=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ce=/[+~]/,
// CSS escapes
// http://www.w3.org/TR/CSS21/syndata.html#escaped-characters
D=new RegExp("\\\\[\\da-fA-F]{1,6}"+a+"?|\\\\([^\\r\\n\\f])","g"),
// CSS string/identifier serialization
// https://drafts.csswg.org/cssom/#common-serializing-idioms
fe=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,de=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});
// Optimize for push.apply( _, NodeList )
try{S.apply(t=G.call(c.childNodes),c.childNodes),
// Support: Android<4.0
// Detect silently failing push.apply
// eslint-disable-next-line no-unused-expressions
t[c.childNodes.length].nodeType}catch(e){S={apply:t.length?
// Leverage slice if possible
function(e,t){Y.apply(e,G.call(t))}:
// Support: IE<9
// Otherwise append directly
function(e,t){
// Can't trust NodeList.length
for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function L(t,e,n,r){var i,o,a,s,u,l,c=e&&e.ownerDocument,
// nodeType defaults to 9, since context defaults to document
f=e?e.nodeType:9;
// Return early from calls with invalid selector or context
if(n=n||[],"string"!=typeof t||!t||1!==f&&9!==f&&11!==f)return n;
// Try to shortcut find operations (as opposed to filters) in HTML documents
if(!r&&(C(e),e=e||T,E)){
// If the selector is sufficiently simple, try using a "get*By*" DOM method
// (excepting DocumentFragment context, where the methods don't exist)
if(11!==f&&(s=le.exec(t)))
// ID selector
if(i=s[1]){
// Document context
if(9===f){if(!(l=e.getElementById(i)))return n;
// Element context
// Support: IE, Opera, Webkit
// TODO: identify versions
// getElementById can match elements by name instead of ID
if(l.id===i)return n.push(l),n}else
// Support: IE, Opera, Webkit
// TODO: identify versions
// getElementById can match elements by name instead of ID
if(c&&(l=c.getElementById(i))&&g(e,l)&&l.id===i)return n.push(l),n;
// Type selector
}else{if(s[2])return S.apply(n,e.getElementsByTagName(t)),n;
// Class selector
if((i=s[3])&&d.getElementsByClassName&&e.getElementsByClassName)return S.apply(n,e.getElementsByClassName(i)),n}
// Take advantage of querySelectorAll
if(d.qsa&&!v[t+" "]&&(!h||!h.test(t))&&(
// Support: IE 8 only
// Exclude object elements
1!==f||"object"!==e.nodeName.toLowerCase())){
// qSA considers elements outside a scoping root when evaluating child or
// descendant combinators, which is not what we want.
// In such cases, we work around the behavior by prefixing every selector in the
// list with an ID selector referencing the scope context.
// The technique has to be used as well when a leading combinator is used
// as such selectors are not recognized by querySelectorAll.
// Thanks to Andrew Dupont for this technique.
if(l=t,c=e,1===f&&(re.test(t)||ne.test(t))){for(
// Expand context for sibling selectors
// We can use :scope instead of the ID hack if the browser
// supports it & if we're not changing the context.
(c=ce.test(t)&&ve(e.parentNode)||e)===e&&d.scope||(
// Capture the context ID, setting it first if necessary
(a=e.getAttribute("id"))?a=a.replace(fe,R):e.setAttribute("id",a=A)),o=(
// Prefix every selector in the list
u=p(t)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+H(u[o]);l=u.join(",")}try{return S.apply(n,c.querySelectorAll(l)),n}catch(e){v(t,!0)}finally{a===A&&e.removeAttribute("id")}}}
// All others
return F(t.replace(m,"$1"),e,n,r)}
/**
 * Create key-value caches of limited size
 * @returns {function(string, object)} Returns the Object data after storing it on itself with
 *	property name the (space-suffixed) string and (if the cache is larger than Expr.cacheLength)
 *	deleting the oldest entry
 */function j(){var n=[];function r(e,t){
// Use (key + " ") to avoid collision with native prototype properties (see Issue #157)
return n.push(e+" ")>x.cacheLength&&
// Only keep the most recent entries
delete r[n.shift()],r[e+" "]=t}return r}
/**
 * Mark a function for special use by Sizzle
 * @param {Function} fn The function to mark
 */function q(e){return e[A]=!0,e}
/**
 * Support testing using an element
 * @param {Function} fn Passed the created element and returns a boolean result
 */function O(e){var t=T.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{
// Remove from its parent by default
t.parentNode&&t.parentNode.removeChild(t);
// release memory in IE
}}
/**
 * Adds the same handler for all of the specified attrs
 * @param {String} attrs Pipe-separated list of attributes
 * @param {Function} handler The method that will be applied
 */function pe(e,t){for(var n=e.split("|"),r=n.length;r--;)x.attrHandle[n[r]]=t}
/**
 * Checks document order of two siblings
 * @param {Element} a
 * @param {Element} b
 * @returns {Number} Returns less than 0 if a precedes b, greater than 0 if a follows b
 */function he(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;
// Use IE sourceIndex if available on both nodes
if(r)return r;
// Check if b follows a
if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}
/**
 * Returns a function to use in pseudos for input types
 * @param {String} type
 */
/**
 * Returns a function to use in pseudos for :enabled/:disabled
 * @param {Boolean} disabled true for :disabled; false for :enabled
 */
function ge(t){
// Known :disabled false positives: fieldset[disabled] > legend:nth-of-type(n+2) :can-disable
return function(e){
// Only certain elements can match :enabled or :disabled
// https://html.spec.whatwg.org/multipage/scripting.html#selector-enabled
// https://html.spec.whatwg.org/multipage/scripting.html#selector-disabled
return"form"in e?
// Check for inherited disabledness on relevant non-disabled elements:
// * listed form-associated elements in a disabled fieldset
//   https://html.spec.whatwg.org/multipage/forms.html#category-listed
//   https://html.spec.whatwg.org/multipage/forms.html#concept-fe-disabled
// * option elements in a disabled optgroup
//   https://html.spec.whatwg.org/multipage/forms.html#concept-option-disabled
// All such elements have a "form" property.
e.parentNode&&!1===e.disabled?
// Option elements defer to a parent optgroup if present
"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||
// Where there is no isDisabled, check manually
/* jshint -W018 */
e.isDisabled!==!t&&de(e)===t:e.disabled===t:"label"in e&&e.disabled===t;
// Remaining elements are neither :enabled nor :disabled
}}
/**
 * Returns a function to use in pseudos for positionals
 * @param {Function} fn
 */function P(a){return q(function(o){return o=+o,q(function(e,t){
// Match elements found at the specified indexes
for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}
/**
 * Checks a node for validity as a Sizzle context
 * @param {Element|Object=} context
 * @returns {Element|Object|Boolean} The input node if acceptable, otherwise a falsy value
 */function ve(e){return e&&void 0!==e.getElementsByTagName&&e}
// Expose support vars for convenience
// Add button/input type pseudos
for(e in d=L.support={},
/**
 * Detects XML nodes
 * @param {Element|Object} elem An element or a document
 * @returns {Boolean} True iff elem is a non-HTML XML node
 */
M=L.isXML=function(e){var t=e&&e.namespaceURI,e=e&&(e.ownerDocument||e).documentElement;
// Support: IE <=8
// Assume HTML when documentElement doesn't yet exist, such as inside loading iframes
// https://bugs.jquery.com/ticket/4833
return!ae.test(t||e&&e.nodeName||"HTML")},
/**
 * Sets document-related variables once based on the current document
 * @param {Element|Object} [doc] An element or document object to use to set the document
 * @returns {Object} Returns the current document
 */
C=L.setDocument=function(e){var e=e?e.ownerDocument||e:c;
// Return early if doc is invalid or already selected
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
return e!=T&&9===e.nodeType&&e.documentElement&&(
// Update global variables
n=(T=e).documentElement,E=!M(T),
// Support: IE 9 - 11+, Edge 12 - 18+
// Accessing iframe documents after unload throws "permission denied" errors (jQuery #13936)
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
c!=T&&(e=T.defaultView)&&e.top!==e&&(
// Support: IE 11, Edge
e.addEventListener?e.addEventListener("unload",B,!1):e.attachEvent&&e.attachEvent("onunload",B)),
// Support: IE 8 - 11+, Edge 12 - 18+, Chrome <=16 - 25 only, Firefox <=3.6 - 31 only,
// Safari 4 - 5 only, Opera <=11.6 - 12.x only
// IE/Edge & older browsers don't support the :scope pseudo-class.
// Support: Safari 6.0 only
// Safari 6.0 supports :scope but it's an alias of :root there.
d.scope=O(function(e){return n.appendChild(e).appendChild(T.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),
/* Attributes
	---------------------------------------------------------------------- */
// Support: IE<8
// Verify that getAttribute really returns attributes and not properties
// (excepting IE8 booleans)
d.attributes=O(function(e){return e.className="i",!e.getAttribute("className")}),
/* getElement(s)By*
	---------------------------------------------------------------------- */
// Check if getElementsByTagName("*") returns only elements
d.getElementsByTagName=O(function(e){return e.appendChild(T.createComment("")),!e.getElementsByTagName("*").length}),
// Support: IE<9
d.getElementsByClassName=k.test(T.getElementsByClassName),
// Support: IE<10
// Check if getElementById returns elements by name
// The broken getElementById methods don't pick up programmatically-set names,
// so use a roundabout getElementsByName test
d.getById=O(function(e){return n.appendChild(e).id=A,!T.getElementsByName||!T.getElementsByName(A).length}),
// ID filter and find
d.getById?(x.filter.ID=function(e){var t=e.replace(D,f);return function(e){return e.getAttribute("id")===t}},x.find.ID=function(e,t){if(void 0!==t.getElementById&&E)return(t=t.getElementById(e))?[t]:[]}):(x.filter.ID=function(e){var t=e.replace(D,f);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},
// Support: IE 6 - 7 only
// getElementById is not reliable as a find shortcut
x.find.ID=function(e,t){if(void 0!==t.getElementById&&E){var n,r,i,o=t.getElementById(e);if(o){if((
// Verify the id attribute
n=o.getAttributeNode("id"))&&n.value===e)return[o];
// Fall back on getElementsByName
for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),
// Tag
x.find.TAG=d.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):d.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,
// By happy coincidence, a (broken) gEBTN appears on DocumentFragment nodes too
o=t.getElementsByTagName(e);
// Filter out possible comments
if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},
// Class
x.find.CLASS=d.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&E)return t.getElementsByClassName(e)},
/* QSA/matchesSelector
	---------------------------------------------------------------------- */
// QSA and matchesSelector support
// matchesSelector(:active) reports false when true (IE9/Opera 11.5)
r=[],
// qSa(:focus) reports false when true (Chrome 21)
// We allow this because of a bug in IE8/9 that throws an error
// whenever `document.activeElement` is accessed on an iframe
// So, we allow :focus to pass through QSA all the time to avoid the IE error
// See https://bugs.jquery.com/ticket/13378
h=[],(d.qsa=k.test(T.querySelectorAll))&&(
// Build QSA regex
// Regex strategy adopted from Diego Perini
O(function(e){var t;
// Select is set to empty string on purpose
// This is to test IE's treatment of not explicitly
// setting a boolean content attribute,
// since its presence should be enough
// https://bugs.jquery.com/ticket/12359
n.appendChild(e).innerHTML="<a id='"+A+"'></a><select id='"+A+"-\r\\' msallowcapture=''><option selected=''></option></select>",
// Support: IE8, Opera 11-12.16
// Nothing should be selected when empty strings follow ^= or $= or *=
// The test attribute must be unknown in Opera but "safe" for WinRT
// https://msdn.microsoft.com/en-us/library/ie/hh465388.aspx#attribute_section
e.querySelectorAll("[msallowcapture^='']").length&&h.push("[*^$]="+a+"*(?:''|\"\")"),
// Support: IE8
// Boolean attributes and "value" are not treated correctly
e.querySelectorAll("[selected]").length||h.push("\\["+a+"*(?:value|"+K+")"),
// Support: Chrome<29, Android<4.4, Safari<7.0+, iOS<7.0+, PhantomJS<1.9.8+
e.querySelectorAll("[id~="+A+"-]").length||h.push("~="),(
// Support: IE 11+, Edge 15 - 18+
// IE 11/Edge don't find elements on a `[name='']` query in some cases.
// Adding a temporary attribute to the document before the selection works
// around the issue.
// Interestingly, IE 10 & older don't seem to have the issue.
t=T.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||h.push("\\["+a+"*name"+a+"*="+a+"*(?:''|\"\")"),
// Webkit/Opera - :checked should return selected option elements
// http://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked
// IE8 throws error here and will not see later tests
e.querySelectorAll(":checked").length||h.push(":checked"),
// Support: Safari 8+, iOS 8+
// https://bugs.webkit.org/show_bug.cgi?id=136851
// In-page `selector#id sibling-combinator selector` fails
e.querySelectorAll("a#"+A+"+*").length||h.push(".#.+[+~]"),
// Support: Firefox <=3.6 - 5 only
// Old Firefox doesn't throw on a badly-escaped identifier.
e.querySelectorAll("\\\f"),h.push("[\\r\\n\\f]")}),O(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";
// Support: Windows 8 Native Apps
// The type and name attributes are restricted during .innerHTML assignment
var t=T.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),
// Support: IE8
// Enforce case-sensitivity of name attribute
e.querySelectorAll("[name=d]").length&&h.push("name"+a+"*[*^$|!~]?="),
// FF 3.5 - :enabled/:disabled and hidden elements (hidden elements are still enabled)
// IE8 throws error here and will not see later tests
2!==e.querySelectorAll(":enabled").length&&h.push(":enabled",":disabled"),
// Support: IE9-11+
// IE's :disabled selector does not pick up the children of disabled fieldsets
n.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),
// Support: Opera 10 - 11 only
// Opera 10-11 does not throw on post-comma invalid pseudos
e.querySelectorAll("*,:x"),h.push(",.*:")})),(d.matchesSelector=k.test(i=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.oMatchesSelector||n.msMatchesSelector))&&O(function(e){
// Check to see if it's possible to do matchesSelector
// on a disconnected node (IE 9)
d.disconnectedMatch=i.call(e,"*"),
// This should fail with an exception
// Gecko does not error, returns false instead
i.call(e,"[s!='']:x"),r.push("!=",Z)}),h=h.length&&new RegExp(h.join("|")),r=r.length&&new RegExp(r.join("|")),
/* Contains
	---------------------------------------------------------------------- */
e=k.test(n.compareDocumentPosition),
// Element contains another
// Purposefully self-exclusive
// As in, an element does not contain itself
g=e||k.test(n.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},
/* Sorting
	---------------------------------------------------------------------- */
// Document order sorting
V=e?function(e,t){
// Flag for duplicate removal
var n;
// Sort on method existence if only one input has compareDocumentPosition
return e===t?(l=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(
// Disconnected nodes
1&(
// Calculate position if both inputs belong to the same document
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):
// Otherwise we know they are disconnected
1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?
// Choose the first element that is related to our preferred document
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
e==T||e.ownerDocument==c&&g(c,e)?-1:
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
t==T||t.ownerDocument==c&&g(c,t)?1:u?y(u,e)-y(u,t):0:4&n?-1:1)}:function(e,t){
// Exit early if the nodes are identical
if(e===t)return l=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];
// Parentless nodes are either documents or disconnected
if(!i||!o)
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
/* eslint-disable eqeqeq */
return e==T?-1:t==T?1:
/* eslint-enable eqeqeq */
i?-1:o?1:u?y(u,e)-y(u,t):0;
// If the nodes are siblings, we can do a quick check
// Otherwise we need full lists of their ancestors for comparison
if(i===o)return he(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);
// Walk down the tree looking for a discrepancy
for(;a[r]===s[r];)r++;return r?
// Do a sibling check if the nodes have a common ancestor
he(a[r],s[r]):
// Otherwise nodes in our document sort first
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
/* eslint-disable eqeqeq */
a[r]==c?-1:s[r]==c?1:
/* eslint-enable eqeqeq */
0}),T},L.matches=function(e,t){return L(e,null,null,t)},L.matchesSelector=function(e,t){if(C(e),d.matchesSelector&&E&&!v[t+" "]&&(!r||!r.test(t))&&(!h||!h.test(t)))try{var n=i.call(e,t);
// IE 9's matchesSelector returns false on disconnected nodes
if(n||d.disconnectedMatch||
// As well, disconnected nodes are said to be in a document
// fragment in IE 9
e.document&&11!==e.document.nodeType)return n}catch(e){v(t,!0)}return 0<L(t,T,null,[e]).length},L.contains=function(e,t){
// Set document vars if needed
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
return(e.ownerDocument||e)!=T&&C(e),g(e,t)},L.attr=function(e,t){
// Set document vars if needed
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
(e.ownerDocument||e)!=T&&C(e);var n=x.attrHandle[t.toLowerCase()],
// Don't get fooled by Object.prototype properties (jQuery #13807)
n=n&&X.call(x.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==n?n:d.attributes||!E?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},L.escape=function(e){return(e+"").replace(fe,R)},L.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},
/**
 * Document sorting and removing duplicates
 * @param {ArrayLike} results
 */
L.uniqueSort=function(e){var t,n=[],r=0,i=0;
// Unless we *know* we can detect duplicates, assume their presence
if(l=!d.detectDuplicates,u=!d.sortStable&&e.slice(0),e.sort(V),l){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}
// Clear input after sorting to release objects
// See https://github.com/jquery/sizzle/pull/225
return u=null,e},
/**
 * Utility function for retrieving the text value of an array of DOM nodes
 * @param {Array|Element} elem
 */
o=L.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){
// Use textContent for elements
// innerText usage removed for consistency of new lines (jQuery #11153)
if("string"==typeof e.textContent)return e.textContent;
// Traverse its children
for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue;
// Do not include comment or processing instruction nodes
}else
// If no nodeType, this is expected to be an array
for(;t=e[r++];)
// Do not traverse comment nodes
n+=o(t);return n},(x=L.selectors={
// Can be adjusted by the user
cacheLength:50,createPseudo:q,match:b,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(D,f),
// Move the given value to match[3] whether quoted or unquoted
e[3]=(e[3]||e[4]||e[5]||"").replace(D,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){
/* matches from matchExpr["CHILD"]
				1 type (only|nth|...)
				2 what (child|of-type)
				3 argument (even|odd|\d*|\d*n([+-]\d+)?|...)
				4 xn-component of xn+y argument ([+-]?\d*n|)
				5 sign of xn-component
				6 x of xn-component
				7 sign of y-component
				8 y of y-component
			*/
return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(
// nth-* requires argument
e[3]||L.error(e[0]),
// numeric x and y parameters for Expr.filter.CHILD
// remember that false/true cast respectively to 0/1
e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&L.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return b.CHILD.test(e[0])?null:(
// Accept quoted arguments as-is
e[3]?e[2]=e[4]||e[5]||"":n&&ie.test(n)&&(
// Get excess from tokenize (recursively)
t=(t=p(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(
// excess is a negative index
e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(D,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=z[e+" "];return t||(t=new RegExp("(^|"+a+")"+e+"("+a+"|$)"))&&z(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=L.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(ee," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,g,v){var m="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===v?
// Shortcut for :nth-*(n)
function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u,l=m!=y?"nextSibling":"previousSibling",c=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b,p=!1;if(c){
// :(first|last|only)-(child|of-type)
if(m){for(;l;){for(a=e;a=a[l];)if(b?a.nodeName.toLowerCase()===f:1===a.nodeType)return!1;
// Reverse direction for :only-* (if we haven't yet done so)
u=l="only"===h&&!u&&"nextSibling"}return!0}
// non-xml :nth-child(...) stores cache data on `parent`
if(u=[y?c.firstChild:c.lastChild],y&&d){for(
// Seek `elem` from a previously-cached index
// ...in a gzip-friendly way
p=(s=(r=(
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
i=(o=(a=c)[A]||(a[A]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===N&&r[1])&&r[2],a=s&&c.childNodes[s];a=++s&&a&&a[l]||(
// Fallback to seeking `elem` from the start
p=s=0,u.pop());)
// When found, cache indexes on `parent` and break
if(1===a.nodeType&&++p&&a===e){i[h]=[N,s,p];break}}else
// xml :nth-child(...)
// or :nth-last-child(...) or :nth(-last)?-of-type(...)
if(!1===(
// Use previously-cached element index if available
p=d?s=(r=(
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
i=(o=(
// ...in a gzip-friendly way
a=e)[A]||(a[A]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===N&&r[1]:p))
// Use the same loop as above to seek `elem` from the start
for(;(a=++s&&a&&a[l]||(p=s=0,u.pop()))&&((b?a.nodeName.toLowerCase()!==f:1!==a.nodeType)||!++p||(
// Cache the index of each encountered element
d&&((
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
i=(o=a[A]||(a[A]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]=[N,p]),a!==e)););
// Incorporate the offset, then check against cycle size
return(p-=v)===g||p%g==0&&0<=p/g}}},PSEUDO:function(e,o){
// pseudo-class names are case-insensitive
// http://www.w3.org/TR/selectors/#pseudo-classes
// Prioritize by case sensitivity in case custom pseudos are added with uppercase letters
// Remember that setFilters inherits from pseudos
var t,a=x.pseudos[e]||x.setFilters[e.toLowerCase()]||L.error("unsupported pseudo: "+e);
// The user may use createPseudo to indicate that
// arguments are needed to create the filter function
// just as Sizzle does
return a[A]?a(o):
// But maintain support for old signatures
1<a.length?(t=[e,e,"",o],x.setFilters.hasOwnProperty(e.toLowerCase())?q(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=y(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{
// Potentially complex pseudos
not:q(function(e){
// Trim the selector passed to compile
// to avoid treating leading and trailing
// spaces as combinators
var r=[],i=[],s=W(e.replace(m,"$1"));return s[A]?q(function(e,t,n,r){
// Match elements unmatched by `matcher`
for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),
// Don't keep the element (issue #299)
r[0]=null,!i.pop()}}),has:q(function(t){return function(e){return 0<L(t,e).length}}),contains:q(function(t){return t=t.replace(D,f),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),
// "Whether an element is represented by a :lang() selector
// is based solely on the element's language value
// being equal to the identifier C,
// or beginning with the identifier C immediately followed by "-".
// The matching of C against the element's language value is performed case-insensitively.
// The identifier C does not have to be a valid language name."
// http://www.w3.org/TR/selectors/#lang-pseudo
lang:q(function(n){
// lang value must be a valid identifier
return oe.test(n||"")||L.error("unsupported lang: "+n),n=n.replace(D,f).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),
// Miscellaneous
target:function(e){var t=I.location&&I.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===n},focus:function(e){return e===T.activeElement&&(!T.hasFocus||T.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},
// Boolean properties
enabled:ge(!1),disabled:ge(!0),checked:function(e){
// In CSS3, :checked should return both checked and selected elements
// http://www.w3.org/TR/2011/REC-css3-selectors-20110929/#checked
var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){
// Accessing this property makes selected-by-default
// options in Safari work properly
return e.parentNode&&
// eslint-disable-next-line no-unused-expressions
e.parentNode.selectedIndex,!0===e.selected},
// Contents
empty:function(e){
// http://www.w3.org/TR/selectors/#empty-pseudo
// :empty is negated by element (1) or content nodes (text: 3; cdata: 4; entity ref: 5),
//   but not by others (comment: 8; processing instruction: 7; etc.)
// nodeType < 6 works because attributes (2) do not appear as children
for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!x.pseudos.empty(e)},
// Element/input types
header:function(e){return ue.test(e.nodeName)},input:function(e){return se.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(
// Support: IE<8
// New HTML5 attribute values (e.g., "search") appear with elem.type === "text"
null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},
// Position-in-collection
first:P(function(){return[0]}),last:P(function(e,t){return[t-1]}),eq:P(function(e,t,n){return[n<0?n+t:n]}),even:P(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:P(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:P(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:P(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=x.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}
/**
 * Returns a function to use in pseudos for buttons
 * @param {String} type
 */(e);for(e in{submit:!0,reset:!0})x.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);
// Easy API for creating new setFilters
function me(){}function H(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ye(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&"parentNode"===l,f=$++;return e.first?
// Check against closest ancestor/preceding element
function(e,t,n){for(;e=e[s];)if(1===e.nodeType||c)return a(e,t,n);return!1}:
// Check against all ancestor/preceding elements
function(e,t,n){var r,i,o=[N,f];
// We can't set arbitrary data on XML nodes, so they don't benefit from combinator caching
if(n){for(;e=e[s];)if((1===e.nodeType||c)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||c)if(
// Support: IE <9 only
// Defend against cloned attroperties (jQuery gh-1709)
i=(i=e[A]||(e[A]={}))[e.uniqueID]||(i[e.uniqueID]={}),u&&u===e.nodeName.toLowerCase())e=e[s]||e;else{if((r=i[l])&&r[0]===N&&r[1]===f)
// Assign to newCache so results back-propagate to previous elements
return o[2]=r[2];
// A match means we're done; a fail means we have to keep checking
if((
// Reuse newcache so results back-propagate to previous elements
i[l]=o)[2]=a(e,t,n))return!0}return!1}}function be(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function xe(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)!(o=e[s])||n&&!n(o,r,i)||(a.push(o),l&&t.push(s));return a}function we(p,h,g,v,m,e){return v&&!v[A]&&(v=we(v)),m&&!m[A]&&(m=we(m,e)),q(function(e,t,n,r){var i,o,a,s=[],u=[],l=t.length,
// Get initial elements from seed or context
c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)L(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),
// Prefilter to get matcher input, preserving a map for seed-results synchronization
f=!p||!e&&h?c:xe(c,s,p,n,r),d=g?
// If we have a postFinder, or filtered seed, or non-seed postFilter or preexisting results,
m||(e?p:l||v)?
// ...intermediate processing is necessary
[]:
// ...otherwise use results directly
t:f;
// Find primary matches
// Apply postFilter
if(g&&g(f,d,n,r),v)for(i=xe(d,u),v(i,[],n,r),
// Un-match failing elements by moving them back to matcherIn
o=i.length;o--;)(a=i[o])&&(d[u[o]]=!(f[u[o]]=a));if(e){if(m||p){if(m){for(
// Get the final matcherOut by condensing this intermediate into postFinder contexts
i=[],o=d.length;o--;)(a=d[o])&&
// Restore matcherIn since elem is not yet a final match
i.push(f[o]=a);m(null,d=[],i,r)}
// Move matched elements from seed to results to keep them synchronized
for(o=d.length;o--;)(a=d[o])&&-1<(i=m?y(e,a):s[o])&&(e[i]=!(t[i]=a))}
// Add elements to results, through postFinder if defined
}else d=xe(d===t?d.splice(l,d.length):d),m?m(null,t,d,r):S.apply(t,d)})}function Ce(v,m){function e(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],d=w,
// We must always have either seed elements or outermost context
p=e||b&&x.find.TAG("*",i),
// Use integer dirruns iff this is the outermost matcher
h=N+=null==d?1:Math.random()||.1,g=p.length;
// Add elements passing elementMatchers directly to results
// Support: IE<9, Safari
// Tolerate NodeList properties (IE: "length"; Safari: <number>) matching elements by id
for(i&&(
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
w=t==T||t||i);l!==g&&null!=(o=p[l]);l++){if(b&&o){for(a=0,
// Support: IE 11+, Edge 17 - 18+
// IE/Edge sometimes throw a "Permission denied" error when strict-comparing
// two documents; shallow comparisons work.
// eslint-disable-next-line eqeqeq
t||o.ownerDocument==T||(C(o),n=!E);s=v[a++];)if(s(o,t||T,n)){r.push(o);break}i&&(N=h)}
// Track unmatched elements for set filters
y&&(
// They will have gone through all possible matchers
(o=!s&&o)&&u--,e)&&c.push(o)}
// `i` is now the count of elements visited above, and adding it to `matchedCount`
// makes the latter nonnegative.
// Apply set filters to unmatched elements
// NOTE: This can be skipped if there are no unmatched elements (i.e., `matchedCount`
// equals `i`), unless we didn't visit _any_ elements in the above loop because we have
// no element matchers and no seed.
// Incrementing an initially-string "0" `i` allows `i` to remain a string only in that
// case, which will result in a "00" `matchedCount` that differs from `i` but is also
// numerically zero.
if(u+=l,y&&l!==u){for(a=0;s=m[a++];)s(c,f,t,n);if(e){
// Reintegrate element matches to eliminate the need for sorting
if(0<u)for(;l--;)c[l]||f[l]||(f[l]=Q.call(r));
// Discard index placeholder values to get only actual matches
f=xe(f)}
// Add matches to results
S.apply(r,f),
// Seedless set matches succeeding multiple successful matchers stipulate sorting
i&&!e&&0<f.length&&1<u+m.length&&L.uniqueSort(r)}
// Override manipulation of globals by nested matchers
return i&&(N=h,w=d),c}var y=0<m.length,b=0<v.length;return y?q(e):e}return me.prototype=x.filters=x.pseudos,x.setFilters=new me,p=L.tokenize=function(e,t){var n,r,i,o,a,s,u,l=_[e+" "];if(l)return t?0:l.slice(0);for(a=e,s=[],u=x.preFilter;a;){
// Filters
for(o in
// Comma and first run
n&&!(r=te.exec(a))||(r&&(
// Don't consume trailing commas as valid
a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,
// Combinators
(r=ne.exec(a))&&(n=r.shift(),i.push({value:n,
// Cast descendant combinators to space
type:r[0].replace(m," ")}),a=a.slice(n.length)),x.filter)!(r=b[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}
// Return the length of the invalid excess
// if we're just parsing
// Otherwise, throw an error or return tokens
return t?a.length:a?L.error(e):
// Cache the tokens
_(e,s).slice(0)},W=L.compile=function(e,t/* Internal Use Only */){var n,r=[],i=[],o=U[e+" "];if(!o){for(n=(
// Generate a function of recursive functions that can be used to check each element
t=t||p(e)).length;n--;)((o=function e(t){for(var r,n,i,o=t.length,a=x.relative[t[0].type],s=a||x.relative[" "],u=a?1:0,
// The foundational matcher ensures that elements are reachable from top-level context(s)
l=ye(function(e){return e===r},s,!0),c=ye(function(e){return-1<y(r,e)},s,!0),f=[function(e,t,n){return e=!a&&(n||t!==w)||((r=t).nodeType?l:c)(e,t,n),
// Avoid hanging onto element (issue #299)
r=null,e}];u<o;u++)if(n=x.relative[t[u].type])f=[ye(be(f),n)];else{
// Return special upon seeing a positional matcher
if((n=x.filter[t[u].type].apply(null,t[u].matches))[A]){for(
// Find the next relative operator (if any) for proper handling
i=++u;i<o&&!x.relative[t[i].type];i++);return we(1<u&&be(f),1<u&&H(
// If the preceding token was a descendant combinator, insert an implicit any-element `*`
t.slice(0,u-1).concat({value:" "===t[u-2].type?"*":""})).replace(m,"$1"),n,u<i&&e(t.slice(u,i)),i<o&&e(t=t.slice(i)),i<o&&H(t))}f.push(n)}return be(f)}(t[n]))[A]?r:i).push(o);
// Cache the compiled function
// Save selector and tokenization
(o=U(e,Ce(i,r))).selector=e}return o},
/**
 * A low-level selection function that works with Sizzle's compiled
 *  selector functions
 * @param {String|Function} selector A selector or a pre-compiled
 *  selector function built with Sizzle.compile
 * @param {Element} context
 * @param {Array} [results]
 * @param {Array} [seed] A set of elements to match against
 */
F=L.select=function(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&p(e=l.selector||e);
// Try to minimize operations if there is only one selector in the list and no seed
// (the latter of which guarantees us context)
if(n=n||[],1===c.length){if(2<(
// Reduce context if the leading compound selector is an ID
o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&E&&x.relative[o[1].type]){if(!(t=(x.find.ID(a.matches[0].replace(D,f),t)||[])[0]))return n;
// Precompiled matchers will still verify ancestry, so step up a level
l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}
// Fetch a seed set for right-to-left matching
for(i=b.needsContext.test(e)?0:o.length;i--&&(a=o[i],!x.relative[s=a.type]);)if((u=x.find[s])&&(r=u(a.matches[0].replace(D,f),ce.test(o[0].type)&&ve(t.parentNode)||t))){if(
// If seed is empty or no tokens remain, we can return early
o.splice(i,1),e=r.length&&H(o))break;return S.apply(n,r),n}}
// Compile and execute a filtering function if one is not provided
// Provide `match` to avoid retokenization if we modified the selector above
return(l||W(e,c))(r,t,!E,n,!t||ce.test(e)&&ve(t.parentNode)||t),n},
// One-time assignments
// Sort stability
d.sortStable=A.split("").sort(V).join("")===A,
// Support: Chrome 14-35+
// Always assume duplicates if they aren't passed to the comparison function
d.detectDuplicates=!!l,
// Initialize against the default document
C(),
// Support: Webkit<537.32 - Safari 6.0.3/Chrome 25 (fixed in Chrome 27)
// Detached nodes confoundingly follow *each other*
d.sortDetached=O(function(e){
// Should return 1, but returns 4 (following)
return 1&e.compareDocumentPosition(T.createElement("fieldset"))}),
// Support: IE<8
// Prevent attribute/property "interpolation"
// https://msdn.microsoft.com/en-us/library/ms536429%28VS.85%29.aspx
O(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||pe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),
// Support: IE<9
// Use defaultValue in place of getAttribute("value")
d.attributes&&O(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||pe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),
// Support: IE<9
// Use getAttributeNode to fetch booleans when getAttribute lies
O(function(e){return null==e.getAttribute("disabled")})||pe(K,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),L}(g),G=(x.find=e,x.expr=e.selectors,
// Deprecated
x.expr[":"]=x.expr.pseudos,x.uniqueSort=x.unique=e.uniqueSort,x.text=e.getText,x.isXMLDoc=e.isXML,x.contains=e.contains,x.escapeSelector=e.escape,x.expr.match.needsContext);function u(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var K=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;
// Implement the identical functionality for filter and not
function J(e,n,r){return v(n)?x.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):
// Single element
n.nodeType?x.grep(e,function(e){return e===n!==r}):
// Arraylike of elements (jQuery, arguments, Array)
"string"!=typeof n?x.grep(e,function(e){return-1<W.call(n,e)!==r}):x.filter(n,e,r)}x.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?x.find.matchesSelector(r,e)?[r]:[]:x.find.matches(e,x.grep(t,function(e){return 1===e.nodeType}))},x.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(x(e).filter(function(){for(t=0;t<r;t++)if(x.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)x.find(e,i[t],n);return 1<r?x.uniqueSort(n):n},filter:function(e){return this.pushStack(J(this,e||[],!1))},not:function(e){return this.pushStack(J(this,e||[],!0))},is:function(e){return!!J(this,
// If this is a positional/relative selector, check membership in the returned set
// so $("p:first").is("p:last") won't return true for a doc with two "p".
"string"==typeof e&&G.test(e)?x(e):e||[],!1).length}});
// Initialize a jQuery object
// A central reference to the root jQuery(document)
var Z,
// A simple way to check for HTML strings
// Prioritize #id over <tag> to avoid XSS via location.hash (#9521)
// Strict HTML recognition (#11290: must start with <)
// Shortcut simple #id case for speed
ee=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,te=(
// Give the init function the jQuery prototype for later instantiation
(x.fn.init=function(e,t,n){
// HANDLE: $(""), $(null), $(undefined), $(false)
if(e){
// Handle HTML strings
if(
// Method init() accepts an alternate rootjQuery
// so migrate can support jQuery.sub (gh-2101)
n=n||Z,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):
// Execute immediately if ready is not present
e(x):x.makeArray(e,this);
// Match html or make sure no context is specified for #id
if(!(
// Assume that strings that start and end with <> are HTML and skip the regex check
r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:ee.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);
// HANDLE: $(DOMElement)
// HANDLE: $(html) -> $(array)
if(r[1]){
// HANDLE: $(html, props)
if(t=t instanceof x?t[0]:t,
// Option to run scripts is true for back-compat
// Intentionally let the error be thrown if parseHTML is not present
x.merge(this,x.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),K.test(r[1])&&x.isPlainObject(t))for(var r in t)
// Properties of context are called as methods if possible
v(this[r])?this[r](t[r]):this.attr(r,t[r])}else(n=b.getElementById(r[2]))&&(
// Inject the element directly into the jQuery object
this[0]=n,this.length=1);
// HANDLE: $(expr, $(...))
}return this}).prototype=x.fn,
// Initialize central reference
Z=x(b),/^(?:parents|prev(?:Until|All))/),
// Methods guaranteed to produce a unique set when starting from a unique set
ne={children:!0,contents:!0,next:!0,prev:!0};function re(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}x.fn.extend({has:function(e){var t=x(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(x.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&x(e);
// Positional selectors never match, since there's no _selection_ context
if(!G.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)
// Always skip document fragments
if(n.nodeType<11&&(a?-1<a.index(n):
// Don't pass non-elements to Sizzle
1===n.nodeType&&x.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?x.uniqueSort(o):o)},
// Determine the position of an element within the set
index:function(e){
// No argument, return index in parent
return e?
// Index in selector
"string"==typeof e?W.call(x(e),this[0]):W.call(this,
// If it receives a jQuery object, the first element is used
e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(x.uniqueSort(x.merge(this.get(),x(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),x.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return r(e,"parentNode")},parentsUntil:function(e,t,n){return r(e,"parentNode",n)},next:function(e){return re(e,"nextSibling")},prev:function(e){return re(e,"previousSibling")},nextAll:function(e){return r(e,"nextSibling")},prevAll:function(e){return r(e,"previousSibling")},nextUntil:function(e,t,n){return r(e,"nextSibling",n)},prevUntil:function(e,t,n){return r(e,"previousSibling",n)},siblings:function(e){return Y((e.parentNode||{}).firstChild,e)},children:function(e){return Y(e.firstChild)},contents:function(e){return null!=e.contentDocument&&
// Support: IE 11+
// <object> elements with no `data` attribute has an object
// `contentDocument` with a `null` prototype.
R(e.contentDocument)?e.contentDocument:(
// Support: IE 9 - 11 only, iOS 7 only, Android Browser <=4.3 only
// Treat the template element as a regular one in browsers that
// don't support it.
u(e,"template")&&(e=e.content||e),x.merge([],e.childNodes))}},function(r,i){x.fn[r]=function(e,t){var n=x.map(this,i,e);return(t="Until"!==r.slice(-5)?e:t)&&"string"==typeof t&&(n=x.filter(t,n)),1<this.length&&(
// Remove duplicates
ne[r]||x.uniqueSort(n),te.test(r))&&n.reverse(),this.pushStack(n)}});var w=/[^\x20\t\r\n\f]+/g;
// Convert String-formatted options into Object-formatted ones
function c(e){return e}function ie(e){throw e}function oe(e,t,n,r){var i;try{
// Check for promise aspect first to privilege synchronous behavior
e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):
// Control `resolve` arguments by letting Array#slice cast boolean `noValue` to integer:
// * false: [ value ].slice( 0 ) => resolve( value )
// * true: [ value ].slice( 1 ) => resolve()
t.apply(void 0,[e].slice(r));
// For Promises/A+, convert exceptions into rejections
// Since jQuery.when doesn't unwrap thenables, we can skip the extra checks appearing in
// Deferred#then to conditionally suppress rejection.
}catch(e){
// Support: Android 4.0 only
// Strict mode functions invoked without .call/.apply get global-object context
n.apply(void 0,[e])}}
/*
 * Create a callback list using the following parameters:
 *
 *	options: an optional list of space-separated options that will change how
 *			the callback list behaves or a more traditional option object
 *
 * By default a callback list will act like an event callback list and can be
 * "fired" multiple times.
 *
 * Possible options:
 *
 *	once:			will ensure the callback list can only be fired once (like a Deferred)
 *
 *	memory:			will keep track of previous values and will call any callback added
 *					after the list has been fired right away with the latest "memorized"
 *					values (like a Deferred)
 *
 *	unique:			will ensure a callback can only be added once (no duplicate in the list)
 *
 *	stopOnFalse:	interrupt callings when a callback returns false
 *
 */
x.Callbacks=function(r){var e,n;
// Convert options from String-formatted to Object-formatted if needed
// (we check in cache first)
r="string"==typeof r?(e=r,n={},x.each(e.match(w)||[],function(e,t){n[t]=!0}),n):x.extend({},r);function
// Fire callbacks
i(){for(
// Enforce single-firing
s=s||r.once,
// Execute callbacks for all pending executions,
// respecting firingIndex overrides and runtime changes
a=o=!0;l.length;c=-1)for(t=l.shift();++c<u.length;)
// Run callback and check for early termination
!1===u[c].apply(t[0],t[1])&&r.stopOnFalse&&(
// Jump to end and forget the data so .add doesn't re-fire
c=u.length,t=!1);
// Forget the data if we're done with it
r.memory||(t=!1),o=!1,
// Clean up if we're done firing for good
s&&(
// Keep an empty list if we have data for future add calls
u=t?[]:"")}var// Flag to know if list is currently firing
o,
// Last fire value for non-forgettable lists
t,
// Flag to know if list was already fired
a,
// Flag to prevent firing
s,
// Actual callback list
u=[],
// Queue of execution data for repeatable lists
l=[],
// Index of currently firing callback (modified by add/remove as needed)
c=-1,
// Actual Callbacks object
f={
// Add a callback or a collection of callbacks to the list
add:function(){return u&&(
// If we have memory from a past run, we should fire after adding
t&&!o&&(c=u.length-1,l.push(t)),function n(e){x.each(e,function(e,t){v(t)?r.unique&&f.has(t)||u.push(t):t&&t.length&&"string"!==h(t)&&
// Inspect recursively
n(t)})}(arguments),t)&&!o&&i(),this},
// Remove a callback from the list
remove:function(){return x.each(arguments,function(e,t){for(var n;-1<(n=x.inArray(t,u,n));)u.splice(n,1),
// Handle firing indexes
n<=c&&c--}),this},
// Check if a given callback is in the list.
// If no argument is given, return whether or not list has callbacks attached.
has:function(e){return e?-1<x.inArray(e,u):0<u.length},
// Remove all callbacks from the list
empty:function(){return u=u&&[],this},
// Disable .fire and .add
// Abort any current/pending executions
// Clear all callbacks and values
disable:function(){return s=l=[],u=t="",this},disabled:function(){return!u},
// Disable .fire
// Also disable .add unless we have memory (since it would have no effect)
// Abort any pending executions
lock:function(){return s=l=[],t||o||(u=t=""),this},locked:function(){return!!s},
// Call all callbacks with the given context and arguments
fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o)||i(),this},
// Call all the callbacks with the given arguments
fire:function(){return f.fireWith(this,arguments),this},
// To know if the callbacks have already been called at least once
fired:function(){return!!a}};return f},x.extend({Deferred:function(e){var o=[
// action, add listener, callbacks,
// ... .then handlers, argument index, [final state]
["notify","progress",x.Callbacks("memory"),x.Callbacks("memory"),2],["resolve","done",x.Callbacks("once memory"),x.Callbacks("once memory"),0,"resolved"],["reject","fail",x.Callbacks("once memory"),x.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},
// Keep pipe for back-compat
pipe:function(){var i=arguments;return x.Deferred(function(r){x.each(o,function(e,t){
// Map tuples (progress, done, fail) to arguments (done, fail, progress)
var n=v(i[t[4]])&&i[t[4]];
// deferred.progress(function() { bind to newDefer or newDefer.notify })
// deferred.done(function() { bind to newDefer or newDefer.resolve })
// deferred.fail(function() { bind to newDefer or newDefer.reject })
s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&v(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){function e(){var e,t;
// Support: Promises/A+ section *******.3
// https://promisesaplus.com/#point-59
// Ignore double-resolution attempts
if(!(i<u)){
// Support: Promises/A+ section 2.3.1
// https://promisesaplus.com/#point-48
if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");
// Support: Promises/A+ sections 2.3.3.1, 3.5
// https://promisesaplus.com/#point-54
// https://promisesaplus.com/#point-75
// Retrieve `then` only once
t=e&&(
// Support: Promises/A+ section 2.3.4
// https://promisesaplus.com/#point-64
// Only check objects and functions for thenability
"object"==typeof e||"function"==typeof e)&&e.then,
// Handle a returned thenable
v(t)?
// Special processors (notify) just wait for resolution
s?t.call(e,l(u,o,c,s),l(u,o,ie,s)):(
// ...and disregard older resolution values
u++,t.call(e,l(u,o,c,s),l(u,o,ie,s),l(u,o,c,o.notifyWith))):(
// Only substitute handlers pass on context
// and multiple values (non-spec behavior)
a!==c&&(n=void 0,r=[e]),
// Process the value(s)
// Default process is resolve
(s||o.resolveWith)(n,r))}}var n=this,r=arguments,
// Only normal processors (resolve) catch and reject exceptions
t=s?e:function(){try{e()}catch(e){x.Deferred.exceptionHook&&x.Deferred.exceptionHook(e,t.stackTrace),
// Support: Promises/A+ section *******.4.1
// https://promisesaplus.com/#point-61
// Ignore post-resolution exceptions
u<=i+1&&(
// Only substitute handlers pass on context
// and multiple values (non-spec behavior)
a!==ie&&(n=void 0,r=[e]),o.rejectWith(n,r))}};
// Support: Promises/A+ section *******.1
// https://promisesaplus.com/#point-57
// Re-resolve promises immediately to dodge false rejection from
// subsequent errors
i?t():(
// Call an optional hook to record the stack, in case of exception
// since it's otherwise lost when execution goes async
x.Deferred.getStackHook&&(t.stackTrace=x.Deferred.getStackHook()),g.setTimeout(t))}}return x.Deferred(function(e){
// progress_handlers.add( ... )
o[0][3].add(l(0,e,v(r)?r:c,e.notifyWith)),
// fulfilled_handlers.add( ... )
o[1][3].add(l(0,e,v(t)?t:c)),
// rejected_handlers.add( ... )
o[2][3].add(l(0,e,v(n)?n:ie))}).promise()},
// Get a promise for this deferred
// If obj is provided, the promise aspect is added to the object
promise:function(e){return null!=e?x.extend(e,a):a}},s={};
// Add list-specific methods
// All done!
return x.each(o,function(e,t){var n=t[2],r=t[5];
// promise.progress = list.add
// promise.done = list.add
// promise.fail = list.add
a[t[1]]=n.add,
// Handle state
r&&n.add(function(){
// state = "resolved" (i.e., fulfilled)
// state = "rejected"
i=r},
// rejected_callbacks.disable
// fulfilled_callbacks.disable
o[3-e][2].disable,
// rejected_handlers.disable
// fulfilled_handlers.disable
o[3-e][3].disable,
// progress_callbacks.lock
o[0][2].lock,
// progress_handlers.lock
o[0][3].lock),
// progress_handlers.fire
// fulfilled_handlers.fire
// rejected_handlers.fire
n.add(t[3].fire),
// deferred.notify = function() { deferred.notifyWith(...) }
// deferred.resolve = function() { deferred.resolveWith(...) }
// deferred.reject = function() { deferred.rejectWith(...) }
s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},
// deferred.notifyWith = list.fireWith
// deferred.resolveWith = list.fireWith
// deferred.rejectWith = list.fireWith
s[t[0]+"With"]=n.fireWith}),
// Make the deferred a promise
a.promise(s),
// Call given func if any
e&&e.call(s,s),s},
// Deferred helper
when:function(e){function
// subordinate callback factory
t(t){return function(e){i[t]=this,o[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(i,o)}}var
// count of uncompleted subordinates
n=arguments.length,
// count of unprocessed arguments
r=n,
// subordinate fulfillment data
i=Array(r),o=s.call(arguments),
// the primary Deferred
a=x.Deferred();
// Single- and empty arguments are adopted like Promise.resolve
if(n<=1&&(oe(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||v(o[r]&&o[r].then)))return a.then();
// Multiple arguments are aggregated like Promise.all array elements
for(;r--;)oe(o[r],t(r),a.reject);return a.promise()}});
// These usually indicate a programmer mistake during development,
// warn about them ASAP rather than swallowing them by default.
var ae=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,se=(x.Deferred.exceptionHook=function(e,t){
// Support: IE 8 - 9 only
// Console exists when dev tools are open, which can happen at any time
g.console&&g.console.warn&&e&&ae.test(e.name)&&g.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},x.readyException=function(e){g.setTimeout(function(){throw e})},x.Deferred());
// The ready event handler and self cleanup method
function ue(){b.removeEventListener("DOMContentLoaded",ue),g.removeEventListener("load",ue),x.ready()}
// Catch cases where $(document).ready() is called
// after the browser event has already occurred.
// Support: IE <=9 - 10 only
// Older IE sometimes signals "interactive" too soon
x.fn.ready=function(e){return se.then(e).catch(function(e){x.readyException(e)}),this},x.extend({
// Is the DOM ready to be used? Set to true once it occurs.
isReady:!1,
// A counter to track how many items to wait for before
// the ready event fires. See #6781
readyWait:1,
// Handle when the DOM is ready
ready:function(e){
// Abort if there are pending holds or we're already ready
(!0===e?--x.readyWait:x.isReady)||(
// Remember that the DOM is ready
x.isReady=!0)!==e&&0<--x.readyWait||
// If there are functions bound, to execute
se.resolveWith(b,[x])}}),x.ready.then=se.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?
// Handle it asynchronously to allow scripts the opportunity to delay ready
g.setTimeout(x.ready):(
// Use the handy event callback
b.addEventListener("DOMContentLoaded",ue),
// A fallback to window.onload, that will always work
g.addEventListener("load",ue));function f(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;
// Sets many values
if("object"===h(n))for(s in i=!0,n)f(e,t,s,n[s],!0,o,a);
// Sets one value
else if(void 0!==r&&(i=!0,v(r)||(a=!0),t=l?
// Bulk operations run against the entire set
a?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(x(e),n)}):t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:
// Gets
l?t.call(e):u?t(e[0],n):o}
// Multifunctional method to get and set values of a collection
// The value/s can optionally be executed if it's a function
var le=/^-ms-/,ce=/-([a-z])/g;
// Matches dashed string for camelizing
// Used by camelCase as callback to replace()
function fe(e,t){return t.toUpperCase()}
// Convert dashed to camelCase; used by the css and data modules
// Support: IE <=9 - 11, Edge 12 - 15
// Microsoft forgot to hump their vendor prefix (#9572)
function d(e){return e.replace(le,"ms-").replace(ce,fe)}function C(e){
// Accepts only:
//  - Node
//    - Node.ELEMENT_NODE
//    - Node.DOCUMENT_NODE
//  - Object
//    - Any
return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function n(){this.expando=x.expando+n.uid++}n.uid=1,n.prototype={cache:function(e){
// Check if the owner object already has a cache
var t=e[this.expando];
// If not, create one
return t||(t={},
// We can accept data for non-element nodes in modern browsers,
// but we should not, see #8335.
// Always return an empty object.
C(e)&&(
// If it is a node unlikely to be stringify-ed or looped over
// use plain assignment
e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);
// Handle: [ owner, key, value ] args
// Always use camelCase key (gh-2257)
if("string"==typeof t)i[d(t)]=n;
// Handle: [ owner, { properties } ] args
else
// Copy the properties one-by-one to the cache object
for(r in t)i[d(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):
// Always use camelCase key (gh-2257)
e[this.expando]&&e[this.expando][d(t)]},access:function(e,t,n){
// In cases where either:
//
//   1. No key was specified
//   2. A string key was specified, but no value provided
//
// Take the "read" path and allow the get method to determine
// which value to return, respectively either:
//
//   1. The entire cache object
//   2. The data stored at the key
//
return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(
// When the key is not a string, or both a key and value
// are specified, set or extend (existing objects) with either:
//
//   1. An object of properties
//   2. A key and value
//
this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(
// Support array or space separated string of keys
// If key is an array of keys...
// We always set camelCase keys, so remove that.
t=Array.isArray(t)?t.map(d):(t=d(t))in r?[t]:t.match(w)||[]).length;for(;n--;)delete r[t[n]]}
// Remove the expando if there's no more data
void 0!==t&&!x.isEmptyObject(r)||(
// Support: Chrome <=35 - 45
// Webkit & Blink performance suffers when deleting properties
// from DOM nodes, so set to undefined instead
// https://bugs.chromium.org/p/chromium/issues/detail?id=378607 (bug restricted)
e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!x.isEmptyObject(e)}};var T=new n,l=new n,de=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,pe=/[A-Z]/g;function he(e,t,n){var r,i;
// If nothing was found internally, try to fetch any
// data from the HTML5 data-* attribute
if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(pe,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:
// Only convert to a number if it doesn't change the string
i===+i+""?+i:de.test(i)?JSON.parse(i):i)}catch(e){}
// Make sure we set the data so it isn't changed later
l.set(e,t,n)}else n=void 0;return n}x.extend({hasData:function(e){return l.hasData(e)||T.hasData(e)},data:function(e,t,n){return l.access(e,t,n)},removeData:function(e,t){l.remove(e,t)},
// TODO: Now that all calls to _data and _removeData have been replaced
// with direct calls to dataPriv methods, these can be deprecated.
_data:function(e,t,n){return T.access(e,t,n)},_removeData:function(e,t){T.remove(e,t)}}),x.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;
// Gets all values
if(void 0!==n)
// Sets multiple values
return"object"==typeof n?this.each(function(){l.set(this,n)}):f(this,function(e){var t;
// The calling jQuery object (element matches) is not empty
// (and therefore has an element appears at this[ 0 ]) and the
// `value` parameter was not undefined. An empty jQuery object
// will result in `undefined` for elem = this[ 0 ] which will
// throw an exception if an attempt to read a data cache is made.
if(o&&void 0===e)return void 0!==(
// Attempt to get data from the cache
// The key will always be camelCased in Data
t=l.get(o,n))||void 0!==(
// Attempt to "discover" the data in
// HTML5 custom data-* attrs
t=he(o,n))?t:
// We tried really hard, but the data doesn't exist.
void 0;
// Set the data...
this.each(function(){
// We always store the camelCased key
l.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=l.get(o),1===o.nodeType)&&!T.get(o,"hasDataAttrs")){for(t=a.length;t--;)
// Support: IE 11 only
// The attrs elements can be null (#14894)
a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=d(r.slice(5)),he(o,r,i[r]));T.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){l.remove(this,e)})}}),x.extend({queue:function(e,t,n){var r;if(e)return r=T.get(e,t=(t||"fx")+"queue"),
// Speed up dequeue by getting out quickly if this is just a lookup
n&&(!r||Array.isArray(n)?r=T.access(e,t,x.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=x.queue(e,t),r=n.length,i=n.shift(),o=x._queueHooks(e,t);
// If the fx queue is dequeued, always remove the progress sentinel
"inprogress"===i&&(i=n.shift(),r--),i&&(
// Add a progress sentinel to prevent the fx queue from being
// automatically dequeued
"fx"===t&&n.unshift("inprogress"),
// Clear up the last queue stop function
delete o.stop,i.call(e,function(){x.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},
// Not public - generate a queueHooks object, or return the current one
_queueHooks:function(e,t){var n=t+"queueHooks";return T.get(e,n)||T.access(e,n,{empty:x.Callbacks("once memory").add(function(){T.remove(e,[t+"queue",n])})})}}),x.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?x.queue(this[0],t):void 0===n?this:this.each(function(){var e=x.queue(this,t,n);
// Ensure a hooks for this queue
x._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&x.dequeue(this,t)})},dequeue:function(e){return this.each(function(){x.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},
// Get a promise resolved when queues of a certain type
// are emptied (fx is the type by default)
promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=x.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=T.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});function ge(e,t){
// Inline style trumps all
return"none"===(
// isHiddenWithinTree might be called from jQuery#filter function;
// in that case, element will be second argument
e=t||e).style.display||""===e.style.display&&
// Otherwise, check computed style
// Support: Firefox <=43 - 45
// Disconnected elements can have computed display: none, so first confirm that elem is
// in the document.
A(e)&&"none"===x.css(e,"display")}var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ve=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),p=["Top","Right","Bottom","Left"],E=b.documentElement,A=function(e){return x.contains(e.ownerDocument,e)},me={composed:!0};
// Support: IE 9 - 11+, Edge 12 - 18+, iOS 10.0 - 10.2 only
// Check attachment across shadow DOM boundaries when possible (gh-3504)
// Support: iOS 10.0-10.2 only
// Early iOS 10 versions support `attachShadow` but not `getRootNode`,
// leading to errors. We need to check for `getRootNode`.
E.getRootNode&&(A=function(e){return x.contains(e.ownerDocument,e)||e.getRootNode(me)===e.ownerDocument});var ye={};function be(e,t){
// Determine new display value for elements that need to change
for(var n,r,i,o,a,s=[],u=0,l=e.length;u<l;u++)(r=e[u]).style&&(n=r.style.display,t?(
// Since we force visibility upon cascade-hidden elements, an immediate (and slow)
// check is required in this first loop unless we have a nonempty display value (either
// inline or about-to-be-restored)
"none"===n&&(s[u]=T.get(r,"display")||null,s[u]||(r.style.display="")),""===r.style.display&&ge(r)&&(s[u]=(a=o=void 0,o=(i=r).ownerDocument,i=i.nodeName,(a=ye[i])||(o=o.body.appendChild(o.createElement(i)),a=x.css(o,"display"),o.parentNode.removeChild(o),ye[i]=a="none"===a?"block":a),a))):"none"!==n&&(s[u]="none",
// Remember what we're overwriting
T.set(r,"display",n)));
// Set the display of the elements in a second loop to avoid constant reflow
for(u=0;u<l;u++)null!=s[u]&&(e[u].style.display=s[u]);return e}x.fn.extend({show:function(){return be(this,!0)},hide:function(){return be(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ge(this)?x(this).show():x(this).hide()})}});var N=/^(?:checkbox|radio)$/i,xe=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,we=/^$|^module$|\/(?:java|ecma)script/i,S=(O=b.createDocumentFragment().appendChild(b.createElement("div")),
// Support: Android 4.0 - 4.3 only
// Check state lost if the name is set (#11217)
// Support: Windows Web Apps (WWA)
// `name` and `type` must use .setAttribute for WWA (#14901)
(q=b.createElement("input")).setAttribute("type","radio"),q.setAttribute("checked","checked"),q.setAttribute("name","t"),O.appendChild(q),
// Support: Android <=4.1 only
// Older WebKit doesn't clone checked state correctly in fragments
y.checkClone=O.cloneNode(!0).cloneNode(!0).lastChild.checked,
// Support: IE <=11 only
// Make sure textarea (and checkbox) defaultValue is properly cloned
O.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!O.cloneNode(!0).lastChild.defaultValue,
// Support: IE <=9 only
// IE <=9 replaces <option> tags with their contents when inserted outside of
// the select element.
O.innerHTML="<option></option>",y.option=!!O.lastChild,{
// XHTML parsers do not magically insert elements in the
// same way that tag soup parsers do. So we cannot shorten
// this by omitting <tbody> or other required elements.
thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function k(e,t){
// Support: IE <=9 - 11 only
// Use typeof to avoid zero-argument method invocation on host objects (#15151)
var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&u(e,t)?x.merge([e],n):n}
// Mark scripts as having already been evaluated
function Ce(e,t){for(var n=0,r=e.length;n<r;n++)T.set(e[n],"globalEval",!t||T.get(t[n],"globalEval"))}S.tbody=S.tfoot=S.colgroup=S.caption=S.thead,S.th=S.td,
// Support: IE <=9 only
y.option||(S.optgroup=S.option=[1,"<select multiple='multiple'>","</select>"]);var Te=/<|&#?\w+;/;function Ee(e,t,n,r,i){for(var o,a,s,u,l,c=t.createDocumentFragment(),f=[],d=0,p=e.length;d<p;d++)if((o=e[d])||0===o)
// Add nodes directly
if("object"===h(o))
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
x.merge(f,o.nodeType?[o]:o);
// Convert non-html into a text node
else if(Te.test(o)){for(a=a||c.appendChild(t.createElement("div")),
// Deserialize a standard representation
s=(xe.exec(o)||["",""])[1].toLowerCase(),s=S[s]||S._default,a.innerHTML=s[1]+x.htmlPrefilter(o)+s[2],
// Descend through wrappers to the right content
l=s[0];l--;)a=a.lastChild;
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
x.merge(f,a.childNodes),
// Ensure the created nodes are orphaned (#12392)
(
// Remember the top-level container
a=c.firstChild).textContent=""}else f.push(t.createTextNode(o));
// Convert html into DOM nodes
// Remove wrapper from fragment
for(c.textContent="",d=0;o=f[d++];)
// Skip elements already in the context collection (trac-4087)
if(r&&-1<x.inArray(o,r))i&&i.push(o);else
// Capture executables
if(u=A(o),
// Append to fragment
a=k(c.appendChild(o),"script"),
// Preserve script evaluation history
u&&Ce(a),n)for(l=0;o=a[l++];)we.test(o.type||"")&&n.push(o);return c}var Ae=/^([^.]*)(?:\.(.+)|)/;function a(){return!0}function D(){return!1}
// Support: IE <=9 - 11+
// focus() and blur() are asynchronous, except when they are no-op.
// So expect focus to be synchronous when the element is already active,
// and blur to be synchronous when the element is not already active.
// (focus and blur are always synchronous in other supported browsers,
// this just defines when we can count on it).
function Ne(e,t){return e===
// Support: IE <=9 only
// Accessing document.activeElement can throw unexpectedly
// https://bugs.jquery.com/ticket/13393
function(){try{return b.activeElement}catch(e){}}()==("focus"===t)}function Se(e,t,n,r,i,o){var a,s;
// Types can be a map of types/handlers
if("object"==typeof t){for(s in
// ( types-Object, selector, data )
"string"!=typeof n&&(
// ( types-Object, data )
r=r||n,n=void 0),t)Se(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(
// ( types, fn )
i=n,r=n=void 0):null==i&&("string"==typeof n?(
// ( types, selector, fn )
i=r,r=void 0):(
// ( types, data, fn )
i=r,r=n,n=void 0)),!1===i)i=D;else if(!i)return e;return 1===o&&(a=i,
// Use same guid so caller can remove using origFn
(i=function(e){
// Can use an empty set, since event contains the info
return x().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=x.guid++)),e.each(function(){x.event.add(this,t,i,r,n)})}
/*
 * Helper functions for managing events -- not part of the public interface.
 * Props to Dean Edwards' addEvent library for many of the ideas.
 */
// Ensure the presence of an event listener that handles manually-triggered
// synthetic events by interrupting progress until reinvoked in response to
// *native* events that it fires directly, ensuring that state changes have
// already occurred before other listeners are invoked.
function ke(e,i,o){
// Missing expectSync indicates a trigger call, which must force setup through jQuery.event.add
o?(
// Register the controller as a special universal handler for all event namespaces
T.set(e,i,!1),x.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=T.get(this,i);if(1&e.isTrigger&&this[i]){
// Interrupt processing of the outer synthetic .trigger()ed event
// Saved data should be false in such cases, but might be a leftover capture object
// from an async native handler (gh-4350)
if(r.length)(x.event.special[i]||{}).delegateType&&e.stopPropagation();
// If this is a native event triggered above, everything is now in order
// Fire an inner synthetic event with the original arguments
else if(
// Store arguments for use when handling the inner native event
// There will always be at least one argument (an event object), so this array
// will not be confused with a leftover capture object.
r=s.call(arguments),T.set(this,i,r),
// Trigger the native event and capture its result
// Support: IE <=9 - 11+
// focus() and blur() are asynchronous
t=o(this,i),this[i](),r!==(n=T.get(this,i))||t?T.set(this,i,!1):n={},r!==n)
// Support: Chrome 86+
// In Chrome, if an element having a focusout handler is blurred by
// clicking outside of it, it invokes the handler synchronously. If
// that handler calls `.remove()` on the element, the data is cleared,
// leaving `result` undefined. We need to guard against this.
// Cancel the outer synthetic event
return e.stopImmediatePropagation(),e.preventDefault(),n&&n.value;
// If this is an inner synthetic event for an event with a bubbling surrogate
// (focus or blur), assume that the surrogate already propagated from triggering the
// native event and prevent that from happening again here.
// This technically gets the ordering wrong w.r.t. to `.trigger()` (in which the
// bubbling surrogate propagates *after* the non-bubbling base), but that seems
// less bad than duplication.
}else r.length&&(
// ...and capture the result
T.set(this,i,{value:x.event.trigger(
// Support: IE <=9 - 11+
// Extend with the prototype to reset the above stopImmediatePropagation()
x.extend(r[0],x.Event.prototype),r.slice(1),this)}),
// Abort handling of the native event
e.stopImmediatePropagation())}})):void 0===T.get(e,i)&&x.event.add(e,i,a)}x.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,d,p,h=T.get(t);
// Only attach events to objects that accept data
if(C(t))for(
// Caller can pass in an object of custom data in lieu of the handler
n.handler&&(n=(o=n).handler,i=o.selector),
// Ensure that invalid selectors throw exceptions at attach time
// Evaluate against documentElement in case elem is a non-element node (e.g., document)
i&&x.find.matchesSelector(E,i),
// Make sure that the handler has a unique ID, used to find/remove it later
n.guid||(n.guid=x.guid++),
// Init the element's event structure and main handler, if this is the first
s=(s=h.events)||(h.events=Object.create(null)),a=(a=h.handle)||(h.handle=function(e){
// Discard the second event of a jQuery.event.trigger() and
// when an event is called after a page has unloaded
return void 0!==x&&x.event.triggered!==e.type?x.event.dispatch.apply(t,arguments):void 0}),u=(
// Handle multiple events separated by a space
e=(e||"").match(w)||[""]).length;u--;)f=p=(d=Ae.exec(e[u])||[])[1],d=(d[2]||"").split(".").sort(),
// There *must* be a type, no attaching namespace-only handlers
f&&(
// If event changes its type, use the special event handlers for the changed type
l=x.event.special[f]||{},
// If selector defined, determine special event api type, otherwise given type
f=(i?l.delegateType:l.bindType)||f,
// Update special based on newly reset type
l=x.event.special[f]||{},
// handleObj is passed to all event handlers
p=x.extend({type:f,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&x.expr.match.needsContext.test(i),namespace:d.join(".")},o),
// Init the event handler queue if we're the first
(c=s[f])||((c=s[f]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(t,r,d,a))||t.addEventListener&&t.addEventListener(f,a),l.add&&(l.add.call(t,p),p.handler.guid||(p.handler.guid=n.guid)),
// Add to the element's handler list, delegates in front
i?c.splice(c.delegateCount++,0,p):c.push(p),
// Keep track of which events have ever been used, for event optimization
x.event.global[f]=!0)},
// Detach an event or set of events from an element
remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,d,p,h,g,v=T.hasData(e)&&T.get(e);if(v&&(u=v.events)){for(l=(
// Once for each type.namespace in types; type may be omitted
t=(t||"").match(w)||[""]).length;l--;)
// Unbind all events (on this namespace, if provided) for the element
if(p=g=(s=Ae.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),p){for(f=x.event.special[p]||{},d=u[p=(r?f.delegateType:f.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),
// Remove matching events
a=o=d.length;o--;)c=d[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));
// Remove generic event handler if we removed something and no more handlers exist
// (avoids potential for endless recursion during removal of special event handlers)
a&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||x.removeEvent(e,p,v.handle),delete u[p])}else for(p in u)x.event.remove(e,p+t[l],n,r,!0);
// Remove data and the expando if it's no longer used
x.isEmptyObject(u)&&T.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a=new Array(arguments.length),
// Make a writable jQuery.Event from the native event object
s=x.event.fix(e),e=(T.get(this,"events")||Object.create(null))[s.type]||[],u=x.event.special[s.type]||{};
// Use the fix-ed jQuery.Event rather than the (read-only) native event
for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];
// Call the preDispatch hook for the mapped type, and let it bail if desired
if(s.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,s)){for(
// Determine handlers
o=x.event.handlers.call(this,s,e),
// Run delegates first; they may want to stop propagation beneath us
t=0;(r=o[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!s.isImmediatePropagationStopped();)
// If the event is namespaced, then each handler is only invoked if it is
// specially universal or its namespaces are a superset of the event's.
s.rnamespace&&!1!==i.namespace&&!s.rnamespace.test(i.namespace)||(s.handleObj=i,s.data=i.data,void 0!==(i=((x.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(s.result=i)&&(s.preventDefault(),s.stopPropagation()));
// Call the postDispatch hook for the mapped type
return u.postDispatch&&u.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;
// Find delegate handlers
if(u&&
// Support: IE <=9
// Black-hole SVG <use> instance trees (trac-13180)
l.nodeType&&
// Support: Firefox <=42
// Suppress spec-violating clicks indicating a non-primary pointer button (trac-3861)
// https://www.w3.org/TR/DOM-Level-3-Events/#event-type-click
// Support: IE 11 only
// ...but not arrow key "clicks" of radio inputs, which can have `button` -1 (gh-2343)
!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)
// Don't check non-elements (#13208)
// Don't process clicks on disabled elements (#6911, #8165, #11382, #11764)
if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[
// Don't conflict with Object.prototype properties (#13203)
i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<x(i,this).index(l):x.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}
// Add the remaining (directly-bound) handlers
return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(x.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[x.expando]?e:new x.Event(e)},special:{load:{
// Prevent triggered image.load events from bubbling to window.load
noBubble:!0},click:{
// Utilize native event to ensure correct state for checkable inputs
setup:function(e){
// For mutual compressibility with _default, replace `this` access with a local var.
// `|| data` is dead code meant only to preserve the variable through minification.
e=this||e;
// Claim the first handler
// Return false to allow normal processing in the caller
return N.test(e.type)&&e.click&&u(e,"input")&&
// dataPriv.set( el, "click", ... )
ke(e,"click",a),!1},trigger:function(e){
// For mutual compressibility with _default, replace `this` access with a local var.
// `|| data` is dead code meant only to preserve the variable through minification.
e=this||e;
// Force setup before triggering a click
// Return non-false to allow normal event-path propagation
return N.test(e.type)&&e.click&&u(e,"input")&&ke(e,"click"),!0},
// For cross-browser consistency, suppress native .click() on links
// Also prevent it if we're currently inside a leveraged native-event stack
_default:function(e){e=e.target;return N.test(e.type)&&e.click&&u(e,"input")&&T.get(e,"click")||u(e,"a")}},beforeunload:{postDispatch:function(e){
// Support: Firefox 20+
// Firefox doesn't alert if the returnValue field is not set.
void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},x.removeEvent=function(e,t,n){
// This "if" is needed for plain objects
e.removeEventListener&&e.removeEventListener(t,n)},x.Event=function(e,t){
// Allow instantiation without the 'new' keyword
if(!(this instanceof x.Event))return new x.Event(e,t);
// Event object
e&&e.type?(this.originalEvent=e,this.type=e.type,
// Events bubbling up the document may have been marked as prevented
// by a handler lower down the tree; reflect the correct value.
this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&
// Support: Android <=2.3 only
!1===e.returnValue?a:D,
// Create target properties
// Support: Safari <=6 - 7 only
// Target should not be a text node (#504, #13143)
this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,
// Put explicitly provided properties onto the event object
t&&x.extend(this,t),
// Create a timestamp if incoming event doesn't have one
this.timeStamp=e&&e.timeStamp||Date.now(),
// Mark it as fixed
this[x.expando]=!0},
// jQuery.Event is based on DOM3 Events as specified by the ECMAScript Language Binding
// https://www.w3.org/TR/2003/WD-DOM-Level-3-Events-20030331/ecma-script-binding.html
x.Event.prototype={constructor:x.Event,isDefaultPrevented:D,isPropagationStopped:D,isImmediatePropagationStopped:D,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=a,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=a,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=a,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},
// Includes all common event props including KeyEvent and MouseEvent specific props
x.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},x.event.addProp),x.each({focus:"focusin",blur:"focusout"},function(e,t){x.event.special[e]={
// Utilize native event if possible so blur/focus sequence is correct
setup:function(){
// Return false to allow normal processing in the caller
// Claim the first handler
// dataPriv.set( this, "focus", ... )
// dataPriv.set( this, "blur", ... )
return ke(this,e,Ne),!1},trigger:function(){
// Return non-false to allow normal event-path propagation
// Force setup before trigger
return ke(this,e),!0},
// Suppress native focus or blur as it's already being fired
// in leverageNative.
_default:function(){return!0},delegateType:t}}),
// Create mouseenter/leave events using mouseover/out and event-time checks
// so that event delegation works in jQuery.
// Do the same for pointerenter/pointerleave and pointerover/pointerout
//
// Support: Safari 7 only
// Safari sends mouseenter too often; see:
// https://bugs.chromium.org/p/chromium/issues/detail?id=470258
// for the description of the bug (it existed in older Chrome versions as well).
x.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){x.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;
// For mouseenter/leave call the handler if related is outside the target.
// NB: No relatedTarget if the mouse left/entered the browser window
return n&&(n===this||x.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),x.fn.extend({on:function(e,t,n,r){return Se(this,e,t,n,r)},one:function(e,t,n,r){return Se(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)
// ( event )  dispatched jQuery.Event
r=e.handleObj,x(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(
// ( types [, fn] )
n=t,t=void 0),!1===n&&(n=D),this.each(function(){x.event.remove(this,e,n,t)});
// ( types-object [, selector] )
for(i in e)this.off(i,t,e[i])}return this}});var
// Support: IE <=10 - 11, Edge 12 - 13 only
// In IE/Edge using regex groups here causes severe slowdowns.
// See https://connect.microsoft.com/IE/feedback/details/1736512/
De=/<script|<style|<link/i,
// checked="checked" or checked
Le=/checked\s*(?:[^=]|=\s*.checked.)/i,je=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;
// Prefer a tbody over its parent table for containing new rows
function qe(e,t){return u(e,"table")&&u(11!==t.nodeType?t:t.firstChild,"tr")&&x(e).children("tbody")[0]||e}
// Replace/restore the type attribute of script elements for safe DOM manipulation
function Oe(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Pe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function He(e,t){var n,r,i,o;if(1===t.nodeType){
// 1. Copy private data: events, handlers, etc.
if(T.hasData(e)&&(o=T.get(e).events))for(i in T.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)x.event.add(t,i,o[i][n]);
// 2. Copy user data
l.hasData(e)&&(e=l.access(e),e=x.extend({},e),l.set(t,e))}}
// Fix IE bugs, see support tests
function L(n,r,i,o){
// Flatten any nested arrays
r=B(r);var e,t,a,s,u,l,c=0,f=n.length,d=f-1,p=r[0],h=v(p);
// We can't cloneNode fragments that contain checked, in WebKit
if(h||1<f&&"string"==typeof p&&!y.checkClone&&Le.test(p))return n.each(function(e){var t=n.eq(e);h&&(r[0]=p.call(this,e,t.html())),L(t,r,i,o)});if(f&&(t=(e=Ee(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){
// Use the original fragment for the last item
// instead of the first because it can end up
// being emptied incorrectly in certain situations (#8070).
for(s=(a=x.map(k(e,"script"),Oe)).length;c<f;c++)u=e,c!==d&&(u=x.clone(u,!0,!0),s)&&
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
x.merge(a,k(u,"script")),i.call(n[c],u,c);if(s)
// Evaluate executable scripts on first document insertion
for(l=a[a.length-1].ownerDocument,
// Reenable scripts
x.map(a,Pe),c=0;c<s;c++)u=a[c],we.test(u.type||"")&&!T.access(u,"globalEval")&&x.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?
// Optional AJAX dependency, but won't run scripts if not present
x._evalUrl&&!u.noModule&&x._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):X(u.textContent.replace(je,""),u,l))}return n}function Ie(e,t,n){for(var r,i=t?x.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||x.cleanData(k(r)),r.parentNode&&(n&&A(r)&&Ce(k(r,"script")),r.parentNode.removeChild(r));return e}x.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=A(e);
// Fix IE cloning issues
if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||x.isXMLDoc(e)))for(
// We eschew Sizzle here for performance reasons: https://jsperf.com/getall-vs-sizzle/2
a=k(c),r=0,i=(o=k(e)).length;r<i;r++)s=o[r],u=a[r],l=void 0,
// Fails to persist the checked state of a cloned checkbox or radio button.
"input"===(l=u.nodeName.toLowerCase())&&N.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);
// Copy the events from the original to the clone
if(t)if(n)for(o=o||k(e),a=a||k(c),r=0,i=o.length;r<i;r++)He(o[r],a[r]);else He(e,c);
// Preserve script evaluation history
// Return the cloned set
return 0<(a=k(c,"script")).length&&Ce(a,!f&&k(e,"script")),c},cleanData:function(e){for(var t,n,r,i=x.event.special,o=0;void 0!==(n=e[o]);o++)if(C(n)){if(t=n[T.expando]){if(t.events)for(r in t.events)i[r]?x.event.remove(n,r):x.removeEvent(n,r,t.handle);
// Support: Chrome <=35 - 45+
// Assign undefined instead of using delete, see Data#remove
n[T.expando]=void 0}n[l.expando]&&(
// Support: Chrome <=35 - 45+
// Assign undefined instead of using delete, see Data#remove
n[l.expando]=void 0)}}}),x.fn.extend({detach:function(e){return Ie(this,e,!0)},remove:function(e){return Ie(this,e)},text:function(e){return f(this,function(e){return void 0===e?x.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return L(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||qe(this,e).appendChild(e)})},prepend:function(){return L(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=qe(this,e)).insertBefore(e,t.firstChild)})},before:function(){return L(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return L(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(
// Prevent memory leaks
x.cleanData(k(e,!1)),
// Remove any remaining nodes
e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return x.clone(this,e,t)})},html:function(e){return f(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;
// See if we can take a shortcut and just use innerHTML
if("string"==typeof e&&!De.test(e)&&!S[(xe.exec(e)||["",""])[1].toLowerCase()]){e=x.htmlPrefilter(e);try{for(;n<r;n++)
// Remove element nodes and prevent memory leaks
1===(t=this[n]||{}).nodeType&&(x.cleanData(k(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];
// Make the changes, replacing each non-ignored context element with the new content
return L(this,arguments,function(e){var t=this.parentNode;x.inArray(this,n)<0&&(x.cleanData(k(this)),t)&&t.replaceChild(e,this);
// Force callback invocation
},n)}}),x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){x.fn[e]=function(e){for(var t,n=[],r=x(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),x(r[o])[a](t),
// Support: Android <=4.0 only, PhantomJS 1 only
// .get() because push.apply(_, arraylike) throws on ancient WebKit
M.apply(n,t.get());return this.pushStack(n)}});function Re(e){
// Support: IE <=11 only, Firefox <=30 (#15098, #14150)
// IE throws on elements created in popups
// FF meanwhile throws on frame elements through "defaultView.getComputedStyle"
var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:g).getComputedStyle(e)}function Be(e,t,n){var r,i={};
// Remember the old values, and insert the new ones
for(r in t)i[r]=e.style[r],e.style[r]=t[r];
// Revert the old values
for(r in n=n.call(e),t)e.style[r]=i[r];return n}var Me,We,Fe,$e,ze,_e,Ue,i,Ve=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),Xe=new RegExp(p.join("|"),"i");
// Executing both pixelPosition & boxSizingReliable tests require only one layout
// so they're executed at the same time to save the second computation.
function o(){
// This is a singleton, we need to execute it only once
var e;i&&(Ue.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",i.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",E.appendChild(Ue).appendChild(i),e=g.getComputedStyle(i),Me="1%"!==e.top,
// Support: Android 4.0 - 4.3 only, Firefox <=3 - 44
_e=12===Qe(e.marginLeft),
// Support: Android 4.0 - 4.3 only, Safari <=9.1 - 10.1, iOS <=7.0 - 9.3
// Some styles come back with percentage values, even though they shouldn't
i.style.right="60%",$e=36===Qe(e.right),
// Support: IE 9 - 11 only
// Detect misreporting of content dimensions for box-sizing:border-box elements
We=36===Qe(e.width),
// Support: IE 9 only
// Detect overflow:scroll screwiness (gh-3699)
// Support: Chrome <=64
// Don't get tricked when zoom affects offsetWidth (gh-4029)
i.style.position="absolute",Fe=12===Qe(i.offsetWidth/3),E.removeChild(Ue),
// Nullify the div so it wouldn't be stored in the memory and
// it will also be a sign that checks already performed
i=null)}function Qe(e){return Math.round(parseFloat(e))}function j(e,t,n){var r,i,
// Support: Firefox 51+
// Retrieving style before computed somehow
// fixes an issue with getting wrong values
// on detached elements
o=e.style;
// getPropertyValue is needed for:
//   .css('filter') (IE 9 only, #12537)
//   .css('--customProperty) (#3144)
return(n=n||Re(e))&&(""!==(i=n.getPropertyValue(t)||n[t])||A(e)||(i=x.style(e,t)),!y.pixelBoxStyles())&&Ve.test(i)&&Xe.test(t)&&(
// Remember the original values
e=o.width,t=o.minWidth,r=o.maxWidth,
// Put in the new values to get a computed value out
o.minWidth=o.maxWidth=o.width=i,i=n.width,
// Revert the changed values
o.width=e,o.minWidth=t,o.maxWidth=r),void 0!==i?
// Support: IE <=9 - 11 only
// IE returns zIndex value as an integer.
i+"":i}function Ye(e,t){
// Define the hook, we'll check on the first run if it's really needed.
return{get:function(){if(!e())
// Hook needed; redefine it so that the support test is not executed again.
return(this.get=t).apply(this,arguments);
// Hook not needed (or it's not possible to use it due
// to missing dependency), remove it.
delete this.get}}}Ue=b.createElement("div"),
// Finish early in limited (non-browser) environments
(i=b.createElement("div")).style&&(
// Support: IE <=9 - 11 only
// Style of cloned element affects source element cloned (#8908)
i.style.backgroundClip="content-box",i.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===i.style.backgroundClip,x.extend(y,{boxSizingReliable:function(){return o(),We},pixelBoxStyles:function(){return o(),$e},pixelPosition:function(){return o(),Me},reliableMarginLeft:function(){return o(),_e},scrollboxSize:function(){return o(),Fe},
// Support: IE 9 - 11+, Edge 15 - 18+
// IE/Edge misreport `getComputedStyle` of table rows with width/height
// set in CSS while `offset*` properties report correct values.
// Behavior in IE 9 is more subtle than in newer versions & it passes
// some versions of this test; make sure not to make it pass there!
//
// Support: Firefox 70+
// Only Firefox includes border widths
// in computed dimensions. (gh-4529)
reliableTrDimensions:function(){var e,t,n;return null==ze&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",
// Support: Chrome 86+
// Height set through cssText does not get applied.
// Computed height then comes back as 0.
t.style.height="1px",n.style.height="9px",
// Support: Android 8 Chrome 86+
// In our bodyBackground.html iframe,
// display for all div elements is set to "inline",
// which causes a problem only in Android 8 Chrome 86.
// Ensuring the div is display: block
// gets around this issue.
n.style.display="block",E.appendChild(e).appendChild(t).appendChild(n),n=g.getComputedStyle(t),ze=parseInt(n.height,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10)===t.offsetHeight,E.removeChild(e)),ze}}));var Ge=["Webkit","Moz","ms"],Ke=b.createElement("div").style,Je={};
// Return a vendor-prefixed property or undefined
// Return a potentially-mapped jQuery.cssProps or vendor prefixed property
function Ze(e){var t=x.cssProps[e]||Je[e];return t||(e in Ke?e:Je[e]=function(e){for(
// Check for vendor prefixed names
var t=e[0].toUpperCase()+e.slice(1),n=Ge.length;n--;)if((e=Ge[n]+t)in Ke)return e}(e)||e)}var q,O,
// Swappable if display is none or starts with table
// except "table", "table-cell", or "table-caption"
// See here for display values: https://developer.mozilla.org/en-US/docs/CSS/display
et=/^(none|table(?!-c[ea]).+)/,tt=/^--/,nt={position:"absolute",visibility:"hidden",display:"block"},rt={letterSpacing:"0",fontWeight:"400"};function it(e,t,n){
// Any relative (+/-) values have already been
// normalized at this point
var r=ve.exec(t);return r?
// Guard against undefined "subtract", e.g., when used as in cssHooks
Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ot(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0;
// Adjustment may not be necessary
if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)
// Both box models exclude margin
"margin"===n&&(u+=x.css(e,n+p[a],!0,i)),
// If we get here with a content-box, we're seeking "padding" or "border" or "margin"
r?(
// For "content", subtract padding
"content"===n&&(u-=x.css(e,"padding"+p[a],!0,i)),
// For "content" or "padding", subtract border
"margin"!==n&&(u-=x.css(e,"border"+p[a]+"Width",!0,i))):(
// Add padding
u+=x.css(e,"padding"+p[a],!0,i),
// For "border" or "margin", add border
"padding"!==n?u+=x.css(e,"border"+p[a]+"Width",!0,i):s+=x.css(e,"border"+p[a]+"Width",!0,i));
// Account for positive content-box scroll gutter when requested by providing computedVal
return!r&&0<=o&&(
// offsetWidth/offsetHeight is a rounded sum of content, padding, scroll gutter, and border
// Assuming integer scroll gutter, subtract the rest and round down
u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u}function at(e,t,n){
// Start with computed style
var r=Re(e),i=(!y.boxSizingReliable()||n)&&"border-box"===x.css(e,"boxSizing",!1,r),o=i,a=j(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);
// Support: Firefox <=54
// Return a confounding non-pixel value or feign ignorance, as appropriate.
if(Ve.test(a)){if(!n)return a;a="auto"}
// Support: IE 9 - 11 only
// Use offsetWidth/offsetHeight for when box sizing is unreliable.
// In those cases, the computed value can be trusted to be border-box.
// Adjust for the element's box model
return(!y.boxSizingReliable()&&i||
// Support: IE 10 - 11+, Edge 15 - 18+
// IE/Edge misreport `getComputedStyle` of table rows with width/height
// set in CSS while `offset*` properties report correct values.
// Interestingly, in some cases IE 9 doesn't suffer from this issue.
!y.reliableTrDimensions()&&u(e,"tr")||
// Fall back to offsetWidth/offsetHeight when value is "auto"
// This happens for inline elements with no explicit setting (gh-3571)
"auto"===a||
// Support: Android <=4.1 - 4.3 only
// Also use offsetWidth/offsetHeight for misreported inline dimensions (gh-3602)
!parseFloat(a)&&"inline"===x.css(e,"display",!1,r))&&
// Make sure the element is visible & connected
e.getClientRects().length&&(i="border-box"===x.css(e,"boxSizing",!1,r),
// Where available, offsetWidth/offsetHeight approximate border box dimensions.
// Where not available (e.g., SVG), assume unreliable box-sizing and interpret the
// retrieved value as a content box dimension.
o=s in e)&&(a=e[s]),(
// Normalize "" and auto
a=parseFloat(a)||0)+ot(e,t,n||(i?"border":"content"),o,r,
// Provide the current computed size to request scroll gutter calculation (gh-3589)
a)+"px"}x.extend({
// Add in style property hooks for overriding the default
// behavior of getting and setting a style property
cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=j(e,"opacity"))?"1":t}}},
// Don't automatically add "px" to these possibly-unitless properties
cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},
// Add in properties whose names you wish to fix before
// setting or getting the value
cssProps:{},
// Get and set the style property on a DOM Node
style:function(e,t,n,r){
// Don't set styles on text and comment nodes
if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){
// Make sure that we're working with the right name
var i,o,a,s=d(t),u=tt.test(t),l=e.style;
// Make sure that we're working with the right name. We don't
// want to query the value if it is a CSS custom property
// since they are user-defined.
// Check if we're setting a value
if(u||(t=Ze(s)),
// Gets hook for the prefixed version, then unprefixed version
a=x.cssHooks[t]||x.cssHooks[s],void 0===n)
// If a hook was provided get the non-computed value from there
return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];
// Otherwise just get the value from the style object
// Convert "+=" or "-=" to relative numbers (#7345)
"string"===(o=typeof n)&&(i=ve.exec(n))&&i[1]&&(n=function(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return x.css(e,t,"")},u=s(),l=n&&n[3]||(x.cssNumber[t]?"":"px"),
// Starting value computation is required for potential unit mismatches
c=e.nodeType&&(x.cssNumber[t]||"px"!==l&&+u)&&ve.exec(x.css(e,t));if(c&&c[3]!==l){for(
// Support: Firefox <=54
// Halve the iteration target value to prevent interference from CSS upper bounds (gh-2144)
// Trust units reported by jQuery.css
l=l||c[3],
// Iteratively approximate from a nonzero starting point
c=+(u/=2)||1;a--;)
// Evaluate and update our best guess (doubling guesses that zero out).
// Finish if the scale equals or crosses 1 (making the old*new product non-positive).
x.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;x.style(e,t,(c*=2)+l),
// Make sure we update the tween properties later on
n=n||[]}return n&&(c=+c||+u||0,
// Apply relative offset (+=/-=) if specified
i=n[1]?c+(n[1]+1)*n[2]:+n[2],r)&&(r.unit=l,r.start=c,r.end=i),i}(e,t,i),
// Fixes bug #9237
o="number"),
// Make sure that null and NaN values aren't set (#7116)
null==n||n!=n||(
// If a number was passed in, add the unit (except for certain CSS properties)
// The isCustomProp check can be removed in jQuery 4.0 when we only auto-append
// "px" to a few hardcoded values.
"number"!==o||u||(n+=i&&i[3]||(x.cssNumber[s]?"":"px")),
// background-* props affect original clone's values
y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r)))||(u?l.setProperty(t,n):l[t]=n)}},css:function(e,t,n,r){var i,o=d(t);
// Make sure that we're working with the right name. We don't
// want to modify the value if it is a CSS custom property
// since they are user-defined.
// Make numeric if forced or a qualifier was provided and val looks numeric
return tt.test(t)||(t=Ze(o)),
// Convert "normal" to computed value
"normal"===(
// Otherwise, if a way to get the computed value exists, use that
i=void 0===(
// If a hook was provided get the computed value from there
i=(
// Try prefixed name followed by the unprefixed name
o=x.cssHooks[t]||x.cssHooks[o])&&"get"in o?o.get(e,!0,n):i)?j(e,t,r):i)&&t in rt&&(i=rt[t]),(""===n||n)&&(o=parseFloat(i),!0===n||isFinite(o))?o||0:i}}),x.each(["height","width"],function(e,a){x.cssHooks[a]={get:function(e,t,n){if(t)
// Certain elements can have dimension info if we invisibly show them
// but it must have a current display style that would benefit
return!et.test(x.css(e,"display"))||
// Support: Safari 8+
// Table columns in Safari have non-zero offsetWidth & zero
// getBoundingClientRect().width unless display is changed.
// Support: IE <=11 only
// Running getBoundingClientRect on a disconnected node
// in IE throws an error.
e.getClientRects().length&&e.getBoundingClientRect().width?at(e,a,n):Be(e,nt,function(){return at(e,a,n)})},set:function(e,t,n){var r=Re(e),
// Only read styles.position if the test has a chance to fail
// to avoid forcing a reflow.
i=!y.scrollboxSize()&&"absolute"===r.position,o=(i||n)&&"border-box"===x.css(e,"boxSizing",!1,r),n=n?ot(e,a,n,o,r):0;
// Account for unreliable border-box dimensions by comparing offset* to computed and
// faking a content-box to get border and padding (gh-3699)
return o&&i&&(n-=Math.ceil(e["offset"+a[0].toUpperCase()+a.slice(1)]-parseFloat(r[a])-ot(e,a,"border",!1,r)-.5)),
// Convert to pixels if value adjustment is needed
n&&(o=ve.exec(t))&&"px"!==(o[3]||"px")&&(e.style[a]=t,t=x.css(e,a)),it(0,t,n)}}}),x.cssHooks.marginLeft=Ye(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(j(e,"marginLeft"))||e.getBoundingClientRect().left-Be(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),
// These hooks are used by animate to expand properties
x.each({margin:"",padding:"",border:"Width"},function(i,o){x.cssHooks[i+o]={expand:function(e){for(var t=0,n={},
// Assumes a single number if not a string
r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+p[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(x.cssHooks[i+o].set=it)}),x.fn.extend({css:function(e,t){return f(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Re(e),i=t.length;a<i;a++)o[t[a]]=x.css(e,t[a],!1,r);return o}return void 0!==n?x.style(e,t,n):x.css(e,t)},e,t,1<arguments.length)}}),
// Based off of the plugin by Clint Helfers, with permission.
// https://web.archive.org/web/20100324014747/http://blindsignals.com/index.php/2009/07/jquery-delay/
x.fn.delay=function(r,e){return r=x.fx&&x.fx.speeds[r]||r,this.queue(e=e||"fx",function(e,t){var n=g.setTimeout(e,r);t.stop=function(){g.clearTimeout(n)}})},q=b.createElement("input"),O=b.createElement("select").appendChild(b.createElement("option")),q.type="checkbox",
// Support: Android <=4.3 only
// Default value for a checkbox should be "on"
y.checkOn=""!==q.value,
// Support: IE <=11 only
// Must access selectedIndex to make default options select
y.optSelected=O.selected,(
// Support: IE <=11 only
// An input loses its value after becoming a radio
q=b.createElement("input")).value="t",q.type="radio",y.radioValue="t"===q.value;var st,ut=x.expr.attrHandle,lt=(x.fn.extend({attr:function(e,t){return f(this,x.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){x.removeAttr(this,e)})}}),x.extend({attr:function(e,t,n){var r,i,o=e.nodeType;
// Don't get/set attributes on text, comment and attribute nodes
if(3!==o&&8!==o&&2!==o)
// Fallback to prop when attributes are not supported
return void 0===e.getAttribute?x.prop(e,t,n):(
// Attribute hooks are determined by the lowercase version
// Grab necessary hook if one is defined
1===o&&x.isXMLDoc(e)||(i=x.attrHooks[t.toLowerCase()]||(x.expr.match.bool.test(t)?st:void 0)),void 0!==n?null===n?void x.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=x.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){var n;if(!y.radioValue&&"radio"===t&&u(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,r=0,
// Attribute names can contain non-HTML whitespace characters
// https://html.spec.whatwg.org/multipage/syntax.html#attributes-2
i=t&&t.match(w);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),
// Hooks for boolean attributes
st={set:function(e,t,n){return!1===t?
// Remove boolean attributes when set to false
x.removeAttr(e,n):e.setAttribute(n,n),n}},x.each(x.expr.match.bool.source.match(/\w+/g),function(e,t){var a=ut[t]||x.find.attr;ut[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(
// Avoid an infinite loop by temporarily removing this function from the getter
i=ut[o],ut[o]=r,r=null!=a(e,t,n)?o:null,ut[o]=i),r}}),/^(?:input|select|textarea|button)$/i),ct=/^(?:a|area)$/i;
// Strip and collapse whitespace according to HTML spec
// https://infra.spec.whatwg.org/#strip-and-collapse-ascii-whitespace
function P(e){return(e.match(w)||[]).join(" ")}function H(e){return e.getAttribute&&e.getAttribute("class")||""}function ft(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(w)||[]}x.fn.extend({prop:function(e,t){return f(this,x.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[x.propFix[e]||e]})}}),x.extend({prop:function(e,t,n){var r,i,o=e.nodeType;
// Don't get/set properties on text, comment and attribute nodes
if(3!==o&&8!==o&&2!==o)return 1===o&&x.isXMLDoc(e)||(
// Fix name and attach hooks
t=x.propFix[t]||t,i=x.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){
// Support: IE <=9 - 11 only
// elem.tabIndex doesn't always return the
// correct value when it hasn't been explicitly set
// https://web.archive.org/web/20141116233347/http://fluidproject.org/blog/2008/01/09/getting-setting-and-removing-tabindex-values-with-javascript/
// Use proper attribute retrieval(#12072)
var t=x.find.attr(e,"tabindex");return t?parseInt(t,10):lt.test(e.nodeName)||ct.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),
// Support: IE <=11 only
// Accessing the selectedIndex property
// forces the browser to respect setting selected
// on the option
// The getter ensures a default option is selected
// when in an optgroup
// eslint rule "no-unused-expressions" is disabled for this code
// since it considers such accessions noop
y.optSelected||(x.propHooks.selected={get:function(e){
/* eslint no-unused-expressions: "off" */
e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){
/* eslint no-unused-expressions: "off" */
e=e.parentNode;e&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){x.propFix[this.toLowerCase()]=this}),x.fn.extend({addClass:function(t){var e,n,r,i,o,a,s=0;if(v(t))return this.each(function(e){x(this).addClass(t.call(this,e,H(this)))});if((e=ft(t)).length)for(;n=this[s++];)if(a=H(n),r=1===n.nodeType&&" "+P(a)+" "){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");
// Only assign if different to avoid unneeded rendering.
a!==(a=P(r))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,r,i,o,a,s=0;if(v(t))return this.each(function(e){x(this).removeClass(t.call(this,e,H(this)))});if(!arguments.length)return this.attr("class","");if((e=ft(t)).length)for(;n=this[s++];)if(a=H(n),
// This expression is here for better compressibility (see addClass)
r=1===n.nodeType&&" "+P(a)+" "){for(o=0;i=e[o++];)
// Remove *all* instances
for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");
// Only assign if different to avoid unneeded rendering.
a!==(a=P(r))&&n.setAttribute("class",a)}return this},toggleClass:function(i,t){var o=typeof i,a="string"==o||Array.isArray(i);return"boolean"==typeof t&&a?t?this.addClass(i):this.removeClass(i):v(i)?this.each(function(e){x(this).toggleClass(i.call(this,e,H(this),t),t)}):this.each(function(){var e,t,n,r;if(a)for(
// Toggle individual class names
t=0,n=x(this),r=ft(i);e=r[t++];)
// Check each className given, space separated list
n.hasClass(e)?n.removeClass(e):n.addClass(e);
// Toggle whole class name
else void 0!==i&&"boolean"!=o||((e=H(this))&&
// Store className if set
T.set(this,"__className__",e),
// If the element has a class name or if we're passed `false`,
// then remove the whole classname (if there was one, the above saved it).
// Otherwise bring back whatever was previously saved (if anything),
// falling back to the empty string if nothing was stored.
this.setAttribute&&this.setAttribute("class",!e&&!1!==i&&T.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+P(H(t))+" ").indexOf(r))return!0;return!1}});function dt(e){e.stopPropagation()}var pt=/\r/g,ht=(x.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=v(t),this.each(function(e){1!==this.nodeType||(
// Treat null/undefined as ""; convert numbers to string
null==(e=r?t.call(this,e,x(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=x.map(e,function(e){return null==e?"":e+""})),(n=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value"))||(this.value=e)})):i?(n=x.valHooks[i.type]||x.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:
// Handle most common string cases
"string"==typeof(e=i.value)?e.replace(pt,""):null==e?"":e:void 0}}),x.extend({valHooks:{option:{get:function(e){var t=x.find.attr(e,"value");return null!=t?t:
// Support: IE <=10 - 11 only
// option.text throws exceptions (#14686, #14858)
// Strip and collapse whitespace
// https://html.spec.whatwg.org/#strip-and-collapse-whitespace
P(x.text(e))}},select:{get:function(e){
// Loop through all the selected options
for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type,o=i?null:[],a=i?r+1:n.length,s=r<0?a:i?r:0;s<a;s++)
// Support: IE <=9 only
// IE8-9 doesn't update selected after form reset (#2551)
if(((t=n[s]).selected||s===r)&&
// Don't return options that are disabled or in a disabled optgroup
!t.disabled&&(!t.parentNode.disabled||!u(t.parentNode,"optgroup"))){
// We don't need an array for one selects
if(
// Get the specific value for the option
t=x(t).val(),i)return t;
// Multi-Selects return an array
o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=x.makeArray(t),a=i.length;a--;)
/* eslint-disable no-cond-assign */
((r=i[a]).selected=-1<x.inArray(x.valHooks.option.get(r),o))&&(n=!0)
/* eslint-enable no-cond-assign */;
// Force browsers to behave consistently when non-matching value is set
return n||(e.selectedIndex=-1),o}}}}),
// Radios and checkboxes getter/setter
x.each(["radio","checkbox"],function(){x.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<x.inArray(x(e).val(),t)}},y.checkOn||(x.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),
// Return jQuery for attributes-only inclusion
y.focusin="onfocusin"in g,/^(?:focusinfocus|focusoutblur)$/),gt=(x.extend(x.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f=[n||b],d=z.call(e,"type")?e.type:e,p=z.call(e,"namespace")?e.namespace.split("."):[],h=c=o=n=n||b;
// Don't do events on text and comment nodes
if(3!==n.nodeType&&8!==n.nodeType&&!ht.test(d+x.event.triggered)&&(-1<d.indexOf(".")&&(
// Namespaced trigger; create a regexp to match event type in handle()
d=(p=d.split(".")).shift(),p.sort()),s=d.indexOf(":")<0&&"on"+d,
// Trigger bitmask: & 1 for native handlers; & 2 for jQuery (always true)
(
// Caller can pass in a jQuery.Event object, Object, or just an event type string
e=e[x.expando]?e:new x.Event(d,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,
// Clean up the event in case it is being reused
e.result=void 0,e.target||(e.target=n),
// Clone any incoming data and prepend the event, creating the handler arg list
t=null==t?[e]:x.makeArray(t,[e]),
// Allow special events to draw outside the lines
l=x.event.special[d]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){
// Determine event propagation path in advance, per W3C events spec (#9951)
// Bubble up to document, then to window; watch for a global ownerDocument var (#9724)
if(!r&&!l.noBubble&&!m(n)){for(a=l.delegateType||d,ht.test(a+d)||(h=h.parentNode);h;h=h.parentNode)f.push(h),o=h;
// Only add window if we got to document (e.g., not plain obj or detached DOM)
o===(n.ownerDocument||b)&&f.push(o.defaultView||o.parentWindow||g)}
// Fire handlers on the event path
for(i=0;(h=f[i++])&&!e.isPropagationStopped();)c=h,e.type=1<i?a:l.bindType||d,(
// jQuery handler
u=(T.get(h,"events")||Object.create(null))[e.type]&&T.get(h,"handle"))&&u.apply(h,t),(
// Native handler
u=s&&h[s])&&u.apply&&C(h)&&(e.result=u.apply(h,t),!1===e.result)&&e.preventDefault();return e.type=d,
// If nobody prevented the default action, do it now
r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(f.pop(),t)||!C(n)||
// Call a native DOM method on the target with the same name as the event.
// Don't do default actions on window, that's where global variables be (#6170)
s&&v(n[d])&&!m(n)&&(
// Don't re-trigger an onFOO event when we call its FOO() method
(o=n[s])&&(n[s]=null),
// Prevent re-triggering of the same event, since we already bubbled it above
x.event.triggered=d,e.isPropagationStopped()&&c.addEventListener(d,dt),n[d](),e.isPropagationStopped()&&c.removeEventListener(d,dt),x.event.triggered=void 0,o)&&(n[s]=o),e.result}
// focus/blur morphs to focusin/out; ensure we're not firing them right now
},
// Piggyback on a donor event to simulate a different one
// Used only for `focus(in | out)` events
simulate:function(e,t,n){n=x.extend(new x.Event,n,{type:e,isSimulated:!0});x.event.trigger(n,null,t)}}),x.fn.extend({trigger:function(e,t){return this.each(function(){x.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return x.event.trigger(e,t,n,!0)}}),
// Support: Firefox <=44
// Firefox doesn't have focus(in | out) events
// Related ticket - https://bugzilla.mozilla.org/show_bug.cgi?id=687787
//
// Support: Chrome <=48 - 49, Safari <=9.0 - 9.1
// focus(in | out) events fire after focus & blur events,
// which is spec violation - http://www.w3.org/TR/DOM-Level-3-Events/#events-focusevent-event-order
// Related ticket - https://bugs.chromium.org/p/chromium/issues/detail?id=449857
y.focusin||x.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){x.event.simulate(r,e.target,x.event.fix(e))}x.event.special[r]={setup:function(){
// Handle: regular nodes (via `this.ownerDocument`), window
// (via `this.document`) & document (via `this`).
var e=this.ownerDocument||this.document||this,t=T.access(e,r);t||e.addEventListener(n,i,!0),T.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=T.access(e,r)-1;t?T.access(e,r,t):(e.removeEventListener(n,i,!0),T.remove(e,r))}}}),
// Cross-browser xml parsing
x.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;
// Support: IE 9 - 11 only
// IE throws on parseFromString with invalid input.
try{t=(new g.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||x.error("Invalid XML: "+(n?x.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t},/\[\]$/),vt=/\r?\n/g,mt=/^(?:submit|button|image|reset|file)$/i,yt=/^(?:input|select|textarea|keygen)/i;
// Serialize an array of form elements or a set of
// key/values into a query string
x.param=function(e,t){function n(e,t){t=v(t)?t():t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,i=[];if(null==e)return"";
// If an array was passed in, assume that it is an array of form elements.
if(Array.isArray(e)||e.jquery&&!x.isPlainObject(e))
// Serialize the form elements
x.each(e,function(){n(this.name,this.value)});else
// If traditional, encode the "old" way (the way 1.3.2 or older
// did it), otherwise encode params recursively.
for(r in e)!function n(r,e,i,o){if(Array.isArray(e))
// Serialize array item.
x.each(e,function(e,t){i||gt.test(r)?
// Treat each array item as a scalar.
o(r,t):
// Item is non-scalar (array or object), encode its numeric index.
n(r+"["+("object"==typeof t&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==h(e))
// Serialize scalar item.
o(r,e);else
// Serialize object item.
for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);
// Return the resulting serialization
return i.join("&")},x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map(function(){
// Can add propHook for "elements" to filter or add form elements
var e=x.prop(this,"elements");return e?x.makeArray(e):this}).filter(function(){var e=this.type;
// Use .is( ":disabled" ) so that fieldset[disabled] works
return this.name&&!x(this).is(":disabled")&&yt.test(this.nodeName)&&!mt.test(e)&&(this.checked||!N.test(e))}).map(function(e,t){var n=x(this).val();return null==n?null:Array.isArray(n)?x.map(n,function(e){return{name:t.name,value:e.replace(vt,"\r\n")}}):{name:t.name,value:n.replace(vt,"\r\n")}}).get()}}),x.fn.extend({wrapAll:function(e){return this[0]&&(v(e)&&(e=e.call(this[0])),
// The elements to wrap the target around
e=x(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return v(n)?this.each(function(e){x(this).wrapInner(n.call(this,e))}):this.each(function(){var e=x(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=v(t);return this.each(function(e){x(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){x(this).replaceWith(this.childNodes)}),this}}),x.expr.pseudos.hidden=function(e){return!x.expr.pseudos.visible(e)},x.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},
// Support: Safari 8 only
// In Safari 8 documents created via document.implementation.createHTMLDocument
// collapse sibling forms: the second one becomes a child of the first one.
// Because of that, this security measure has to be disabled in Safari 8.
// https://bugs.webkit.org/show_bug.cgi?id=137337
y.createHTMLDocument=((e=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),
// Argument "data" should be string of html
// context (optional): If specified, the fragment will be created in this context,
// defaults to document
// keepScripts (optional): If true, will include scripts passed in the html string
x.parseHTML=function(e,t,n){var r;return"string"!=typeof e?[]:(
// Single tag
"boolean"==typeof t&&(n=t,t=!1),t||(
// Stop scripts or inline event handlers from being executed immediately
// by using document.implementation
y.createHTMLDocument?((
// Set the base href for the created document
// so any parsed elements with URLs
// are based on the document's URL (gh-2965)
r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),r=!n&&[],(n=K.exec(e))?[t.createElement(n[1])]:(n=Ee([e],t,r),r&&r.length&&x(r).remove(),x.merge([],n.childNodes)))},x.offset={setOffset:function(e,t,n){var r,i,o,a,s=x.css(e,"position"),u=x(e),l={};
// Set position first, in-case top/left are set even on static elem
"static"===s&&(e.style.position="relative"),o=u.offset(),r=x.css(e,"top"),a=x.css(e,"left"),
// Need to be able to calculate position if either
// top or left is auto and position is either absolute or fixed
s=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(i=(s=u.position()).top,s.left):(i=parseFloat(r)||0,parseFloat(a)||0),null!=(
// Use jQuery.extend here to allow modification of coordinates argument (gh-1848)
t=v(t)?t.call(e,n,x.extend({},o)):t).top&&(l.top=t.top-o.top+i),null!=t.left&&(l.left=t.left-o.left+s),"using"in t?t.using.call(e,l):u.css(l)}},x.fn.extend({
// offset() relates an element's border box to the document origin
offset:function(t){
// Preserve chaining for setter
var e,n;return arguments.length?void 0===t?this:this.each(function(e){x.offset.setOffset(this,t,e)}):(n=this[0])?
// Return zeros for disconnected and hidden (display: none) elements (gh-2310)
// Support: IE <=11 only
// Running getBoundingClientRect on a
// disconnected node in IE throws an error
n.getClientRects().length?(
// Get document-relative position by adding viewport scroll to viewport-relative gBCR
e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},
// position() relates an element's margin box to its offset parent's padding box
// This corresponds to the behavior of CSS absolute positioning
position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};
// position:fixed elements are offset from the viewport, which itself always has zero offset
if("fixed"===x.css(r,"position"))
// Assume position:fixed implies availability of getBoundingClientRect
t=r.getBoundingClientRect();else{for(t=this.offset(),
// Account for the *real* offset parent, which can be the document or its root element
// when a statically positioned element is identified
n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===x.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&(
// Incorporate borders into its offset, since they are outside its content origin
(i=x(e).offset()).top+=x.css(e,"borderTopWidth",!0),i.left+=x.css(e,"borderLeftWidth",!0))}
// Subtract parent offsets and element margins
return{top:t.top-i.top-x.css(r,"marginTop",!0),left:t.left-i.left-x.css(r,"marginLeft",!0)}}},
// This method will return documentElement in the following cases:
// 1) For the element inside the iframe without offsetParent, this method will return
//    documentElement of the parent window
// 2) For the hidden or detached element
// 3) For body or html element, i.e. in case of the html node - it will return itself
//
// but those exceptions were never presented as a real life use-cases
// and might be considered as more preferable results.
//
// This logic, however, is not guaranteed and can change at any point in the future
offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===x.css(e,"position");)e=e.offsetParent;return e||E})}}),
// Create scrollLeft and scrollTop methods
x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;x.fn[t]=function(e){return f(this,function(e,t,n){
// Coalesce documents and windows
var r;if(m(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),
// Support: Safari <=7 - 9.1, Chrome <=37 - 49
// Add the top/left cssHooks using jQuery.fn.position
// Webkit bug: https://bugs.webkit.org/show_bug.cgi?id=29084
// Blink bug: https://bugs.chromium.org/p/chromium/issues/detail?id=589347
// getComputedStyle returns percent when specified for top/left/bottom/right;
// rather than make the css module depend on the offset module, just check for it here
x.each(["top","left"],function(e,n){x.cssHooks[n]=Ye(y.pixelPosition,function(e,t){if(t)
// If curCSS returns percentage, fallback to offset
return t=j(e,n),Ve.test(t)?x(e).position()[n]+"px":t})}),
// Create innerHeight, innerWidth, height, width, outerHeight and outerWidth methods
x.each({Height:"height",Width:"width"},function(a,s){x.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){
// Margin is only for outerHeight, outerWidth
x.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return f(this,function(e,t,n){var r;return m(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:
// Get document width or height
9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?
// Get width or height on the element, requesting but not forcing parseFloat
x.css(e,t,i):
// Set width or height on the element
x.style(e,t,n,i)},s,n?e:void 0,n)}})}),x.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){
// ( namespace ) or ( selector, types [, fn] )
return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),x.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){
// Handle event binding
x.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});
// Support: Android <=4.0 only
// Make sure we trim BOM and NBSP
var bt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,
// Map over jQuery in case of overwrite
xt=(
// Bind a function to a context, optionally partially applying any
// arguments.
// jQuery.proxy is deprecated to promote standards (specifically Function#bind)
// However, it is not slated for removal any time soon
x.proxy=function(e,t){var n,r;
// Quick check to determine if target is callable, in the spec
// this throws a TypeError, but we will just return undefined.
if("string"==typeof t&&(r=e[t],t=e,e=r),v(e))
// Simulated bind
return n=s.call(arguments,2),
// Set the guid of unique handler to the same of original handler, so it can be removed
(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||x.guid++,r},x.holdReady=function(e){e?x.readyWait++:x.ready(!0)},x.isArray=Array.isArray,x.parseJSON=JSON.parse,x.nodeName=u,x.isFunction=v,x.isWindow=m,x.camelCase=d,x.type=h,x.now=Date.now,x.isNumeric=function(e){
// As of jQuery 3.0, isNumeric is limited to
// strings and numbers (primitives or objects)
// that can be coerced to finite numbers (gh-2662)
var t=x.type(e);return("number"===t||"string"===t)&&
// parseFloat NaNs numeric-cast false positives ("")
// ...but misinterprets leading-number strings, particularly hex literals ("0x...")
// subtraction forces infinities to NaN
!isNaN(e-parseFloat(e))},x.trim=function(e){return null==e?"":(e+"").replace(bt,"")},
// Register as a named AMD module, since jQuery can be concatenated with other
// files that may use define, but not via a proper concatenation script that
// understands anonymous AMD modules. A named AMD is safest and most robust
// way to register. Lowercase jquery is used because AMD module names are
// derived from file names, and jQuery is normally delivered in a lowercase
// file name. Do this after creating the global so that if an AMD module wants
// to call noConflict to hide this version of jQuery, it will work.
// Note that for maximum portability, libraries that are not jQuery should
// declare themselves as anonymous modules, and avoid setting a global if an
// AMD loader is present. jQuery is a special case. For more information, see
// https://github.com/jrburke/requirejs/wiki/Updating-existing-libraries#wiki-anon
"function"==typeof define&&define.amd&&define("jquery",[],function(){return x}),g.jQuery),
// Map over the $ in case of overwrite
wt=g.$;return x.noConflict=function(e){return g.$===x&&(g.$=wt),e&&g.jQuery===x&&(g.jQuery=xt),x},
// Expose jQuery and $ identifiers, even in AMD
// (#7102#comment:10, https://github.com/jquery/jquery/pull/557)
// and CommonJS for browser emulators (#13566)
void 0===I&&(g.jQuery=g.$=x),x});