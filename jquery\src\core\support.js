define(["../var/document","../var/support"],function(e,r){"use strict";
// Support: Safari 8 only
// In Safari 8 documents created via document.implementation.createHTMLDocument
// collapse sibling forms: the second one becomes a child of the first one.
// Because of that, this security measure has to be disabled in Safari 8.
// https://bugs.webkit.org/show_bug.cgi?id=137337
return r.createHTMLDocument=((e=e.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),r});