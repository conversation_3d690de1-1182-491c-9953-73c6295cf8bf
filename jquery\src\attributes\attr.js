define(["../core","../core/access","../core/nodeName","./support","../var/rnothtmlwhite","../selector"],function(a,r,o,n,i){"use strict";var u,c=a.expr.attrHandle;a.fn.extend({attr:function(t,e){return r(this,a.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){a.removeAttr(this,t)})}}),a.extend({attr:function(t,e,r){var o,n,i=t.nodeType;
// Don't get/set attributes on text, comment and attribute nodes
if(3!==i&&8!==i&&2!==i)
// Fallback to prop when attributes are not supported
return void 0===t.getAttribute?a.prop(t,e,r):(
// Attribute hooks are determined by the lowercase version
// Grab necessary hook if one is defined
1===i&&a.isXMLDoc(t)||(n=a.attrHooks[e.toLowerCase()]||(a.expr.match.bool.test(e)?u:void 0)),void 0!==r?null===r?void a.removeAttr(t,e):n&&"set"in n&&void 0!==(o=n.set(t,r,e))?o:(t.setAttribute(e,r+""),r):!(n&&"get"in n&&null!==(o=n.get(t,e)))&&null==(o=a.find.attr(t,e))?void 0:o)},attrHooks:{type:{set:function(t,e){var r;if(!n.radioValue&&"radio"===e&&o(t,"input"))return r=t.value,t.setAttribute("type",e),r&&(t.value=r),e}}},removeAttr:function(t,e){var r,o=0,
// Attribute names can contain non-HTML whitespace characters
// https://html.spec.whatwg.org/multipage/syntax.html#attributes-2
n=e&&e.match(i);if(n&&1===t.nodeType)for(;r=n[o++];)t.removeAttribute(r)}}),
// Hooks for boolean attributes
u={set:function(t,e,r){return!1===e?
// Remove boolean attributes when set to false
a.removeAttr(t,r):t.setAttribute(r,r),r}},a.each(a.expr.match.bool.source.match(/\w+/g),function(t,e){var u=c[e]||a.find.attr;c[e]=function(t,e,r){var o,n,i=e.toLowerCase();return r||(
// Avoid an infinite loop by temporarily removing this function from the getter
n=c[i],c[i]=o,o=null!=u(t,e,r)?i:null,c[i]=n),o}})});