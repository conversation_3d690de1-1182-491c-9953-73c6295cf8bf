define(["./core","./core/camelCase","./var/document","./var/isFunction","./var/rcssNum","./var/rnothtmlwhite","./css/var/cssExpand","./css/var/isHiddenWithinTree","./css/adjustCSS","./data/var/dataPriv","./css/showHide","./core/init","./queue","./deferred","./traversing","./manipulation","./css","./effects/Tween"],function(v,y,e,x,i,s,r,m,o,g,q){"use strict";var T,t,k=/^(?:toggle|show|hide)$/,u=/queueHooks$/;function n(){t&&(!1===e.hidden&&window.requestAnimationFrame?window.requestAnimationFrame(n):window.setTimeout(n,v.fx.interval),v.fx.tick())}
// Animations created synchronously will run synchronously
function b(){return window.setTimeout(function(){T=void 0}),T=Date.now()}
// Generate parameters to create a standard animation
function a(e,t){var n,i=0,o={height:e};
// If we include width, step value is 1 to do all cssExpand values,
// otherwise step value is 2 to skip over Left and Right
for(t=t?1:0;i<4;i+=2-t)o["margin"+(n=r[i])]=o["padding"+n]=e;return t&&(o.opacity=o.width=e),o}function H(e,t,n){for(var i,o=(E.tweeners[t]||[]).concat(E.tweeners["*"]),s=0,r=o.length;s<r;s++)if(i=o[s].call(n,t,e))
// We're done with this property
return i}function E(o,e,t){var n,s,i,r,u,a,f,l=0,d=E.prefilters.length,c=v.Deferred().always(function(){
// Don't match elem in the :animated selector
delete p.elem}),p=function(){if(!s){for(var e=T||b(),e=Math.max(0,h.startTime+h.duration-e),t=1-(e/h.duration||0),n=0,i=h.tweens.length;n<i;n++)h.tweens[n].run(t);
// If there's more to do, yield
if(c.notifyWith(o,[h,t,e]),t<1&&i)return e;
// If this was an empty animation, synthesize a final progress notification
i||c.notifyWith(o,[h,1,0]),
// Resolve the animation and report its conclusion
c.resolveWith(o,[h])}return!1},h=c.promise({elem:o,props:v.extend({},e),opts:v.extend(!0,{specialEasing:{},easing:v.easing._default},t),originalProperties:e,originalOptions:t,startTime:T||b(),duration:t.duration,tweens:[],createTween:function(e,t){t=v.Tween(o,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(t),t},stop:function(e){var t=0,
// If we are going to the end, we want to run all the tweens
// otherwise we skip this part
n=e?h.tweens.length:0;if(!s){for(s=!0;t<n;t++)h.tweens[t].run(1);
// Resolve when we played the last frame; otherwise, reject
e?(c.notifyWith(o,[h,1,0]),c.resolveWith(o,[h,e])):c.rejectWith(o,[h,e])}return this}}),w=h.props,m=w,g=h.opts.specialEasing;
// camelCase, specialEasing and expand cssHook pass
for(i in m)if(u=g[r=y(i)],a=m[i],Array.isArray(a)&&(u=a[1],a=m[i]=a[0]),i!==r&&(m[r]=a,delete m[i]),(f=v.cssHooks[r])&&"expand"in f)
// Not quite $.extend, this won't overwrite existing keys.
// Reusing 'index' because we have the correct "name"
for(i in a=f.expand(a),delete m[r],a)i in m||(m[i]=a[i],g[i]=u);else g[r]=u;for(;l<d;l++)if(n=E.prefilters[l].call(h,o,w,h.opts))return x(n.stop)&&(v._queueHooks(h.elem,h.opts.queue).stop=n.stop.bind(n)),n;return v.map(w,H,h),x(h.opts.start)&&h.opts.start.call(o,h),
// Attach callbacks from options
h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),v.fx.timer(v.extend(p,{elem:o,anim:h,queue:h.opts.queue})),h}return v.Animation=v.extend(E,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return o(n.elem,e,i.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,o=(e=x(e)?(t=e,["*"]):e.match(s)).length;i<o;i++)n=e[i],E.tweeners[n]=E.tweeners[n]||[],E.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,o,s,r,u,a,f,l="width"in t||"height"in t,d=this,c={},p=e.style,h=e.nodeType&&m(e),w=g.get(e,"fxshow");
// Queue-skipping animations hijack the fx hooks
// Detect show/hide animations
for(i in n.queue||(null==(r=v._queueHooks(e,"fx")).unqueued&&(r.unqueued=0,u=r.empty.fire,r.empty.fire=function(){r.unqueued||u()}),r.unqueued++,d.always(function(){
// Ensure the complete handler is called before this completes
d.always(function(){r.unqueued--,v.queue(e,"fx").length||r.empty.fire()})})),t)if(o=t[i],k.test(o)){if(delete t[i],s=s||"toggle"===o,o===(h?"hide":"show")){
// Pretend to be hidden if this is a "show" and
// there is still data from a stopped show/hide
if("show"!==o||!w||void 0===w[i])continue;h=!0}c[i]=w&&w[i]||v.style(e,i)}
// Bail out if this is a no-op like .hide().hide()
if((a=!v.isEmptyObject(t))||!v.isEmptyObject(c))for(i in
// Restrict "overflow" and "display" styles during box animations
l&&1===e.nodeType&&(
// Support: IE <=9 - 11, Edge 12 - 15
// Record all 3 overflow attributes because IE does not infer the shorthand
// from identically-valued overflowX and overflowY and Edge just mirrors
// the overflowX value there.
n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(
// Identify a display type, preferring old show/hide data over the CSS cascade
f=w&&w.display)&&(f=g.get(e,"display")),"none"===(l=v.css(e,"display"))&&(f?l=f:(
// Get nonempty value(s) by temporarily forcing visibility
q([e],!0),f=e.style.display||f,l=v.css(e,"display"),q([e]))),"inline"===l||"inline-block"===l&&null!=f)&&"none"===v.css(e,"float")&&(
// Restore the original display value at the end of pure show/hide animations
a||(d.done(function(){p.display=f}),null==f&&(l=p.display,f="none"===l?"":l)),p.display="inline-block"),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),
// Implement show/hide animations
a=!1,c)
// General show/hide setup for this element animation
a||(w?"hidden"in w&&(h=w.hidden):w=g.access(e,"fxshow",{display:f}),
// Store hidden/visible for toggle so `.stop().toggle()` "reverses"
s&&(w.hidden=!h),
// Show elements before animating them
h&&q([e],!0)
/* eslint-disable no-loop-func */,d.done(function(){for(i in
/* eslint-enable no-loop-func */
// The final step of a "hide" animation is actually hiding the element
h||q([e]),g.remove(e,"fxshow"),c)v.style(e,i,c[i])})),
// Per-property setup
a=H(h?w[i]:0,i,d),i in w||(w[i]=a.start,h&&(a.end=a.start,a.start=0))}],prefilter:function(e,t){t?E.prefilters.unshift(e):E.prefilters.push(e)}}),v.speed=function(e,t,n){var i=e&&"object"==typeof e?v.extend({},e):{complete:n||!n&&t||x(e)&&e,duration:e,easing:n&&t||t&&!x(t)&&t};
// Go to the end state if fx are off
return v.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in v.fx.speeds?i.duration=v.fx.speeds[i.duration]:i.duration=v.fx.speeds._default),
// Normalize opt.queue - true/undefined/null -> "fx"
null!=i.queue&&!0!==i.queue||(i.queue="fx"),
// Queueing
i.old=i.complete,i.complete=function(){x(i.old)&&i.old.call(this),i.queue&&v.dequeue(this,i.queue)},i},v.fn.extend({fadeTo:function(e,t,n,i){
// Show any hidden elements after setting opacity to 0
return this.filter(m).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){function o(){
// Operate on a copy of prop so per-property easing won't be lost
var e=E(this,v.extend({},t),r);
// Empty animations, or finishing resolves immediately
(s||g.get(this,"finish"))&&e.stop(!0)}var s=v.isEmptyObject(t),r=v.speed(e,n,i);return o.finish=o,s||!1===r.queue?this.each(o):this.queue(r.queue,o)},stop:function(o,e,s){function r(e){var t=e.stop;delete e.stop,t(s)}return"string"!=typeof o&&(s=e,e=o,o=void 0),e&&this.queue(o||"fx",[]),this.each(function(){var e=!0,t=null!=o&&o+"queueHooks",n=v.timers,i=g.get(this);if(t)i[t]&&i[t].stop&&r(i[t]);else for(t in i)i[t]&&i[t].stop&&u.test(t)&&r(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=o&&n[t].queue!==o||(n[t].anim.stop(s),e=!1,n.splice(t,1));
// Start the next in the queue if the last step wasn't forced.
// Timers currently will call their complete callbacks, which
// will dequeue but only if they were gotoEnd.
!e&&s||v.dequeue(this,o)})},finish:function(r){return!1!==r&&(r=r||"fx"),this.each(function(){var e,t=g.get(this),n=t[r+"queue"],i=t[r+"queueHooks"],o=v.timers,s=n?n.length:0;
// Enable finishing flag on private data
// Look for any active animations, and finish them
for(t.finish=!0,
// Empty the queue first
v.queue(this,r,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===r&&(o[e].anim.stop(!0),o.splice(e,1));
// Look for any animations in the old queue and finish them
for(e=0;e<s;e++)n[e]&&n[e].finish&&n[e].finish.call(this);
// Turn off finishing flag
delete t.finish})}}),v.each(["toggle","show","hide"],function(e,i){var o=v.fn[i];v.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?o.apply(this,arguments):this.animate(a(i,!0),e,t,n)}}),
// Generate shortcuts for custom animations
v.each({slideDown:a("show"),slideUp:a("hide"),slideToggle:a("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){v.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),v.timers=[],v.fx.tick=function(){var e,t=0,n=v.timers;for(T=Date.now();t<n.length;t++)
// Run the timer and safely remove it when done (allowing for external removal)
(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||v.fx.stop(),T=void 0},v.fx.timer=function(e){v.timers.push(e),v.fx.start()},v.fx.interval=13,v.fx.start=function(){t||(t=!0,n())},v.fx.stop=function(){t=null},v.fx.speeds={slow:600,fast:200,
// Default speed
_default:400},v});