define(["./core","./var/document","./var/documentElement","./var/isFunction","./var/rnothtmlwhite","./var/rcheckableType","./var/slice","./data/var/acceptData","./data/var/dataPriv","./core/nodeName","./core/init","./selector"],function(m,n,v,i,y,t,o,g,b,a){"use strict";var T=/^([^.]*)(?:\.(.+)|)/;function s(){return!0}function l(){return!1}
// Support: IE <=9 - 11+
// focus() and blur() are asynchronous, except when they are no-op.
// So expect focus to be synchronous when the element is already active,
// and blur to be synchronous when the element is not already active.
// (focus and blur are always synchronous in other supported browsers,
// this just defines when we can count on it).
function r(e,t){return e===
// Support: IE <=9 only
// Accessing document.activeElement can throw unexpectedly
// https://bugs.jquery.com/ticket/13393
function(){try{return n.activeElement}catch(e){}}()==("focus"===t)}function c(e,t,n,i,a,r){var o,s;
// Types can be a map of types/handlers
if("object"==typeof t){for(s in
// ( types-Object, selector, data )
"string"!=typeof n&&(
// ( types-Object, data )
i=i||n,n=void 0),t)c(e,s,n,i,t[s],r);return e}if(null==i&&null==a?(
// ( types, fn )
a=n,i=n=void 0):null==a&&("string"==typeof n?(
// ( types, selector, fn )
a=i,i=void 0):(
// ( types, data, fn )
a=i,i=n,n=void 0)),!1===a)a=l;else if(!a)return e;return 1===r&&(o=a,
// Use same guid so caller can remove using origFn
(a=function(e){
// Can use an empty set, since event contains the info
return m().off(e),o.apply(this,arguments)}).guid=o.guid||(o.guid=m.guid++)),e.each(function(){m.event.add(this,t,a,i,n)})}
/*
 * Helper functions for managing events -- not part of the public interface.
 * Props to Dean Edwards' addEvent library for many of the ideas.
 */
// Ensure the presence of an event listener that handles manually-triggered
// synthetic events by interrupting progress until reinvoked in response to
// *native* events that it fires directly, ensuring that state changes have
// already occurred before other listeners are invoked.
function u(e,a,r){
// Missing expectSync indicates a trigger call, which must force setup through jQuery.event.add
r?(
// Register the controller as a special universal handler for all event namespaces
b.set(e,a,!1),m.event.add(e,a,{namespace:!1,handler:function(e){var t,n,i=b.get(this,a);if(1&e.isTrigger&&this[a]){
// Interrupt processing of the outer synthetic .trigger()ed event
// Saved data should be false in such cases, but might be a leftover capture object
// from an async native handler (gh-4350)
if(i.length)(m.event.special[a]||{}).delegateType&&e.stopPropagation();
// If this is a native event triggered above, everything is now in order
// Fire an inner synthetic event with the original arguments
else if(
// Store arguments for use when handling the inner native event
// There will always be at least one argument (an event object), so this array
// will not be confused with a leftover capture object.
i=o.call(arguments),b.set(this,a,i),
// Trigger the native event and capture its result
// Support: IE <=9 - 11+
// focus() and blur() are asynchronous
t=r(this,a),this[a](),i!==(n=b.get(this,a))||t?b.set(this,a,!1):n={},i!==n)
// Support: Chrome 86+
// In Chrome, if an element having a focusout handler is blurred by
// clicking outside of it, it invokes the handler synchronously. If
// that handler calls `.remove()` on the element, the data is cleared,
// leaving `result` undefined. We need to guard against this.
// Cancel the outer synthetic event
return e.stopImmediatePropagation(),e.preventDefault(),n&&n.value;
// If this is an inner synthetic event for an event with a bubbling surrogate
// (focus or blur), assume that the surrogate already propagated from triggering the
// native event and prevent that from happening again here.
// This technically gets the ordering wrong w.r.t. to `.trigger()` (in which the
// bubbling surrogate propagates *after* the non-bubbling base), but that seems
// less bad than duplication.
}else i.length&&(
// ...and capture the result
b.set(this,a,{value:m.event.trigger(
// Support: IE <=9 - 11+
// Extend with the prototype to reset the above stopImmediatePropagation()
m.extend(i[0],m.Event.prototype),i.slice(1),this)}),
// Abort handling of the native event
e.stopImmediatePropagation())}})):void 0===b.get(e,a)&&m.event.add(e,a,s)}return m.event={global:{},add:function(t,e,n,i,a){var r,o,s,l,c,u,p,d,h,f=b.get(t);
// Only attach events to objects that accept data
if(g(t))for(
// Caller can pass in an object of custom data in lieu of the handler
n.handler&&(n=(r=n).handler,a=r.selector),
// Ensure that invalid selectors throw exceptions at attach time
// Evaluate against documentElement in case elem is a non-element node (e.g., document)
a&&m.find.matchesSelector(v,a),
// Make sure that the handler has a unique ID, used to find/remove it later
n.guid||(n.guid=m.guid++),
// Init the element's event structure and main handler, if this is the first
s=(s=f.events)||(f.events=Object.create(null)),o=(o=f.handle)||(f.handle=function(e){
// Discard the second event of a jQuery.event.trigger() and
// when an event is called after a page has unloaded
return void 0!==m&&m.event.triggered!==e.type?m.event.dispatch.apply(t,arguments):void 0}),l=(
// Handle multiple events separated by a space
e=(e||"").match(y)||[""]).length;l--;)p=h=(d=T.exec(e[l])||[])[1],d=(d[2]||"").split(".").sort(),
// There *must* be a type, no attaching namespace-only handlers
p&&(
// If event changes its type, use the special event handlers for the changed type
c=m.event.special[p]||{},
// If selector defined, determine special event api type, otherwise given type
p=(a?c.delegateType:c.bindType)||p,
// Update special based on newly reset type
c=m.event.special[p]||{},
// handleObj is passed to all event handlers
h=m.extend({type:p,origType:h,data:i,handler:n,guid:n.guid,selector:a,needsContext:a&&m.expr.match.needsContext.test(a),namespace:d.join(".")},r),
// Init the event handler queue if we're the first
(u=s[p])||((u=s[p]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,i,d,o))||t.addEventListener&&t.addEventListener(p,o),c.add&&(c.add.call(t,h),h.handler.guid||(h.handler.guid=n.guid)),
// Add to the element's handler list, delegates in front
a?u.splice(u.delegateCount++,0,h):u.push(h),
// Keep track of which events have ever been used, for event optimization
m.event.global[p]=!0)},
// Detach an event or set of events from an element
remove:function(e,t,n,i,a){var r,o,s,l,c,u,p,d,h,f,v,g=b.hasData(e)&&b.get(e);if(g&&(l=g.events)){for(c=(
// Once for each type.namespace in types; type may be omitted
t=(t||"").match(y)||[""]).length;c--;)
// Unbind all events (on this namespace, if provided) for the element
if(h=v=(s=T.exec(t[c])||[])[1],f=(s[2]||"").split(".").sort(),h){for(p=m.event.special[h]||{},d=l[h=(i?p.delegateType:p.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),
// Remove matching events
o=r=d.length;r--;)u=d[r],!a&&v!==u.origType||n&&n.guid!==u.guid||s&&!s.test(u.namespace)||i&&i!==u.selector&&("**"!==i||!u.selector)||(d.splice(r,1),u.selector&&d.delegateCount--,p.remove&&p.remove.call(e,u));
// Remove generic event handler if we removed something and no more handlers exist
// (avoids potential for endless recursion during removal of special event handlers)
o&&!d.length&&(p.teardown&&!1!==p.teardown.call(e,f,g.handle)||m.removeEvent(e,h,g.handle),delete l[h])}else for(h in l)m.event.remove(e,h+t[c],n,i,!0);
// Remove data and the expando if it's no longer used
m.isEmptyObject(l)&&b.remove(e,"handle events")}},dispatch:function(e){var t,n,i,a,r,o=new Array(arguments.length),
// Make a writable jQuery.Event from the native event object
s=m.event.fix(e),e=(b.get(this,"events")||Object.create(null))[s.type]||[],l=m.event.special[s.type]||{};
// Use the fix-ed jQuery.Event rather than the (read-only) native event
for(o[0]=s,t=1;t<arguments.length;t++)o[t]=arguments[t];
// Call the preDispatch hook for the mapped type, and let it bail if desired
if(s.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,s)){for(
// Determine handlers
r=m.event.handlers.call(this,s,e),
// Run delegates first; they may want to stop propagation beneath us
t=0;(i=r[t++])&&!s.isPropagationStopped();)for(s.currentTarget=i.elem,n=0;(a=i.handlers[n++])&&!s.isImmediatePropagationStopped();)
// If the event is namespaced, then each handler is only invoked if it is
// specially universal or its namespaces are a superset of the event's.
s.rnamespace&&!1!==a.namespace&&!s.rnamespace.test(a.namespace)||(s.handleObj=a,s.data=a.data,void 0!==(a=((m.event.special[a.origType]||{}).handle||a.handler).apply(i.elem,o))&&!1===(s.result=a)&&(s.preventDefault(),s.stopPropagation()));
// Call the postDispatch hook for the mapped type
return l.postDispatch&&l.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,i,a,r,o,s=[],l=t.delegateCount,c=e.target;
// Find delegate handlers
if(l&&
// Support: IE <=9
// Black-hole SVG <use> instance trees (trac-13180)
c.nodeType&&
// Support: Firefox <=42
// Suppress spec-violating clicks indicating a non-primary pointer button (trac-3861)
// https://www.w3.org/TR/DOM-Level-3-Events/#event-type-click
// Support: IE 11 only
// ...but not arrow key "clicks" of radio inputs, which can have `button` -1 (gh-2343)
!("click"===e.type&&1<=e.button))for(;c!==this;c=c.parentNode||this)
// Don't check non-elements (#13208)
// Don't process clicks on disabled elements (#6911, #8165, #11382, #11764)
if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(r=[],o={},n=0;n<l;n++)void 0===o[
// Don't conflict with Object.prototype properties (#13203)
a=(i=t[n]).selector+" "]&&(o[a]=i.needsContext?-1<m(a,this).index(c):m.find(a,this,null,[c]).length),o[a]&&r.push(i);r.length&&s.push({elem:c,handlers:r})}
// Add the remaining (directly-bound) handlers
return c=this,l<t.length&&s.push({elem:c,handlers:t.slice(l)}),s},addProp:function(t,e){Object.defineProperty(m.Event.prototype,t,{enumerable:!0,configurable:!0,get:i(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[m.expando]?e:new m.Event(e)},special:{load:{
// Prevent triggered image.load events from bubbling to window.load
noBubble:!0},click:{
// Utilize native event to ensure correct state for checkable inputs
setup:function(e){
// For mutual compressibility with _default, replace `this` access with a local var.
// `|| data` is dead code meant only to preserve the variable through minification.
e=this||e;
// Claim the first handler
// Return false to allow normal processing in the caller
return t.test(e.type)&&e.click&&a(e,"input")&&
// dataPriv.set( el, "click", ... )
u(e,"click",s),!1},trigger:function(e){
// For mutual compressibility with _default, replace `this` access with a local var.
// `|| data` is dead code meant only to preserve the variable through minification.
e=this||e;
// Force setup before triggering a click
// Return non-false to allow normal event-path propagation
return t.test(e.type)&&e.click&&a(e,"input")&&u(e,"click"),!0},
// For cross-browser consistency, suppress native .click() on links
// Also prevent it if we're currently inside a leveraged native-event stack
_default:function(e){e=e.target;return t.test(e.type)&&e.click&&a(e,"input")&&b.get(e,"click")||a(e,"a")}},beforeunload:{postDispatch:function(e){
// Support: Firefox 20+
// Firefox doesn't alert if the returnValue field is not set.
void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},m.removeEvent=function(e,t,n){
// This "if" is needed for plain objects
e.removeEventListener&&e.removeEventListener(t,n)},m.Event=function(e,t){
// Allow instantiation without the 'new' keyword
if(!(this instanceof m.Event))return new m.Event(e,t);
// Event object
e&&e.type?(this.originalEvent=e,this.type=e.type,
// Events bubbling up the document may have been marked as prevented
// by a handler lower down the tree; reflect the correct value.
this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&
// Support: Android <=2.3 only
!1===e.returnValue?s:l,
// Create target properties
// Support: Safari <=6 - 7 only
// Target should not be a text node (#504, #13143)
this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,
// Put explicitly provided properties onto the event object
t&&m.extend(this,t),
// Create a timestamp if incoming event doesn't have one
this.timeStamp=e&&e.timeStamp||Date.now(),
// Mark it as fixed
this[m.expando]=!0},
// jQuery.Event is based on DOM3 Events as specified by the ECMAScript Language Binding
// https://www.w3.org/TR/2003/WD-DOM-Level-3-Events-20030331/ecma-script-binding.html
m.Event.prototype={constructor:m.Event,isDefaultPrevented:l,isPropagationStopped:l,isImmediatePropagationStopped:l,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=s,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=s,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=s,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},
// Includes all common event props including KeyEvent and MouseEvent specific props
m.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},m.event.addProp),m.each({focus:"focusin",blur:"focusout"},function(e,t){m.event.special[e]={
// Utilize native event if possible so blur/focus sequence is correct
setup:function(){
// Return false to allow normal processing in the caller
// Claim the first handler
// dataPriv.set( this, "focus", ... )
// dataPriv.set( this, "blur", ... )
return u(this,e,r),!1},trigger:function(){
// Return non-false to allow normal event-path propagation
// Force setup before trigger
return u(this,e),!0},
// Suppress native focus or blur as it's already being fired
// in leverageNative.
_default:function(){return!0},delegateType:t}}),
// Create mouseenter/leave events using mouseover/out and event-time checks
// so that event delegation works in jQuery.
// Do the same for pointerenter/pointerleave and pointerover/pointerout
//
// Support: Safari 7 only
// Safari sends mouseenter too often; see:
// https://bugs.chromium.org/p/chromium/issues/detail?id=470258
// for the description of the bug (it existed in older Chrome versions as well).
m.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,a){m.event.special[e]={delegateType:a,bindType:a,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;
// For mouseenter/leave call the handler if related is outside the target.
// NB: No relatedTarget if the mouse left/entered the browser window
return n&&(n===this||m.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=a),t}}}),m.fn.extend({on:function(e,t,n,i){return c(this,e,t,n,i)},one:function(e,t,n,i){return c(this,e,t,n,i,1)},off:function(e,t,n){var i,a;if(e&&e.preventDefault&&e.handleObj)
// ( event )  dispatched jQuery.Event
i=e.handleObj,m(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(
// ( types [, fn] )
n=t,t=void 0),!1===n&&(n=l),this.each(function(){m.event.remove(this,e,n,t)});
// ( types-object [, selector] )
for(a in e)this.off(a,t,e[a])}return this}}),m});