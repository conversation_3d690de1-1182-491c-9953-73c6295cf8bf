define(["./core","./core/toType","./var/isFunction","./var/rnothtmlwhite"],function(l,p,d,g){"use strict";
// Convert String-formatted options into Object-formatted ones
/*
 * Create a callback list using the following parameters:
 *
 *	options: an optional list of space-separated options that will change how
 *			the callback list behaves or a more traditional option object
 *
 * By default a callback list will act like an event callback list and can be
 * "fired" multiple times.
 *
 * Possible options:
 *
 *	once:			will ensure the callback list can only be fired once (like a Deferred)
 *
 *	memory:			will keep track of previous values and will call any callback added
 *					after the list has been fired right away with the latest "memorized"
 *					values (like a Deferred)
 *
 *	unique:			will ensure a callback can only be added once (no duplicate in the list)
 *
 *	stopOnFalse:	interrupt callings when a callback returns false
 *
 */
return l.Callbacks=function(e){var n,r;
// Convert options from String-formatted to Object-formatted if needed
// (we check in cache first)
e="string"==typeof e?(n=e,r={},l.each(n.match(g)||[],function(n,t){r[t]=!0}),r):l.extend({},e);function
// Fire callbacks
i(){for(
// Enforce single-firing
o=o||e.once,
// Execute callbacks for all pending executions,
// respecting firingIndex overrides and runtime changes
c=u=!0;h.length;s=-1)for(t=h.shift();++s<f.length;)
// Run callback and check for early termination
!1===f[s].apply(t[0],t[1])&&e.stopOnFalse&&(
// Jump to end and forget the data so .add doesn't re-fire
s=f.length,t=!1);
// Forget the data if we're done with it
e.memory||(t=!1),u=!1,
// Clean up if we're done firing for good
o&&(
// Keep an empty list if we have data for future add calls
f=t?[]:"")}var// Flag to know if list is currently firing
u,
// Last fire value for non-forgettable lists
t,
// Flag to know if list was already fired
c,
// Flag to prevent firing
o,
// Actual callback list
f=[],
// Queue of execution data for repeatable lists
h=[],
// Index of currently firing callback (modified by add/remove as needed)
s=-1,
// Actual Callbacks object
a={
// Add a callback or a collection of callbacks to the list
add:function(){return f&&(
// If we have memory from a past run, we should fire after adding
t&&!u&&(s=f.length-1,h.push(t)),function r(n){l.each(n,function(n,t){d(t)?e.unique&&a.has(t)||f.push(t):t&&t.length&&"string"!==p(t)&&
// Inspect recursively
r(t)})}(arguments),t)&&!u&&i(),this},
// Remove a callback from the list
remove:function(){return l.each(arguments,function(n,t){for(var r;-1<(r=l.inArray(t,f,r));)f.splice(r,1),
// Handle firing indexes
r<=s&&s--}),this},
// Check if a given callback is in the list.
// If no argument is given, return whether or not list has callbacks attached.
has:function(n){return n?-1<l.inArray(n,f):0<f.length},
// Remove all callbacks from the list
empty:function(){return f=f&&[],this},
// Disable .fire and .add
// Abort any current/pending executions
// Clear all callbacks and values
disable:function(){return o=h=[],f=t="",this},disabled:function(){return!f},
// Disable .fire
// Also disable .add unless we have memory (since it would have no effect)
// Abort any pending executions
lock:function(){return o=h=[],t||u||(f=t=""),this},locked:function(){return!!o},
// Call all callbacks with the given context and arguments
fireWith:function(n,t){return o||(t=[n,(t=t||[]).slice?t.slice():t],h.push(t),u)||i(),this},
// Call all the callbacks with the given arguments
fire:function(){return a.fireWith(this,arguments),this},
// To know if the callbacks have already been called at least once
fired:function(){return!!c}};return a},l});