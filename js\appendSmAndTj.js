//该文件功能：向速卖通的搜索产品页面和分类页面插入统计按钮，产品列表的小图标，
//插入小图标
function appendTjTool(){try{injectSendParamsToContentScript("",{},cmd="isLoginrequest_seven_data_todo",type="web_event"),
//插入统计按钮
setInterval(()=>{var e,t=localStorage.getItem("smt_config_tj_class"),n=(globTjClass=JSON.parse(t),window.location.pathname),t=window.location.href,a=globTjClass.is_append_tool,a=(a.forEach(e=>{-1!==n.indexOf(e)&&0}),checkClassIs(n,"is_top_or_proudces")),i=checkClassIs(n,"is_search_or_category"),r=checkClassIs(n,"af");(-1!=a||-1!=i||-1!==r&&-1!==t.indexOf(globTjClass.search_text))&&(0<$(".self-my-icon").length&&(-1!==checkClassIs(n,"is_search_or_category")&&(""!=(e=getPosition($("body")[0],"tj_div_parent"))&&(a=(a=$(e).css("height")).replace("px",""),(a=parseInt(a))<30)&&$(e).css("height","30px"),""!=(i=getPosition($("body")[0],"tj_div_1")))&&0==$(".TjDataButton").length&&(".SnowSearchFilter_SortPanel__searchHeading__1rp0i"==i?$(i).before('<div><button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button></div>"):-1!==location.href.indexOf("aliexpress.ru/")?(r='<button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button>",".RedFilter_TopPanel__container__oegso"==i||".haze-select_Select__container__15dpmq.RedFilter_SortBlock__select__gd8xh"==i?$(i).before(r):$(i).append(r)):".cm_a4"==i?$(i).after('<button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button>"):$(i).append('<button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button>"),appendAutoExpand("search")),-1!==checkClassIs(n,"is_store_product_list")&&(-1!==n.indexOf("store/")&&-1!==n.indexOf("/search")&&""!=(e=getPosition($("body")[0],"ti_div_2"))&&0==$(".TjDataButton").length&&0<$(".self-my-icon").length&&(0<$("#bd").length?$(e).before('<div style="margin: 0 auto;"> <button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button></div>"):$(e).before('<div> <button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button></div>"),appendAutoExpandStore()),-1!==n.indexOf("store/"))&&-1==n.indexOf("/search")&&""!=(e=getPosition($("body")[0],"ti_div_2"))&&0==$(".TjDataButton").length&&0<$(".self-my-icon").length&&("#hd"===e&&"hc"===$(e).prev("div").attr("id")?$("#right").children("div").eq(0).children("div").eq(0).append('<div class="tongji" style="display:inline-block"> <button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button></div>"):$(e).before('<div class="tongji"> <button style="margin-left:20px;" type="button" class="w-button btn btn-default TjDataButton" onclick="TjData()">'+bg_i18n("lang_data_tj")+"</button></div>"),appendAutoExpandStore("store")),0==$(".TjDataButton").length)&&injectSendParamsToContentScript("",{url:window.location.href,msg:"插入统计按钮失败"},"add_log","web_event"),appendSmallIco(),changeSoldCss())},3e3)}catch(e){console.log("appendTjTool Error",e)}}
//插入七日数据
function isLoginrequest_seven_data(e){if(-1==location.href.indexOf("/item/")){!1===e?($(".auto-expand").css("display","none"),$(".TjDataButton").css("display","none"),0!=localStorage.getItem("smt_level")&&localStorage.setItem("smt_level",0)):1!=localStorage.getItem("smt_level")&&localStorage.setItem("smt_level",1);e=localStorage.getItem("get_seven_data");if(1!=e){localStorage.setItem("get_seven_data",1);var t,n,a,i=getProductDiv(),r=[];
// var objDoms = $("[class*='store_product']")
// if(objDoms.length == 0){
//     localStorage.setItem('get_seven_data',0)
//     return
// }
for(t in i)isNaN(t)||""==(a=eachGetAttribute(n=i[t],"product_a_href"))||!1===(a=getProductId(a))||0<$(n).find("#ixspy-product-id-"+a).length||
// //判断该id 是否已经获取了七日数据了,获取了就结束
// if($(".seven-data-product-"+productId).length > 0){
//     continue;
// }
//插入Loading 层数据
// createSmallLoadingDom("store_product_"+productId)
//没有获取的话就进行保存产品id,待会传入到后端去获取
r.push(a);0==r.length?localStorage.setItem("get_seven_data",0):
//传递到后端去
injectSendParamsToContentScript("",{init_data:dealWithProductIdByRunParams(r)},cmd="request_seven_data",type="web_event");
//检测产品id的信息，window.runParams
}}}
//产品列表根据runParams 获取 x_object_id
function dealWithProductIdByRunParams(e){var t={};try{var n=window.runParams;
// var runParams = localStorage.getItem("run_params")
if(null==(n=null!=n&&n?n:localStorage.getItem("run_params"))||""==n)return e;if("undefined"==n||null==n||""==n||null==n)return e;if(null==(n=JSON.parse(n)).mods||""==n.mods||null==n.mods)return e;if(null==n.mods.itemList)return e;var a,i=n.mods.itemList.content;for(a in e){var r,o=e[a];for(r in i){var l=i[r];o==l.productId&&(t[o]=l.trace.utLogMap.x_object_id)}}return t}catch(e){console.log("错误",e)}return e}function appendSevenData(){setInterval(()=>{injectSendParamsToContentScript("",{},cmd="isLoginrequest_seven_data",type="web_event")},3e3)}
//-----------------------------------------------------------------------------------------------------------辅助方法
//插入小图标，图表 和 折线图
function appendSmallIco(){try{var e=window.location.pathname,t=(window.location.href,getProductDiv());if(0==t.length)injectSendParamsToContentScript("",{url:window.location.href,msg:"没有获取到产品列表,插入小图标失败"},"add_log","web_event");else{
//店铺名称的长度
// $("a[href*='/store/']").css('width','75%')
//遍历产品dom 插入对icon 收藏和图表数据
for(var n in t)if(!isNaN(n)){var a=t[n],i=eachGetAttribute(a,"product_a_href");
//1. 获取产品id
if(void 0===(
// console.log('objProductDom', href)
i=""!=i&&void 0!==i?i:(0===$(a).children("a").length?$(a).children("div"):$(a)).children("a").attr("href")))
//可能存在之前的渲染,需要删除
$(a).find(".temp-seven-data")&&$(a).find(".temp-seven-data").remove(),$(a).find(".self-my-icon")&&$(a).find(".self-my-icon").remove();else{var r=getProductId(i);
// console.log('product_id',product_id)
if(""!=r){
//判断是否已经插入过了
if(-1!==location.href.indexOf("aliexpress.ru/")){
//跳过 data-w-page-area="rcmdprod"
if(0<$(a).find(".self-my-icon").length)continue;if(0<$(a).parent().parent('div[data-w-page-area="rcmdprod"]').length)continue;if(0<$(a).parent().parent().parent('div[data-w-page-area="rcmdprod"]').length)continue}else{if(0<$(a).find("#ixspy-product-id-"+r).length)
// $(dom).find('.temp-seven-data').remove()
continue;$(a).find(".temp-seven-data").remove(),$(a).find(".self-my-icon").remove(),$(a).find(".test-small-chart").remove(),$(a).find(".zhexiantu").remove(),$(a).find(".favted-products-1").remove()}var o,l,s="";if(0<$(a).find("img").length)for(l in o=$(a).find("img")){var c=$(o[l]).attr("height");if(!(null!=c&&c<50)){s="'"+(s=$(o[l]).attr("src"))+"'";break}}if(""==s&&-1!=window.location.href.indexOf("aliexpress.ru"))for(l in o=$(a).parent().find("img")){c=$(o[l]).attr("height");if(!(null!=c&&c<50)){s="'"+(s=$(o[l]).attr("src"))+"'";break}}
//2. 判断是已插入小图标了,已存在的话就跳过，不存在的话，移除一些可能存在的内容
// var tempAlreadyObj = $(dom).find('.test-small-chart')
// if(tempAlreadyObj.length > 0){
//
//     var tempClass = $(dom).find('.test-small-chart').attr("class")
//     if(tempClass.indexOf(product_id+"_h3") !==-1){
//         continue;
//     }else{
//
//         //移除七日数据的展示和移除class
//
//     }
// }
//判断当前是什么页面，不同的页面插入的位置不同
var d,p=chrome.runtime.getURL("img/zhexian.png"),f=bg_i18n("lang_view_data"),h=bg_i18n("lang_no_favted"),g=chrome.runtime.getURL("img/no_favted.png"),_=chrome.runtime.getURL("img/similar.png");bg_i18n("look_more");
//分类搜索页面，小图标是插入到店铺名字旁边 ()
//[href*=\"\/item\/\"]
if(-1==checkClassIs(e,"is_top_or_proudces")){var u=eachGetAttribute(a,"shop_a_href","attr"),m=(0<$(a).find("div[class*='us--price-sale--3MpboLs']").length&&(u="div[class*='us--price-sale--3MpboLs']"),0<$(a).find('[class*="store_product_"]').length&&removeSevenClassName($($(a).find('[class*="store_product_"]')[0]).attr("class")),$($(a).find(".sales-total-div")).parent().remove(),
// var autoSale = localStorage.getItem("smt_sales_auto")
// if(autoSale == 1){
//     $($(dom).find('.sales-total-div')).parent().remove()
// }else{
//     if($(dom).find('.sales-total-div').length > 1){
//         $($(dom).find('.sales-total-div')).parent().remove()
//     }
// }
$($(a).find(u)[0]).parent().addClass("store_product_"+r),bg_i18n("lang_search_img")),v='<div style="display: block;height:25px;text-align: right;" onclick="alinkEventDisable(event)" class="self-my-icon self-my-icon-'+r+'"> '+showImageSearchChoise()+' <img onclick="toSearchByImg('+s+","+r+',event,this)" style="float:none;margin-right:8px;" title="'+m+' By IXSPY" class="favted-products favted-products-1 " src="'+_+'">';if(v=(v+='<img style="float:none;" title="'+f+' " onclick="clickSmallChart('+r+',event)" class="zhexiantu zhexiantu_'+r+'" src="'+p+'">')+('<img style="float:none;margin-right:8px;" onclick="favted_products('+r+',event)" title="'+h+'" class="favted-products favted-products-1 favted_'+r+'" src="'+g+'">')+"</div>",-1!==location.href.indexOf("aliexpress.ru/"))try{$($(a).find(u)[0]).parent().parent().parent().hasClass("SnowSearchRecommendations_SnowSearchRecommendations__redShelf__37iru")?0==$($(a).find(u)[0]).parent().children(".self-my-icon").length&&$($(a).find(u)[0]).parent().append(v):0==$($(a).find(u)[0]).parent().next(".self-my-icon").length&&$($(a).find(u)[0]).parent().after(v)}catch(e){0==$($(a).find(u)[0]).parent().next(".self-my-icon").length&&$($(a).find(u)[0]).parent().after(v)}else'[href*="/item/"]'==u&&0<$($(a).find(u).children("div")[1]).length?0==$($(a).find(u).children("div")[1]).find(".self-my-icon").length&&$($(a).find(u).children("div")[1]).append(v):0==$($(a).find(u)[0]).parent().next(".self-my-icon").length&&$($(a).find(u)[0]).parent().after(v);
// if ($($(dom).find(tempClass)[0]).parent().parent().hasClass('product-img')) {
//     $($(dom).find(tempClass)[0]).parent().parent().after(temp)
// } else { 
//     $($(dom).find(tempClass)[0]).parent().after(temp)
// }
var b='<span class="sales-'+r+'"></span>',S=(0==$(".sales-"+r).length&&($($(a).find("[class='manhattan--tradeContainer--33O19sx']")).prepend(b),0<$($(a).find("[class='multi--tradeContainer--3TqP9qf']")).length)&&$($(a).find("[class='multi--tradeContainer--3TqP9qf']")).prepend(b),$(".self-my-icon-"+r).parent().width());
// $($(dom).find(tempClass)[0]).after('<img style="float: right;" title="'+lang_view_data+'" onclick="clickSmallChart('+product_id+',event)" class="zhexiantu zhexiantu_'+product_id+'" src="'+ZheXian+'">')
// $($(dom).find(tempClass)[0]).after('<img style="float:right;margin-right:8px;" onclick="favted_products('+product_id+',event)" title="'+lang_no_favted+'" class="favted-products favted-products-1 favted_'+product_id+'" src="'+no_favted+'">')
0==$(".self-my-icon-"+r).next(".test-small-chart").length&&$(".self-my-icon-"+r).after('<div onclick="alinkEventDisable(event)" style="width:'+S+'px"  class="test-small-chart '+r+'_h3"></div>')}
// if (flag != -1)
else{//店铺里的所有产品 店铺里的Top Selling
//店铺主页不需要商品的插件信息
let e=!1;!(0<$(".pc-store-nav-Products").length&&-1!=$(".pc-store-nav-Products").css("border-bottom").indexOf("none")||!(e=!0)),e&&(u=getPosition(a,"store_product_order"),$(a).css("height","100%"),m=bg_i18n("lang_search_img"),v=(v='<div style="display: inherit;height:25px" onclick="alinkEventDisable(event)" class="self-my-icon self-my-icon-'+r+'"> '+showImageSearchChoise()+' <img onclick="toSearchByImg('+s+","+r+',event,this)" style="float:left;margin-left:8px;" title="'+m+' By IXSPY" class="favted-products favted-products-1 " src="'+_+'">')+'<img style="float: left;margin-left:8px;" title="'+f+'" onclick="clickSmallChart('+r+',event)" class="zhexiantu" src="'+p+'"><img style="float:left;margin-left:8px;" onclick="favted_products('+r+',event)" title="'+h+'" class="favted-products favted-products-1 favted_'+r+'" src="'+g+'"></div>',
// $($(dom).find(tempClass)[0]).append('<img style="float:right;margin-right:8px;" onclick="favted_products('+product_id+',event)" title="'+lang_no_favted+'" class="favted-products favted-products-1 favted_'+product_id+'" src="'+no_favted+'">')
// $($(dom).find(tempClass)[0]).append('<img style="float: right;" title="'+lang_view_data+'" onclick="clickSmallChart('+product_id+',event)" class="zhexiantu" src="'+ZheXian+'">')
//俄罗斯地区列表和平铺展示位置不一样
//self-my-icon-
$($(a).find(u)[0]).parent().is("a")?0==$($(a).find(u)[0]).parent().next(".self-my-icon").length&&
//如果是a标签则放到标签外
$($(a).find(u)[0]).parent().after(v):void 0!==$(a).find(u)[0]?$($(a).find(u)[0]).is("a")?0==$($(a).find(u)[0]).children(".self-my-icon").length&&$($(a).find(u)[0]).append(v):0==$($(a).find(u)[0]).parent().children(".self-my-icon").length&&$($(a).find(u)[0]).parent().append(v):(v=(v='<div onclick="alinkEventDisable(event)" style="display: inherit;height:25px;position: absolute;z-index: 100;width: 100%;" class="self-my-icon self-my-icon-'+r+'"> '+showImageSearchChoise()+' <img onclick="toSearchByImg('+s+","+r+',event,this)" style="float:left;margin-left:8px;" title="'+m+' By IXSPY" class="favted-products favted-products-1 " src="'+_+'">')+'<img style="float: left;margin-left:8px;" title="'+f+'" onclick="clickSmallChart('+r+',event)" class="zhexiantu" src="'+p+'"><img style="float:left;margin-left:8px;" onclick="favted_products('+r+',event)" title="'+h+'" class="favted-products favted-products-1 favted_'+r+'" src="'+g+'"></div>',0<$(".red-snippet_RedSnippet__container__e15tmk.red-snippet_RedSnippet__horizontal__e15tmk").length&&-1!==location.href.indexOf("aliexpress.ru/")&&0<$(a).find(".red-snippet_RedSnippet__content__e15tmk").length&&0==$(a).find(".self-my-icon").length?(v=(v='<div onclick="alinkEventDisable(event)" style="display: inherit;height:25px;width: 100%;" class="self-my-icon self-my-icon-'+r+'"> '+showImageSearchChoise()+' <img onclick="toSearchByImg('+s+","+r+',event,this)" style="float:left;margin-left:8px;" title="'+m+' By IXSPY" class="favted-products favted-products-1 " src="'+_+'">')+'<img style="float: left;margin-left:8px;" title="'+f+'" onclick="clickSmallChart('+r+',event)" class="zhexiantu" src="'+p+'"><img style="float:left;margin-left:8px;" onclick="favted_products('+r+',event)" title="'+h+'" class="favted-products favted-products-1 favted_'+r+'" src="'+g+'"></div>',$(a).find(".red-snippet_RedSnippet__content__e15tmk").append(v)):0<$(a).children("a").length?0==$(a).children("a").eq(0).children(".self-my-icon").length&&$(a).children("a").eq(0).append(v):0<$(a).children("div").children("a").length&&0==$(a).children("div").children("a").eq(0).children(".self-my-icon").length&&(-1!==location.href.indexOf("aliexpress.ru/")?(v=(v='<div onclick="alinkEventDisable(event)" style="display: inherit;height:25px;width: 100%;" class="self-my-icon self-my-icon-'+r+'"> '+showImageSearchChoise()+' <img onclick="toSearchByImg('+s+","+r+',event,this)" style="float:left;margin-left:8px;" title="'+m+' By IXSPY" class="favted-products favted-products-1 " src="'+_+'">')+'<img style="float: left;margin-left:8px;" title="'+f+'" onclick="clickSmallChart('+r+',event)" class="zhexiantu" src="'+p+'"><img style="float:left;margin-left:8px;" onclick="favted_products('+r+',event)" title="'+h+'" class="favted-products favted-products-1 favted_'+r+'" src="'+g+'"></div>',$(a).children("div").children("a").eq(0).parent()):$(a).children("div").children("a").eq(0)).append(v)),$($(a).find(".sales-total-div")).parent().remove(),0<$(a).find('[class*="store_product_"]').length&&removeSevenClassName($($(a).find('[class*="store_product_"]')[0]).attr("class")),b='<span class="sales-'+r+'"></span>',0<$($(a).find("[class='manhattan--tradeContainer--33O19sx']")).length&&$($(a).find("[class='manhattan--tradeContainer--33O19sx']")).prepend(b),0<$($(a).find("[class='multi--tradeContainer--3TqP9qf']")).length&&$($(a).find("[class='multi--tradeContainer--3TqP9qf']")).prepend(b),d=$(".self-my-icon-"+r).parent().width(),0==$(".self-my-icon-"+r).next(".store_product_"+r).length)&&$(".self-my-icon-"+r).after('<div onclick="alinkEventDisable(event)" style="width:'+d+'px;margin-top:40px;"  class="test-small-chart '+r+"_h3 store_product_"+r+'"></div>')}}}}0==$(".test-small-chart").length&&injectSendParamsToContentScript("",{url:window.location.href,msg:"插入小图标失败"},"add_log","web_event")}}catch(e){injectSendParamsToContentScript("",{url:window.location.href,msg:"插入小图标失败异常",catch_error:e},"add_log","web_event"),console.log("appendSmallIco Error",e)}}function showImageSearchChoise(){var e=(e=navigator.language)||navigator.userLanguage;let t=`<div style="
    position: absolute;
    min-width:150px;
    `;return location.href.indexOf("aliexpress.ru/"),t=t+`
    padding: 10px;
    z-index: 999;
    text-align: center;
    display: none;" onmouseleave="closeImageSearchHidden()" class="choise_image_search_type_box_hidden">`+'<div class="smt_img_search_choise_item_list" onclick="todoSearchImage(this,1,event)">',
// dom += `速卖通以图搜图`
e.includes("zh")?t+="速卖通以图搜图":t+="Search the same Aliexpress products by image",t=t+"</div>"+'<div class="smt_img_search_choise_item_list" onclick="todoSearchImage(this,2,event)">',
// dom += `1688以图搜图`
e.includes("zh")?t+="1688以图搜图":t+="Searching for products by pictures in 1688",t=t+"</div>"+"</div>"}function appendAutoExpand(e){var t={},t=(t.lang_auto_expand=bg_i18n("lang_auto_expand"),pub_format(String(function(){
/*!
    <div class="auto-expand" >
         <label>
             <input onclick='autoSales()' style="vertical-align:middle;margin:0;opacity: 1 !important;appearance:checkbox;"  type="checkbox" id="toggleSale" checked  >
              <span  style="vertical-align:middle">#{lang_auto_expand}</span>
             </label>
    </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t));-1!==location.href.indexOf("aliexpress.ru/")?"search"==e&&(0<$(".SnowSearchSubcategories_CategoriesSnippets__snippetsWrap__14ap2").length?$(".SnowSearchSubcategories_CategoriesSnippets__snippetsWrap__14ap2").append(t):0<$(".SnowSearchHeading_SnowSearchHeading__searchHeading__b9qvy").length?$(".SnowSearchHeading_SnowSearchHeading__searchHeading__b9qvy").append(t):0<$(".RedFilter_TopPanel__container__oegso").length?$(".RedFilter_TopPanel__container__oegso").append(t):0<$(".TjDataButton").length&&$(".TjDataButton").after(t)):($(".sort--sort--324BGff").append(t),0<$(".topRefine2023--rightSort--1fG0bSp").length?$(".topRefine2023--rightSort--1fG0bSp").append(t):0<$(".TjDataButton").length&&$(".TjDataButton").after(t)),1==localStorage.getItem("smt_sales_auto")?$("#toggleSale").attr("checked","true"):$("#toggleSale").removeAttr("checked")}function appendAutoExpandStore(e){var t={},n=(t.lang_auto_expand=bg_i18n("lang_auto_expand"),pub_format(String(function(){
/*!
    <div class="auto-expand">
         <label>
             <input onclick='autoSales()' style="vertical-align:middle;margin:0;opacity: 1 !important;appearance:checkbox;"  type="checkbox" id="toggleSale" checked  >
              <span style="vertical-align:middle">#{lang_auto_expand}</span>
             </label>
    </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t));-1!==location.href.indexOf("aliexpress.ru/")?"store"==e?0<$(".RedShopSearch_RedShopSearch__root__waro8").length?$(".RedShopSearch_RedShopSearch__root__waro8").after(n):0<$("#hd").length&&0<$(".self-my-icon").length?($("#hd").append(n),$(".auto-expand").css("margin","0 auto"),$(".tongji").css("margin","0 auto")):0<$(".RedFilter_TopPanel__container__84ax4").length?$(".RedFilter_TopPanel__container__84ax4").after(n):0<$(".TjDataButton").length&&$(".TjDataButton").after(n):0<$(".TjDataButton").length?$(".TjDataButton").after(n):$(".narrow-down-bg").last().append(n):"store"==e&&0<$(".self-my-icon").length?(0<$(".TjDataButton").length?$(".TjDataButton").after(n):("hc"===$("#hd").prev("div").attr("id")?(n=pub_format(String(function(){
/*!
    <div class="auto-expand" style="display:inline-block">
         <label>
             <input onclick='autoSales()' style="vertical-align:middle;margin:0;opacity: 1 !important;appearance:checkbox;"  type="checkbox" id="toggleSale" checked  >
              <span style="vertical-align:middle">#{lang_auto_expand}</span>
             </label>
    </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t),$("#right").children("div").eq(0).children("div").eq(0)):$("#hd")).append(n),$(".auto-expand").css("margin","0 auto"),$(".tongji").css("margin","0 auto")):0<$(".narrow-down-bg").length?$(".narrow-down-bg").last().append(n):0<$(".TjDataButton").length&&$(".TjDataButton").after(n),1==localStorage.getItem("smt_sales_auto")?$("#toggleSale").attr("checked","true"):$("#toggleSale").removeAttr("checked")}
//用来判断是否包含某个值，只要包含就返回那个值，不包含就返回 -1
function checkClassIs(t,e){var e=globTjClass[e],n=-1;try{e.forEach(e=>{if(-1!=n)throw new Error("end check");-1!==e.indexOf("&")?e.split("&").forEach(e=>{n=-(n=1)!==t.indexOf(e)?1:-1}):-1!==t.indexOf(e)&&(n=e)})}catch(e){}return n}
//遍历产品包裹模块，获取dom 
function getProductDiv(){var e,t=localStorage.getItem("smt_config_tj_class"),t=(globTjClass=JSON.parse(t)).product_div,n=[];return t.forEach(e=>{var a,i,r,e=$(e);
// console.log(tempDom)
// var tempDom = $('.list--gallery--C2f2tvm')
0!=e.length&&(r=i=a="",e.each((e,t)=>{var n;""==a&&""!==(n=eachGetAttribute(t,"product_a_href"))&&(a=n),""==i&&""!==(n=eachGetAttribute(t,"shop_a_href"))&&(i=n),""==r&&""!==(n=getPosition(t,"store_product_order"))&&(r=n)}),location.href.indexOf("aliexpress.ru/"),""!=a&&-1!==a.indexOf("/item/")||""!=a&&-1!==a.indexOf("/item/")&&""!=r)&&!$(e[0]).hasClass("product-card")&&(n=0<n.length?{...n,...e}:e);
//查询是否有 产品的链接 和 店铺的链接
}),0===(n=0===n.length?e=$("#right").children("div").children("div").children("div").children("a").parent("div"):n).length&&(e=$("#bd").children("div").children("div").children("div").children("div").children("div").children("div").children('a[style*="position: relative; height: 100%;"]').parent("div"),n=e),n}
//遍历获取属性根据class
function eachGetAttribute(i,r,e=""){var t=localStorage.getItem("smt_config_tj_class"),t=(globTjClass=JSON.parse(t))[r],o="",l="";return t.forEach(e=>{var t="",n=(
//分割属性，判断得到的属性是什么
-1!==(e=e.replaceAll("/\\/","")).indexOf("|")&&(e=(n=e.split("|"))[0],t=n[1]),"");if("self"==e){if(null!=(n=$(i).attr(t))&&""!=n&&null!=n){if("shop_a_href"==r&&-1===n.indexOf("/store/"))return;if("product_a_href"==r&&-1===n.indexOf("/item/"))return;o=n}}else{var a=$(i).find(e);0!=a.length&&null!=(n=$(a[0]).attr(t))&&""!=n&&null!=n&&""==o&&(o=n,l=e)}}),""==o&&(l=""),"attr"==e?l:o}
//根据 href 获取 产品id
function getProductId(e){var t,n=e.split("/"),a="";for(t in n){var i=n[t];-1!==i.indexOf("html")&&(a=parseInt(i))}return a}
// 判断是否存在该 class 或者标志位
function getPosition(t,e){var e=globTjClass[e],n="";return e.forEach(e=>{0<$(t).find(e).length&&""==n&&(n=e)}),n}
//销量标红
function changeSoldCss(){var e=globTjClass.sold_change_red;null!=e&&e.forEach(e=>{var t,n=$(e);0!=n.length&&(n=$(n).text(),t=globTjClass.flag_sold_text,-1!==n.indexOf(t))&&($(e).css("color","red"),$(e).css("font-weight","bolder"))})}
//判断class 里有没有包含某个产品id,将id 提取出来
function judgeStoreProductId(e){if(""!=e&&null!=e){var t,n=e.split(" ");for(t in n){var a=n[t];if(-1!==a.indexOf("store_product"))return a.split("_")[2]}}return!1}
//向 content_script 通信
function injectSendParamsToContentScript(e,t=null,n="request",a="web_menu"){window.postMessage({type:a,data:t,cmd:n,chosed_item:e},"*")}
//翻页的时候移除 store_product
function removeSevenClassName(e){e=judgeStoreProductId(e);0!=e&&$(".store_product_"+e).removeClass("store_product_"+e)}
//产品列表根据runParams 获取 x_object_id
function dealWithProductIdByRunParamsById(e){try{var t=localStorage.getItem("run_params");if(null==t||""==t)return e;if(null==(t=JSON.parse(t))||""==t||null==t)return e;if(null==t.mods||""==t.mods||null==t.mods)return e;if(null==t.mods.itemList)return e;var n,a=t.mods.itemList.content,i=e;for(n in a){var r=a[n];if(i==r.productId)return r.trace.utLogMap.x_object_id}return newProductIds}catch(e){console.log("错误",e)}return e}setTimeout(()=>{appendTjTool()},3e3),setTimeout(()=>{appendSevenData()},3e3),localStorage.setItem("get_seven_data",0);