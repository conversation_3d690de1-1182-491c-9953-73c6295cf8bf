define(["./core","./core/access","./var/documentElement","./var/isFunction","./css/var/rnumnonpx","./css/curCSS","./css/addGetHookIf","./css/support","./var/isWindow","./core/init","./css","./selector"],function(a,o,e,p,s,n,f,i,c){"use strict";return a.offset={setOffset:function(t,e,o){var s,n,f,i,c=a.css(t,"position"),r=a(t),l={};
// Set position first, in-case top/left are set even on static elem
"static"===c&&(t.style.position="relative"),f=r.offset(),s=a.css(t,"top"),i=a.css(t,"left"),
// Need to be able to calculate position if either
// top or left is auto and position is either absolute or fixed
c=("absolute"===c||"fixed"===c)&&-1<(s+i).indexOf("auto")?(n=(c=r.position()).top,c.left):(n=parseFloat(s)||0,parseFloat(i)||0),null!=(
// Use jQuery.extend here to allow modification of coordinates argument (gh-1848)
e=p(e)?e.call(t,o,a.extend({},f)):e).top&&(l.top=e.top-f.top+n),null!=e.left&&(l.left=e.left-f.left+c),"using"in e?e.using.call(t,l):r.css(l)}},a.fn.extend({
// offset() relates an element's border box to the document origin
offset:function(e){
// Preserve chaining for setter
var t,o;return arguments.length?void 0===e?this:this.each(function(t){a.offset.setOffset(this,e,t)}):(o=this[0])?
// Return zeros for disconnected and hidden (display: none) elements (gh-2310)
// Support: IE <=11 only
// Running getBoundingClientRect on a
// disconnected node in IE throws an error
o.getClientRects().length?(
// Get document-relative position by adding viewport scroll to viewport-relative gBCR
t=o.getBoundingClientRect(),o=o.ownerDocument.defaultView,{top:t.top+o.pageYOffset,left:t.left+o.pageXOffset}):{top:0,left:0}:void 0},
// position() relates an element's margin box to its offset parent's padding box
// This corresponds to the behavior of CSS absolute positioning
position:function(){if(this[0]){var t,e,o,s=this[0],n={top:0,left:0};
// position:fixed elements are offset from the viewport, which itself always has zero offset
if("fixed"===a.css(s,"position"))
// Assume position:fixed implies availability of getBoundingClientRect
e=s.getBoundingClientRect();else{for(e=this.offset(),
// Account for the *real* offset parent, which can be the document or its root element
// when a statically positioned element is identified
o=s.ownerDocument,t=s.offsetParent||o.documentElement;t&&(t===o.body||t===o.documentElement)&&"static"===a.css(t,"position");)t=t.parentNode;t&&t!==s&&1===t.nodeType&&(
// Incorporate borders into its offset, since they are outside its content origin
(n=a(t).offset()).top+=a.css(t,"borderTopWidth",!0),n.left+=a.css(t,"borderLeftWidth",!0))}
// Subtract parent offsets and element margins
return{top:e.top-n.top-a.css(s,"marginTop",!0),left:e.left-n.left-a.css(s,"marginLeft",!0)}}},
// This method will return documentElement in the following cases:
// 1) For the element inside the iframe without offsetParent, this method will return
//    documentElement of the parent window
// 2) For the hidden or detached element
// 3) For body or html element, i.e. in case of the html node - it will return itself
//
// but those exceptions were never presented as a real life use-cases
// and might be considered as more preferable results.
//
// This logic, however, is not guaranteed and can change at any point in the future
offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===a.css(t,"position");)t=t.offsetParent;return t||e})}}),
// Create scrollLeft and scrollTop methods
a.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,n){var f="pageYOffset"===n;a.fn[e]=function(t){return o(this,function(t,e,o){
// Coalesce documents and windows
var s;if(c(t)?s=t:9===t.nodeType&&(s=t.defaultView),void 0===o)return s?s[n]:t[e];s?s.scrollTo(f?s.pageXOffset:o,f?o:s.pageYOffset):t[e]=o},e,t,arguments.length)}}),
// Support: Safari <=7 - 9.1, Chrome <=37 - 49
// Add the top/left cssHooks using jQuery.fn.position
// Webkit bug: https://bugs.webkit.org/show_bug.cgi?id=29084
// Blink bug: https://bugs.chromium.org/p/chromium/issues/detail?id=589347
// getComputedStyle returns percent when specified for top/left/bottom/right;
// rather than make the css module depend on the offset module, just check for it here
a.each(["top","left"],function(t,o){a.cssHooks[o]=f(i.pixelPosition,function(t,e){if(e)
// If curCSS returns percentage, fallback to offset
return e=n(t,o),s.test(e)?a(t).position()[o]+"px":e})}),a});