define(["../core","../core/isAttached","./var/rboxStyle","./var/rnumnonpx","./var/getStyles","./support"],function(h,o,s,a,u,l){"use strict";return function(t,e,i){var r,d,
// Support: Firefox 51+
// Retrieving style before computed somehow
// fixes an issue with getting wrong values
// on detached elements
n=t.style;
// getPropertyValue is needed for:
//   .css('filter') (IE 9 only, #12537)
//   .css('--customProperty) (#3144)
return(i=i||u(t))&&(""!==(d=i.getPropertyValue(e)||i[e])||o(t)||(d=h.style(t,e)),!l.pixelBoxStyles())&&a.test(d)&&s.test(e)&&(
// Remember the original values
t=n.width,e=n.minWidth,r=n.maxWidth,
// Put in the new values to get a computed value out
n.minWidth=n.maxWidth=n.width=d,d=i.width,
// Revert the changed values
n.width=t,n.minWidth=e,n.maxWidth=r),void 0!==d?
// Support: IE <=9 - 11 only
// IE returns zIndex value as an integer.
d+"":d}});