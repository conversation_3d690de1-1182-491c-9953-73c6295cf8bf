/*! jQuery v3.6.0 -ajax,-ajax/jsonp,-ajax/load,-ajax/script,-ajax/var/location,-ajax/var/nonce,-ajax/var/rquery,-ajax/xhr,-manipulation/_evalUrl,-deprecated/ajax-event-alias,-effects,-effects/Tween,-effects/animatedSelector | (c) OpenJS Foundation and other contributors | jquery.org/license */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)}("undefined"!=typeof window?window:this,function(g,I){"use strict";function v(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item}function m(e){return null!=e&&e===e.window}var t=[],R=Object.getPrototypeOf,s=t.slice,B=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},M=t.push,W=t.indexOf,F={},$=F.toString,z=F.hasOwnProperty,_=z.toString,U=_.call(Object),y={},b=g.document,V={type:!0,src:!0,nonce:!0,noModule:!0};function X(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in V)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function h(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?F[$.call(e)]||"object":typeof e}var e="3.6.0 -ajax,-ajax/jsonp,-ajax/load,-ajax/script,-ajax/var/location,-ajax/var/nonce,-ajax/var/rquery,-ajax/xhr,-manipulation/_evalUrl,-deprecated/ajax-event-alias,-effects,-effects/Tween,-effects/animatedSelector",x=function(e,t){return new x.fn.init(e,t)};function Q(e){var t=!!e&&"length"in e&&e.length,n=h(e);return!v(e)&&!m(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}x.fn=x.prototype={jquery:e,constructor:x,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=x.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return x.each(this,e)},map:function(n){return this.pushStack(x.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(x.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(x.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:M,sort:t.sort,splice:t.splice},x.extend=x.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},a=1,s=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[a]||{},a++),"object"==typeof o||v(o)||(o={}),a===s&&(o=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&o!==n&&(u&&n&&(x.isPlainObject(n)||(r=Array.isArray(n)))?(i=o[t],i=r&&!Array.isArray(i)?[]:r||x.isPlainObject(i)?i:{},r=!1,o[t]=x.extend(u,i,n)):void 0!==n&&(o[t]=n));return o},x.extend({expando:"jQuery"+(e+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==$.call(e)||(e=R(e))&&("function"!=typeof(e=z.call(e,"constructor")&&e.constructor)||_.call(e)!==U))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){X(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(Q(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},makeArray:function(e,t){t=t||[];return null!=e&&(Q(Object(e))?x.merge(t,"string"==typeof e?[e]:e):M.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:W.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!=a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,a=[];if(Q(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&a.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&a.push(i);return B(a)},guid:1,support:y}),"function"==typeof Symbol&&(x.fn[Symbol.iterator]=t[Symbol.iterator]),x.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){F["[object "+t+"]"]=t.toLowerCase()});function r(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&x(e).is(n))break;r.push(e)}return r}function Y(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var e=function(I){function f(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function R(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}function B(){C()}var e,d,x,o,M,p,W,F,w,u,l,C,T,n,E,h,r,i,g,A="sizzle"+ +new Date,c=I.document,N=0,$=0,z=j(),_=j(),U=j(),v=j(),V=function(e,t){return e===t&&(l=!0),0},X={}.hasOwnProperty,t=[],Q=t.pop,Y=t.push,S=t.push,G=t.slice,y=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},K="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",a="[\\x20\\t\\r\\n\\f]",s="(?:\\\\[\\da-fA-F]{1,6}"+a+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",J="\\["+a+"*("+s+")(?:"+a+"*([*^$|!~]?=)"+a+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+s+"))|)"+a+"*\\]",Z=":("+s+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+J+")*)|.*)\\)|)",ee=new RegExp(a+"+","g"),m=new RegExp("^"+a+"+|((?:^|[^\\\\])(?:\\\\.)*)"+a+"+$","g"),te=new RegExp("^"+a+"*,"+a+"*"),ne=new RegExp("^"+a+"*([>+~]|"+a+")"+a+"*"),re=new RegExp(a+"|>"),ie=new RegExp(Z),oe=new RegExp("^"+s+"$"),b={ID:new RegExp("^#("+s+")"),CLASS:new RegExp("^\\.("+s+")"),TAG:new RegExp("^("+s+"|[*])"),ATTR:new RegExp("^"+J),PSEUDO:new RegExp("^"+Z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+a+"*(even|odd|(([+-]|)(\\d*)n|)"+a+"*(?:([+-]|)"+a+"*(\\d+)|))"+a+"*\\)|)","i"),bool:new RegExp("^(?:"+K+")$","i"),needsContext:new RegExp("^"+a+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+a+"*((?:-\\d)?\\d*)"+a+"*\\)|)(?=[^-]|$)","i")},ae=/HTML$/i,se=/^(?:input|select|textarea|button)$/i,ue=/^h\d$/i,k=/^[^{]+\{\s*\[native \w/,le=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ce=/[+~]/,D=new RegExp("\\\\[\\da-fA-F]{1,6}"+a+"?|\\\\([^\\r\\n\\f])","g"),fe=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,de=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{S.apply(t=G.call(c.childNodes),c.childNodes),t[c.childNodes.length].nodeType}catch(e){S={apply:t.length?function(e,t){Y.apply(e,G.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function L(e,t,n,r){var i,o,a,s,u,l,c=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!r&&(C(t),t=t||T,E)){if(11!==f&&(s=le.exec(e)))if(i=s[1]){if(9===f){if(!(l=t.getElementById(i)))return n;if(l.id===i)return n.push(l),n}else if(c&&(l=c.getElementById(i))&&g(t,l)&&l.id===i)return n.push(l),n}else{if(s[2])return S.apply(n,t.getElementsByTagName(e)),n;if((i=s[3])&&d.getElementsByClassName&&t.getElementsByClassName)return S.apply(n,t.getElementsByClassName(i)),n}if(d.qsa&&!v[e+" "]&&(!h||!h.test(e))&&(1!==f||"object"!==t.nodeName.toLowerCase())){if(l=e,c=t,1===f&&(re.test(e)||ne.test(e))){for((c=ce.test(e)&&ve(t.parentNode)||t)===t&&d.scope||((a=t.getAttribute("id"))?a=a.replace(fe,R):t.setAttribute("id",a=A)),o=(u=p(e)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+H(u[o]);l=u.join(",")}try{return S.apply(n,c.querySelectorAll(l)),n}catch(t){v(e,!0)}finally{a===A&&t.removeAttribute("id")}}}return F(e.replace(m,"$1"),t,n,r)}function j(){var r=[];return function e(t,n){return r.push(t+" ")>x.cacheLength&&delete e[r.shift()],e[t+" "]=n}}function q(e){return e[A]=!0,e}function O(e){var t=T.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function pe(e,t){for(var n=e.split("|"),r=n.length;r--;)x.attrHandle[n[r]]=t}function he(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function ge(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&de(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function P(a){return q(function(o){return o=+o,q(function(e,t){for(var n,r=a([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function ve(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in d=L.support={},M=L.isXML=function(e){var t=e&&e.namespaceURI,e=e&&(e.ownerDocument||e).documentElement;return!ae.test(t||e&&e.nodeName||"HTML")},C=L.setDocument=function(e){var e=e?e.ownerDocument||e:c;return e!=T&&9===e.nodeType&&e.documentElement&&(n=(T=e).documentElement,E=!M(T),c!=T&&(e=T.defaultView)&&e.top!==e&&(e.addEventListener?e.addEventListener("unload",B,!1):e.attachEvent&&e.attachEvent("onunload",B)),d.scope=O(function(e){return n.appendChild(e).appendChild(T.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),d.attributes=O(function(e){return e.className="i",!e.getAttribute("className")}),d.getElementsByTagName=O(function(e){return e.appendChild(T.createComment("")),!e.getElementsByTagName("*").length}),d.getElementsByClassName=k.test(T.getElementsByClassName),d.getById=O(function(e){return n.appendChild(e).id=A,!T.getElementsByName||!T.getElementsByName(A).length}),d.getById?(x.filter.ID=function(e){var t=e.replace(D,f);return function(e){return e.getAttribute("id")===t}},x.find.ID=function(e,t){if(void 0!==t.getElementById&&E)return(t=t.getElementById(e))?[t]:[]}):(x.filter.ID=function(e){var t=e.replace(D,f);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},x.find.ID=function(e,t){if(void 0!==t.getElementById&&E){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),x.find.TAG=d.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):d.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},x.find.CLASS=d.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&E)return t.getElementsByClassName(e)},r=[],h=[],(d.qsa=k.test(T.querySelectorAll))&&(O(function(e){var t;n.appendChild(e).innerHTML="<a id='"+A+"'></a><select id='"+A+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&h.push("[*^$]="+a+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||h.push("\\["+a+"*(?:value|"+K+")"),e.querySelectorAll("[id~="+A+"-]").length||h.push("~="),(t=T.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||h.push("\\["+a+"*name"+a+"*="+a+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||h.push(":checked"),e.querySelectorAll("a#"+A+"+*").length||h.push(".#.+[+~]"),e.querySelectorAll("\\\f"),h.push("[\\r\\n\\f]")}),O(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=T.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&h.push("name"+a+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&h.push(":enabled",":disabled"),n.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),h.push(",.*:")})),(d.matchesSelector=k.test(i=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.oMatchesSelector||n.msMatchesSelector))&&O(function(e){d.disconnectedMatch=i.call(e,"*"),i.call(e,"[s!='']:x"),r.push("!=",Z)}),h=h.length&&new RegExp(h.join("|")),r=r.length&&new RegExp(r.join("|")),e=k.test(n.compareDocumentPosition),g=e||k.test(n.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},V=e?function(e,t){var n;return e===t?(l=!0,0):!e.compareDocumentPosition-!t.compareDocumentPosition||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?e==T||e.ownerDocument==c&&g(c,e)?-1:t==T||t.ownerDocument==c&&g(c,t)?1:u?y(u,e)-y(u,t):0:4&n?-1:1)}:function(e,t){if(e===t)return l=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!i||!o)return e==T?-1:t==T?1:i?-1:o?1:u?y(u,e)-y(u,t):0;if(i===o)return he(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[r]===s[r];)r++;return r?he(a[r],s[r]):a[r]==c?-1:s[r]==c?1:0}),T},L.matches=function(e,t){return L(e,null,null,t)},L.matchesSelector=function(e,t){if(C(e),d.matchesSelector&&E&&!v[t+" "]&&(!r||!r.test(t))&&(!h||!h.test(t)))try{var n=i.call(e,t);if(n||d.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){v(t,!0)}return 0<L(t,T,null,[e]).length},L.contains=function(e,t){return(e.ownerDocument||e)!=T&&C(e),g(e,t)},L.attr=function(e,t){(e.ownerDocument||e)!=T&&C(e);var n=x.attrHandle[t.toLowerCase()],n=n&&X.call(x.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==n?n:d.attributes||!E?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},L.escape=function(e){return(e+"").replace(fe,R)},L.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},L.uniqueSort=function(e){var t,n=[],r=0,i=0;if(l=!d.detectDuplicates,u=!d.sortStable&&e.slice(0),e.sort(V),l){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return u=null,e},o=L.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=o(t);return n},(x=L.selectors={cacheLength:50,createPseudo:q,match:b,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(D,f),e[3]=(e[3]||e[4]||e[5]||"").replace(D,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||L.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&L.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return b.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&ie.test(n)&&(t=(t=p(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(D,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=z[e+" "];return t||(t=new RegExp("(^|"+a+")"+e+"("+a+"|$)"))&&z(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=L.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(ee," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,g,v){var m="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===v?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,a,s,u,l=m!=y?"nextSibling":"previousSibling",c=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b,p=!1;if(c){if(m){for(;l;){for(a=e;a=a[l];)if(b?a.nodeName.toLowerCase()===f:1===a.nodeType)return!1;u=l="only"===h&&!u&&"nextSibling"}return!0}if(u=[y?c.firstChild:c.lastChild],y&&d){for(p=(s=(r=(i=(o=(a=c)[A]||(a[A]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===N&&r[1])&&r[2],a=s&&c.childNodes[s];a=++s&&a&&a[l]||(p=s=0,u.pop());)if(1===a.nodeType&&++p&&a===e){i[h]=[N,s,p];break}}else if(!1===(p=d?s=(r=(i=(o=(a=e)[A]||(a[A]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]||[])[0]===N&&r[1]:p))for(;(a=++s&&a&&a[l]||(p=s=0,u.pop()))&&((b?a.nodeName.toLowerCase()!==f:1!==a.nodeType)||!++p||(d&&((i=(o=a[A]||(a[A]={}))[a.uniqueID]||(o[a.uniqueID]={}))[h]=[N,p]),a!==e)););return(p-=v)===g||p%g==0&&0<=p/g}}},PSEUDO:function(e,o){var t,a=x.pseudos[e]||x.setFilters[e.toLowerCase()]||L.error("unsupported pseudo: "+e);return a[A]?a(o):1<a.length?(t=[e,e,"",o],x.setFilters.hasOwnProperty(e.toLowerCase())?q(function(e,t){for(var n,r=a(e,o),i=r.length;i--;)e[n=y(e,r[i])]=!(t[n]=r[i])}):function(e){return a(e,0,t)}):a}},pseudos:{not:q(function(e){var r=[],i=[],s=W(e.replace(m,"$1"));return s[A]?q(function(e,t,n,r){for(var i,o=s(e,null,r,[]),a=e.length;a--;)(i=o[a])&&(e[a]=!(t[a]=i))}):function(e,t,n){return r[0]=e,s(r,null,n,i),r[0]=null,!i.pop()}}),has:q(function(t){return function(e){return 0<L(t,e).length}}),contains:q(function(t){return t=t.replace(D,f),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:q(function(n){return oe.test(n||"")||L.error("unsupported lang: "+n),n=n.replace(D,f).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=I.location&&I.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===n},focus:function(e){return e===T.activeElement&&(!T.hasFocus||T.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!x.pseudos.empty(e)},header:function(e){return ue.test(e.nodeName)},input:function(e){return se.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:P(function(){return[0]}),last:P(function(e,t){return[t-1]}),eq:P(function(e,t,n){return[n<0?n+t:n]}),even:P(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:P(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:P(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:P(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=x.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})x.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function H(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ye(a,e,t){var s=e.dir,u=e.next,l=u||s,c=t&&"parentNode"===l,f=$++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||c)return a(e,t,n);return!1}:function(e,t,n){var r,i,o=[N,f];if(n){for(;e=e[s];)if((1===e.nodeType||c)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||c)if(i=(i=e[A]||(e[A]={}))[e.uniqueID]||(i[e.uniqueID]={}),u&&u===e.nodeName.toLowerCase())e=e[s]||e;else{if((r=i[l])&&r[0]===N&&r[1]===f)return o[2]=r[2];if((i[l]=o)[2]=a(e,t,n))return!0}return!1}}function be(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function xe(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)!(o=e[s])||n&&!n(o,r,i)||(a.push(o),l&&t.push(s));return a}function we(e){for(var r,t,n,i=e.length,o=x.relative[e[0].type],a=o||x.relative[" "],s=o?1:0,u=ye(function(e){return e===r},a,!0),l=ye(function(e){return-1<y(r,e)},a,!0),c=[function(e,t,n){e=!o&&(n||t!==w)||((r=t).nodeType?u:l)(e,t,n);return r=null,e}];s<i;s++)if(t=x.relative[e[s].type])c=[ye(be(c),t)];else{if((t=x.filter[e[s].type].apply(null,e[s].matches))[A]){for(n=++s;n<i&&!x.relative[e[n].type];n++);return function e(p,h,g,v,m,t){return v&&!v[A]&&(v=e(v)),m&&!m[A]&&(m=e(m,t)),q(function(e,t,n,r){var i,o,a,s=[],u=[],l=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)L(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),f=!p||!e&&h?c:xe(c,s,p,n,r),d=g?m||(e?p:l||v)?[]:t:f;if(g&&g(f,d,n,r),v)for(i=xe(d,u),v(i,[],n,r),o=i.length;o--;)(a=i[o])&&(d[u[o]]=!(f[u[o]]=a));if(e){if(m||p){if(m){for(i=[],o=d.length;o--;)(a=d[o])&&i.push(f[o]=a);m(null,d=[],i,r)}for(o=d.length;o--;)(a=d[o])&&-1<(i=m?y(e,a):s[o])&&(e[i]=!(t[i]=a))}}else d=xe(d===t?d.splice(l,d.length):d),m?m(null,t,d,r):S.apply(t,d)})}(1<s&&be(c),1<s&&H(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(m,"$1"),t,s<n&&we(e.slice(s,n)),n<i&&we(e=e.slice(n)),n<i&&H(e))}c.push(t)}return be(c)}return me.prototype=x.filters=x.pseudos,x.setFilters=new me,p=L.tokenize=function(e,t){var n,r,i,o,a,s,u,l=_[e+" "];if(l)return t?0:l.slice(0);for(a=e,s=[],u=x.preFilter;a;){for(o in n&&!(r=te.exec(a))||(r&&(a=a.slice(r[0].length)||a),s.push(i=[])),n=!1,(r=ne.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(m," ")}),a=a.slice(n.length)),x.filter)!(r=b[o].exec(a))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?L.error(e):_(e,s).slice(0)},W=L.compile=function(e,t){var n,v,m,y,b,r,i=[],o=[],a=U[e+" "];if(!a){for(n=(t=t||p(e)).length;n--;)((a=we(t[n]))[A]?i:o).push(a);(a=U(e,(y=0<(m=i).length,b=0<(v=o).length,r=function(e,t,n,r,i){var o,a,s,u=0,l="0",c=e&&[],f=[],d=w,p=e||b&&x.find.TAG("*",i),h=N+=null==d?1:Math.random()||.1,g=p.length;for(i&&(w=t==T||t||i);l!==g&&null!=(o=p[l]);l++){if(b&&o){for(a=0,t||o.ownerDocument==T||(C(o),n=!E);s=v[a++];)if(s(o,t||T,n)){r.push(o);break}i&&(N=h)}y&&((o=!s&&o)&&u--,e)&&c.push(o)}if(u+=l,y&&l!==u){for(a=0;s=m[a++];)s(c,f,t,n);if(e){if(0<u)for(;l--;)c[l]||f[l]||(f[l]=Q.call(r));f=xe(f)}S.apply(r,f),i&&!e&&0<f.length&&1<u+m.length&&L.uniqueSort(r)}return i&&(N=h,w=d),c},y?q(r):r))).selector=e}return a},F=L.select=function(e,t,n,r){var i,o,a,s,u,l="function"==typeof e&&e,c=!r&&p(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(a=o[0]).type&&9===t.nodeType&&E&&x.relative[o[1].type]){if(!(t=(x.find.ID(a.matches[0].replace(D,f),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=b.needsContext.test(e)?0:o.length;i--&&(a=o[i],!x.relative[s=a.type]);)if((u=x.find[s])&&(r=u(a.matches[0].replace(D,f),ce.test(o[0].type)&&ve(t.parentNode)||t))){if(o.splice(i,1),e=r.length&&H(o))break;return S.apply(n,r),n}}return(l||W(e,c))(r,t,!E,n,!t||ce.test(e)&&ve(t.parentNode)||t),n},d.sortStable=A.split("").sort(V).join("")===A,d.detectDuplicates=!!l,C(),d.sortDetached=O(function(e){return 1&e.compareDocumentPosition(T.createElement("fieldset"))}),O(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||pe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),d.attributes&&O(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||pe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),O(function(e){return null==e.getAttribute("disabled")})||pe(K,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),L}(g),G=(x.find=e,x.expr=e.selectors,x.expr[":"]=x.expr.pseudos,x.uniqueSort=x.unique=e.uniqueSort,x.text=e.getText,x.isXMLDoc=e.isXML,x.contains=e.contains,x.escapeSelector=e.escape,x.expr.match.needsContext);function u(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var K=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function J(e,n,r){return v(n)?x.grep(e,function(e,t){return!!n.call(e,t,e)!==r}):n.nodeType?x.grep(e,function(e){return e===n!==r}):"string"!=typeof n?x.grep(e,function(e){return-1<W.call(n,e)!==r}):x.filter(n,e,r)}x.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?x.find.matchesSelector(r,e)?[r]:[]:x.find.matches(e,x.grep(t,function(e){return 1===e.nodeType}))},x.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(x(e).filter(function(){for(t=0;t<r;t++)if(x.contains(i[t],this))return!0}));for(n=this.pushStack([]),t=0;t<r;t++)x.find(e,i[t],n);return 1<r?x.uniqueSort(n):n},filter:function(e){return this.pushStack(J(this,e||[],!1))},not:function(e){return this.pushStack(J(this,e||[],!0))},is:function(e){return!!J(this,"string"==typeof e&&G.test(e)?x(e):e||[],!1).length}});var Z,ee=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,te=((x.fn.init=function(e,t,n){if(e){if(n=n||Z,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):v(e)?void 0!==n.ready?n.ready(e):e(x):x.makeArray(e,this);if(!(r="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:ee.exec(e))||!r[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(r[1]){if(t=t instanceof x?t[0]:t,x.merge(this,x.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),K.test(r[1])&&x.isPlainObject(t))for(var r in t)v(this[r])?this[r](t[r]):this.attr(r,t[r])}else(n=b.getElementById(r[2]))&&(this[0]=n,this.length=1)}return this}).prototype=x.fn,Z=x(b),/^(?:parents|prev(?:Until|All))/),ne={children:!0,contents:!0,next:!0,prev:!0};function re(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}x.fn.extend({has:function(e){var t=x(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(x.contains(this,t[e]))return!0})},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&x(e);if(!G.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&x.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?x.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?W.call(x(e),this[0]):W.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(x.uniqueSort(x.merge(this.get(),x(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),x.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return r(e,"parentNode")},parentsUntil:function(e,t,n){return r(e,"parentNode",n)},next:function(e){return re(e,"nextSibling")},prev:function(e){return re(e,"previousSibling")},nextAll:function(e){return r(e,"nextSibling")},prevAll:function(e){return r(e,"previousSibling")},nextUntil:function(e,t,n){return r(e,"nextSibling",n)},prevUntil:function(e,t,n){return r(e,"previousSibling",n)},siblings:function(e){return Y((e.parentNode||{}).firstChild,e)},children:function(e){return Y(e.firstChild)},contents:function(e){return null!=e.contentDocument&&R(e.contentDocument)?e.contentDocument:(u(e,"template")&&(e=e.content||e),x.merge([],e.childNodes))}},function(r,i){x.fn[r]=function(e,t){var n=x.map(this,i,e);return(t="Until"!==r.slice(-5)?e:t)&&"string"==typeof t&&(n=x.filter(t,n)),1<this.length&&(ne[r]||x.uniqueSort(n),te.test(r))&&n.reverse(),this.pushStack(n)}});var w=/[^\x20\t\r\n\f]+/g;function c(e){return e}function ie(e){throw e}function oe(e,t,n,r){var i;try{e&&v(i=e.promise)?i.call(e).done(t).fail(n):e&&v(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}x.Callbacks=function(r){var e,n;r="string"==typeof r?(e=r,n={},x.each(e.match(w)||[],function(e,t){n[t]=!0}),n):x.extend({},r);function i(){for(s=s||r.once,a=o=!0;l.length;c=-1)for(t=l.shift();++c<u.length;)!1===u[c].apply(t[0],t[1])&&r.stopOnFalse&&(c=u.length,t=!1);r.memory||(t=!1),o=!1,s&&(u=t?[]:"")}var o,t,a,s,u=[],l=[],c=-1,f={add:function(){return u&&(t&&!o&&(c=u.length-1,l.push(t)),function n(e){x.each(e,function(e,t){v(t)?r.unique&&f.has(t)||u.push(t):t&&t.length&&"string"!==h(t)&&n(t)})}(arguments),t)&&!o&&i(),this},remove:function(){return x.each(arguments,function(e,t){for(var n;-1<(n=x.inArray(t,u,n));)u.splice(n,1),n<=c&&c--}),this},has:function(e){return e?-1<x.inArray(e,u):0<u.length},empty:function(){return u=u&&[],this},disable:function(){return s=l=[],u=t="",this},disabled:function(){return!u},lock:function(){return s=l=[],t||o||(u=t=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o)||i(),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!a}};return f},x.extend({Deferred:function(e){var o=[["notify","progress",x.Callbacks("memory"),x.Callbacks("memory"),2],["resolve","done",x.Callbacks("once memory"),x.Callbacks("once memory"),0,"resolved"],["reject","fail",x.Callbacks("once memory"),x.Callbacks("once memory"),1,"rejected"]],i="pending",a={state:function(){return i},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var i=arguments;return x.Deferred(function(r){x.each(o,function(e,t){var n=v(i[t[4]])&&i[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&v(e.promise)?e.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[t[0]+"With"](this,n?[e]:arguments)})}),i=null}).promise()},then:function(t,n,r){var u=0;function l(i,o,a,s){return function(){function e(){var e,t;if(!(i<u)){if((e=a.apply(n,r))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,v(t)?s?t.call(e,l(u,o,c,s),l(u,o,ie,s)):(u++,t.call(e,l(u,o,c,s),l(u,o,ie,s),l(u,o,c,o.notifyWith))):(a!==c&&(n=void 0,r=[e]),(s||o.resolveWith)(n,r))}}var n=this,r=arguments,t=s?e:function(){try{e()}catch(e){x.Deferred.exceptionHook&&x.Deferred.exceptionHook(e,t.stackTrace),u<=i+1&&(a!==ie&&(n=void 0,r=[e]),o.rejectWith(n,r))}};i?t():(x.Deferred.getStackHook&&(t.stackTrace=x.Deferred.getStackHook()),g.setTimeout(t))}}return x.Deferred(function(e){o[0][3].add(l(0,e,v(r)?r:c,e.notifyWith)),o[1][3].add(l(0,e,v(t)?t:c)),o[2][3].add(l(0,e,v(n)?n:ie))}).promise()},promise:function(e){return null!=e?x.extend(e,a):a}},s={};return x.each(o,function(e,t){var n=t[2],r=t[5];a[t[1]]=n.add,r&&n.add(function(){i=r},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){i[t]=this,o[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(i,o)}}var n=arguments.length,r=n,i=Array(r),o=s.call(arguments),a=x.Deferred();if(n<=1&&(oe(e,a.done(t(r)).resolve,a.reject,!n),"pending"===a.state()||v(o[r]&&o[r].then)))return a.then();for(;r--;)oe(o[r],t(r),a.reject);return a.promise()}});var ae=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,se=(x.Deferred.exceptionHook=function(e,t){g.console&&g.console.warn&&e&&ae.test(e.name)&&g.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},x.readyException=function(e){g.setTimeout(function(){throw e})},x.Deferred());function ue(){b.removeEventListener("DOMContentLoaded",ue),g.removeEventListener("load",ue),x.ready()}x.fn.ready=function(e){return se.then(e).catch(function(e){x.readyException(e)}),this},x.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--x.readyWait:x.isReady)||(x.isReady=!0)!==e&&0<--x.readyWait||se.resolveWith(b,[x])}}),x.ready.then=se.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?g.setTimeout(x.ready):(b.addEventListener("DOMContentLoaded",ue),g.addEventListener("load",ue));function f(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===h(n))for(s in i=!0,n)f(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,v(r)||(a=!0),t=l?a?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(x(e),n)}):t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o}var le=/^-ms-/,ce=/-([a-z])/g;function fe(e,t){return t.toUpperCase()}function d(e){return e.replace(le,"ms-").replace(ce,fe)}function C(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function n(){this.expando=x.expando+n.uid++}n.uid=1,n.prototype={cache:function(e){var t=e[this.expando];return t||(t={},C(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[d(t)]=n;else for(r in t)i[d(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][d(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(d):(t=d(t))in r?[t]:t.match(w)||[]).length;for(;n--;)delete r[t[n]]}void 0!==t&&!x.isEmptyObject(r)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!x.isEmptyObject(e)}};var T=new n,l=new n,de=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,pe=/[A-Z]/g;function he(e,t,n){var r,i;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(pe,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:de.test(i)?JSON.parse(i):i)}catch(e){}l.set(e,t,n)}else n=void 0;return n}x.extend({hasData:function(e){return l.hasData(e)||T.hasData(e)},data:function(e,t,n){return l.access(e,t,n)},removeData:function(e,t){l.remove(e,t)},_data:function(e,t,n){return T.access(e,t,n)},_removeData:function(e,t){T.remove(e,t)}}),x.fn.extend({data:function(n,e){var t,r,i,o=this[0],a=o&&o.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){l.set(this,n)}):f(this,function(e){var t;if(o&&void 0===e)return void 0!==(t=l.get(o,n))||void 0!==(t=he(o,n))?t:void 0;this.each(function(){l.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=l.get(o),1===o.nodeType)&&!T.get(o,"hasDataAttrs")){for(t=a.length;t--;)a[t]&&0===(r=a[t].name).indexOf("data-")&&(r=d(r.slice(5)),he(o,r,i[r]));T.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){l.remove(this,e)})}}),x.extend({queue:function(e,t,n){var r;if(e)return r=T.get(e,t=(t||"fx")+"queue"),n&&(!r||Array.isArray(n)?r=T.access(e,t,x.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=x.queue(e,t),r=n.length,i=n.shift(),o=x._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){x.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return T.get(e,n)||T.access(e,n,{empty:x.Callbacks("once memory").add(function(){T.remove(e,[t+"queue",n])})})}}),x.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?x.queue(this[0],t):void 0===n?this:this.each(function(){var e=x.queue(this,t,n);x._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&x.dequeue(this,t)})},dequeue:function(e){return this.each(function(){x.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(a,[a])}var r,i=1,o=x.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(r=T.get(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});function ge(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&A(e)&&"none"===x.css(e,"display")}var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ve=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),p=["Top","Right","Bottom","Left"],E=b.documentElement,A=function(e){return x.contains(e.ownerDocument,e)},me={composed:!0},ye=(E.getRootNode&&(A=function(e){return x.contains(e.ownerDocument,e)||e.getRootNode(me)===e.ownerDocument}),{});function be(e,t){for(var n,r,i,o,a,s,u=[],l=0,c=e.length;l<c;l++)(r=e[l]).style&&(n=r.style.display,t?("none"===n&&(u[l]=T.get(r,"display")||null,u[l]||(r.style.display="")),""===r.style.display&&ge(r)&&(u[l]=(s=o=i=void 0,o=r.ownerDocument,a=r.nodeName,(s=ye[a])||(i=o.body.appendChild(o.createElement(a)),s=x.css(i,"display"),i.parentNode.removeChild(i),ye[a]=s="none"===s?"block":s)))):"none"!==n&&(u[l]="none",T.set(r,"display",n)));for(l=0;l<c;l++)null!=u[l]&&(e[l].style.display=u[l]);return e}x.fn.extend({show:function(){return be(this,!0)},hide:function(){return be(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ge(this)?x(this).show():x(this).hide()})}});var N=/^(?:checkbox|radio)$/i,xe=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,we=/^$|^module$|\/(?:java|ecma)script/i,i=b.createDocumentFragment().appendChild(b.createElement("div")),S=((O=b.createElement("input")).setAttribute("type","radio"),O.setAttribute("checked","checked"),O.setAttribute("name","t"),i.appendChild(O),y.checkClone=i.cloneNode(!0).cloneNode(!0).lastChild.checked,i.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!i.cloneNode(!0).lastChild.defaultValue,i.innerHTML="<option></option>",y.option=!!i.lastChild,{thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]});function k(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&u(e,t)?x.merge([e],n):n}function Ce(e,t){for(var n=0,r=e.length;n<r;n++)T.set(e[n],"globalEval",!t||T.get(t[n],"globalEval"))}S.tbody=S.tfoot=S.colgroup=S.caption=S.thead,S.th=S.td,y.option||(S.optgroup=S.option=[1,"<select multiple='multiple'>","</select>"]);var Te=/<|&#?\w+;/;function Ee(e,t,n,r,i){for(var o,a,s,u,l,c=t.createDocumentFragment(),f=[],d=0,p=e.length;d<p;d++)if((o=e[d])||0===o)if("object"===h(o))x.merge(f,o.nodeType?[o]:o);else if(Te.test(o)){for(a=a||c.appendChild(t.createElement("div")),s=(xe.exec(o)||["",""])[1].toLowerCase(),s=S[s]||S._default,a.innerHTML=s[1]+x.htmlPrefilter(o)+s[2],l=s[0];l--;)a=a.lastChild;x.merge(f,a.childNodes),(a=c.firstChild).textContent=""}else f.push(t.createTextNode(o));for(c.textContent="",d=0;o=f[d++];)if(r&&-1<x.inArray(o,r))i&&i.push(o);else if(u=A(o),a=k(c.appendChild(o),"script"),u&&Ce(a),n)for(l=0;o=a[l++];)we.test(o.type||"")&&n.push(o);return c}var Ae=/^([^.]*)(?:\.(.+)|)/;function a(){return!0}function D(){return!1}function Ne(e,t){return e===function(){try{return b.activeElement}catch(e){}}()==("focus"===t)}function Se(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)Se(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=D;else if(!i)return e;return 1===o&&(a=i,(i=function(e){return x().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=x.guid++)),e.each(function(){x.event.add(this,t,i,r,n)})}function ke(e,i,o){o?(T.set(e,i,!1),x.event.add(e,i,{namespace:!1,handler:function(e){var t,n,r=T.get(this,i);if(1&e.isTrigger&&this[i]){if(r.length)(x.event.special[i]||{}).delegateType&&e.stopPropagation();else if(r=s.call(arguments),T.set(this,i,r),t=o(this,i),this[i](),r!==(n=T.get(this,i))||t?T.set(this,i,!1):n={},r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n&&n.value}else r.length&&(T.set(this,i,{value:x.event.trigger(x.extend(r[0],x.Event.prototype),r.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===T.get(e,i)&&x.event.add(e,i,a)}x.event={global:{},add:function(t,e,n,r,i){var o,a,s,u,l,c,f,d,p,h=T.get(t);if(C(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&x.find.matchesSelector(E,i),n.guid||(n.guid=x.guid++),s=(s=h.events)||(h.events=Object.create(null)),a=(a=h.handle)||(h.handle=function(e){return void 0!==x&&x.event.triggered!==e.type?x.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(w)||[""]).length;u--;)f=p=(d=Ae.exec(e[u])||[])[1],d=(d[2]||"").split(".").sort(),f&&(l=x.event.special[f]||{},f=(i?l.delegateType:l.bindType)||f,l=x.event.special[f]||{},p=x.extend({type:f,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&x.expr.match.needsContext.test(i),namespace:d.join(".")},o),(c=s[f])||((c=s[f]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(t,r,d,a))||t.addEventListener&&t.addEventListener(f,a),l.add&&(l.add.call(t,p),p.handler.guid||(p.handler.guid=n.guid)),i?c.splice(c.delegateCount++,0,p):c.push(p),x.event.global[f]=!0)},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,d,p,h,g,v=T.hasData(e)&&T.get(e);if(v&&(u=v.events)){for(l=(t=(t||"").match(w)||[""]).length;l--;)if(p=g=(s=Ae.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),p){for(f=x.event.special[p]||{},d=u[p=(r?f.delegateType:f.bindType)||p]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));a&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||x.removeEvent(e,p,v.handle),delete u[p])}else for(p in u)x.event.remove(e,p+t[l],n,r,!0);x.isEmptyObject(u)&&T.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a=new Array(arguments.length),s=x.event.fix(e),e=(T.get(this,"events")||Object.create(null))[s.type]||[],u=x.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,s)){for(o=x.event.handlers.call(this,s,e),t=0;(r=o[t++])&&!s.isPropagationStopped();)for(s.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==i.namespace&&!s.rnamespace.test(i.namespace)||(s.handleObj=i,s.data=i.data,void 0!==(i=((x.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(s.result=i)&&(s.preventDefault(),s.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&1<=e.button))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?-1<x(i,this).index(l):x.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(t,e){Object.defineProperty(x.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[x.expando]?e:new x.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return N.test(e.type)&&e.click&&u(e,"input")&&ke(e,"click",a),!1},trigger:function(e){e=this||e;return N.test(e.type)&&e.click&&u(e,"input")&&ke(e,"click"),!0},_default:function(e){e=e.target;return N.test(e.type)&&e.click&&u(e,"input")&&T.get(e,"click")||u(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},x.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},x.Event=function(e,t){if(!(this instanceof x.Event))return new x.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?a:D,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&x.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[x.expando]=!0},x.Event.prototype={constructor:x.Event,isDefaultPrevented:D,isPropagationStopped:D,isImmediatePropagationStopped:D,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=a,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=a,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=a,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},x.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},x.event.addProp),x.each({focus:"focusin",blur:"focusout"},function(e,t){x.event.special[e]={setup:function(){return ke(this,e,Ne),!1},trigger:function(){return ke(this,e),!0},_default:function(){return!0},delegateType:t}}),x.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){x.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||x.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),x.fn.extend({on:function(e,t,n,r){return Se(this,e,t,n,r)},one:function(e,t,n,r){return Se(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)r=e.handleObj,x(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=D),this.each(function(){x.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i])}return this}});var De=/<script|<style|<link/i,Le=/checked\s*(?:[^=]|=\s*.checked.)/i,je=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function qe(e,t){return u(e,"table")&&u(11!==t.nodeType?t:t.firstChild,"tr")&&x(e).children("tbody")[0]||e}function Oe(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Pe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function He(e,t){var n,r,i,o;if(1===t.nodeType){if(T.hasData(e)&&(o=T.get(e).events))for(i in T.remove(t,"handle events"),o)for(n=0,r=o[i].length;n<r;n++)x.event.add(t,i,o[i][n]);l.hasData(e)&&(e=l.access(e),e=x.extend({},e),l.set(t,e))}}function L(n,r,i,o){r=B(r);var e,t,a,s,u,l,c=0,f=n.length,d=f-1,p=r[0],h=v(p);if(h||1<f&&"string"==typeof p&&!y.checkClone&&Le.test(p))return n.each(function(e){var t=n.eq(e);h&&(r[0]=p.call(this,e,t.html())),L(t,r,i,o)});if(f&&(t=(e=Ee(r,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(s=(a=x.map(k(e,"script"),Oe)).length;c<f;c++)u=e,c!==d&&(u=x.clone(u,!0,!0),s)&&x.merge(a,k(u,"script")),i.call(n[c],u,c);if(s)for(l=a[a.length-1].ownerDocument,x.map(a,Pe),c=0;c<s;c++)u=a[c],we.test(u.type||"")&&!T.access(u,"globalEval")&&x.contains(l,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?x._evalUrl&&!u.noModule&&x._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},l):X(u.textContent.replace(je,""),u,l))}return n}function Ie(e,t,n){for(var r,i=t?x.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||x.cleanData(k(r)),r.parentNode&&(n&&A(r)&&Ce(k(r,"script")),r.parentNode.removeChild(r));return e}x.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s,u,l,c=e.cloneNode(!0),f=A(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||x.isXMLDoc(e)))for(a=k(c),r=0,i=(o=k(e)).length;r<i;r++)s=o[r],"input"===(l=(u=a[r]).nodeName.toLowerCase())&&N.test(s.type)?u.checked=s.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=s.defaultValue);if(t)if(n)for(o=o||k(e),a=a||k(c),r=0,i=o.length;r<i;r++)He(o[r],a[r]);else He(e,c);return 0<(a=k(c,"script")).length&&Ce(a,!f&&k(e,"script")),c},cleanData:function(e){for(var t,n,r,i=x.event.special,o=0;void 0!==(n=e[o]);o++)if(C(n)){if(t=n[T.expando]){if(t.events)for(r in t.events)i[r]?x.event.remove(n,r):x.removeEvent(n,r,t.handle);n[T.expando]=void 0}n[l.expando]&&(n[l.expando]=void 0)}}}),x.fn.extend({detach:function(e){return Ie(this,e,!0)},remove:function(e){return Ie(this,e)},text:function(e){return f(this,function(e){return void 0===e?x.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return L(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||qe(this,e).appendChild(e)})},prepend:function(){return L(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=qe(this,e)).insertBefore(e,t.firstChild)})},before:function(){return L(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return L(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(x.cleanData(k(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return x.clone(this,e,t)})},html:function(e){return f(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!De.test(e)&&!S[(xe.exec(e)||["",""])[1].toLowerCase()]){e=x.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(x.cleanData(k(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return L(this,arguments,function(e){var t=this.parentNode;x.inArray(this,n)<0&&(x.cleanData(k(this)),t)&&t.replaceChild(e,this)},n)}}),x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){x.fn[e]=function(e){for(var t,n=[],r=x(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),x(r[o])[a](t),M.apply(n,t.get());return this.pushStack(n)}});function Re(e){var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:g).getComputedStyle(e)}function Be(e,t,n){var r,i={};for(r in t)i[r]=e.style[r],e.style[r]=t[r];for(r in n=n.call(e),t)e.style[r]=i[r];return n}var Me,We,Fe,$e,ze,_e,Ue,o,Ve=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),Xe=new RegExp(p.join("|"),"i");function j(e,t,n){var r,i,o=e.style;return(n=n||Re(e))&&(""!==(i=n.getPropertyValue(t)||n[t])||A(e)||(i=x.style(e,t)),!y.pixelBoxStyles())&&Ve.test(i)&&Xe.test(t)&&(e=o.width,t=o.minWidth,r=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=e,o.minWidth=t,o.maxWidth=r),void 0!==i?i+"":i}function Qe(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function q(){var e;o&&(Ue.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",o.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",E.appendChild(Ue).appendChild(o),e=g.getComputedStyle(o),Me="1%"!==e.top,_e=12===Ye(e.marginLeft),o.style.right="60%",$e=36===Ye(e.right),We=36===Ye(e.width),o.style.position="absolute",Fe=12===Ye(o.offsetWidth/3),E.removeChild(Ue),o=null)}function Ye(e){return Math.round(parseFloat(e))}Ue=b.createElement("div"),(o=b.createElement("div")).style&&(o.style.backgroundClip="content-box",o.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===o.style.backgroundClip,x.extend(y,{boxSizingReliable:function(){return q(),We},pixelBoxStyles:function(){return q(),$e},pixelPosition:function(){return q(),Me},reliableMarginLeft:function(){return q(),_e},scrollboxSize:function(){return q(),Fe},reliableTrDimensions:function(){var e,t,n;return null==ze&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",E.appendChild(e).appendChild(t).appendChild(n),n=g.getComputedStyle(t),ze=parseInt(n.height,10)+parseInt(n.borderTopWidth,10)+parseInt(n.borderBottomWidth,10)===t.offsetHeight,E.removeChild(e)),ze}}));var Ge=["Webkit","Moz","ms"],Ke=b.createElement("div").style,Je={};function Ze(e){return x.cssProps[e]||Je[e]||(e in Ke?e:Je[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ge.length;n--;)if((e=Ge[n]+t)in Ke)return e}(e)||e)}var O,et=/^(none|table(?!-c[ea]).+)/,tt=/^--/,nt={position:"absolute",visibility:"hidden",display:"block"},rt={letterSpacing:"0",fontWeight:"400"};function it(e,t,n){var r=ve.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ot(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(u+=x.css(e,n+p[a],!0,i)),r?("content"===n&&(u-=x.css(e,"padding"+p[a],!0,i)),"margin"!==n&&(u-=x.css(e,"border"+p[a]+"Width",!0,i))):(u+=x.css(e,"padding"+p[a],!0,i),"padding"!==n?u+=x.css(e,"border"+p[a]+"Width",!0,i):s+=x.css(e,"border"+p[a]+"Width",!0,i));return!r&&0<=o&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u}function at(e,t,n){var r=Re(e),i=(!y.boxSizingReliable()||n)&&"border-box"===x.css(e,"boxSizing",!1,r),o=i,a=j(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ve.test(a)){if(!n)return a;a="auto"}return(!y.boxSizingReliable()&&i||!y.reliableTrDimensions()&&u(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===x.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===x.css(e,"boxSizing",!1,r),o=s in e)&&(a=e[s]),(a=parseFloat(a)||0)+ot(e,t,n||(i?"border":"content"),o,r,a)+"px"}x.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=j(e,"opacity"))?"1":t}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=d(t),u=tt.test(t),l=e.style;if(u||(t=Ze(s)),a=x.cssHooks[t]||x.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"==(o=typeof n)&&(i=ve.exec(n))&&i[1]&&(n=function(e,t,n){var r,i,o=20,a=function(){return x.css(e,t,"")},s=a(),u=n&&n[3]||(x.cssNumber[t]?"":"px"),l=e.nodeType&&(x.cssNumber[t]||"px"!==u&&+s)&&ve.exec(x.css(e,t));if(l&&l[3]!==u){for(u=u||l[3],l=+(s/=2)||1;o--;)x.style(e,t,l+u),(1-i)*(1-(i=a()/s||.5))<=0&&(o=0),l/=i;x.style(e,t,(l*=2)+u),n=n||[]}return n&&(l=+l||+s||0,r=n[1]?l+(n[1]+1)*n[2]:+n[2]),r}(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(x.cssNumber[s]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o=d(t);return tt.test(t)||(t=Ze(o)),"normal"===(i=void 0===(i=(o=x.cssHooks[t]||x.cssHooks[o])&&"get"in o?o.get(e,!0,n):i)?j(e,t,r):i)&&t in rt&&(i=rt[t]),(""===n||n)&&(o=parseFloat(i),!0===n||isFinite(o))?o||0:i}}),x.each(["height","width"],function(e,a){x.cssHooks[a]={get:function(e,t,n){if(t)return!et.test(x.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?at(e,a,n):Be(e,nt,function(){return at(e,a,n)})},set:function(e,t,n){var r=Re(e),i=!y.scrollboxSize()&&"absolute"===r.position,o=(i||n)&&"border-box"===x.css(e,"boxSizing",!1,r),n=n?ot(e,a,n,o,r):0;return o&&i&&(n-=Math.ceil(e["offset"+a[0].toUpperCase()+a.slice(1)]-parseFloat(r[a])-ot(e,a,"border",!1,r)-.5)),n&&(o=ve.exec(t))&&"px"!==(o[3]||"px")&&(e.style[a]=t,t=x.css(e,a)),it(0,t,n)}}}),x.cssHooks.marginLeft=Qe(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(j(e,"marginLeft"))||e.getBoundingClientRect().left-Be(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),x.each({margin:"",padding:"",border:"Width"},function(i,o){x.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+p[t]+o]=r[t]||r[t-2]||r[0];return n}},"margin"!==i&&(x.cssHooks[i+o].set=it)}),x.fn.extend({css:function(e,t){return f(this,function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Re(e),i=t.length;a<i;a++)o[t[a]]=x.css(e,t[a],!1,r);return o}return void 0!==n?x.style(e,t,n):x.css(e,t)},e,t,1<arguments.length)}}),x.fn.delay=function(r,e){return r=x.fx&&x.fx.speeds[r]||r,this.queue(e=e||"fx",function(e,t){var n=g.setTimeout(e,r);t.stop=function(){g.clearTimeout(n)}})},O=b.createElement("input"),i=b.createElement("select").appendChild(b.createElement("option")),O.type="checkbox",y.checkOn=""!==O.value,y.optSelected=i.selected,(O=b.createElement("input")).value="t",O.type="radio",y.radioValue="t"===O.value;var st,ut=x.expr.attrHandle,lt=(x.fn.extend({attr:function(e,t){return f(this,x.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){x.removeAttr(this,e)})}}),x.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?x.prop(e,t,n):(1===o&&x.isXMLDoc(e)||(i=x.attrHooks[t.toLowerCase()]||(x.expr.match.bool.test(t)?st:void 0)),void 0!==n?null===n?void x.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):!(i&&"get"in i&&null!==(r=i.get(e,t)))&&null==(r=x.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){var n;if(!y.radioValue&&"radio"===t&&u(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(w);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),st={set:function(e,t,n){return!1===t?x.removeAttr(e,n):e.setAttribute(n,n),n}},x.each(x.expr.match.bool.source.match(/\w+/g),function(e,t){var a=ut[t]||x.find.attr;ut[t]=function(e,t,n){var r,i,o=t.toLowerCase();return n||(i=ut[o],ut[o]=r,r=null!=a(e,t,n)?o:null,ut[o]=i),r}}),/^(?:input|select|textarea|button)$/i),ct=/^(?:a|area)$/i;function P(e){return(e.match(w)||[]).join(" ")}function H(e){return e.getAttribute&&e.getAttribute("class")||""}function ft(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(w)||[]}x.fn.extend({prop:function(e,t){return f(this,x.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[x.propFix[e]||e]})}}),x.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&x.isXMLDoc(e)||(t=x.propFix[t]||t,i=x.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=x.find.attr(e,"tabindex");return t?parseInt(t,10):lt.test(e.nodeName)||ct.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(x.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){x.propFix[this.toLowerCase()]=this}),x.fn.extend({addClass:function(t){var e,n,r,i,o,a,s=0;if(v(t))return this.each(function(e){x(this).addClass(t.call(this,e,H(this)))});if((e=ft(t)).length)for(;n=this[s++];)if(a=H(n),r=1===n.nodeType&&" "+P(a)+" "){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");a!==(a=P(r))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,r,i,o,a,s=0;if(v(t))return this.each(function(e){x(this).removeClass(t.call(this,e,H(this)))});if(!arguments.length)return this.attr("class","");if((e=ft(t)).length)for(;n=this[s++];)if(a=H(n),r=1===n.nodeType&&" "+P(a)+" "){for(o=0;i=e[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");a!==(a=P(r))&&n.setAttribute("class",a)}return this},toggleClass:function(i,t){var o=typeof i,a="string"==o||Array.isArray(i);return"boolean"==typeof t&&a?t?this.addClass(i):this.removeClass(i):v(i)?this.each(function(e){x(this).toggleClass(i.call(this,e,H(this),t),t)}):this.each(function(){var e,t,n,r;if(a)for(t=0,n=x(this),r=ft(i);e=r[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==i&&"boolean"!=o||((e=H(this))&&T.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==i&&T.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,r=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+P(H(t))+" ").indexOf(r))return!0;return!1}});function dt(e){e.stopPropagation()}var pt=/\r/g,ht=(x.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=v(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,x(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=x.map(e,function(e){return null==e?"":e+""})),(n=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):i?(n=x.valHooks[i.type]||x.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(pt,""):null==e?"":e:void 0}}),x.extend({valHooks:{option:{get:function(e){var t=x.find.attr(e,"value");return null!=t?t:P(x.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type,o=i?null:[],a=i?r+1:n.length,s=r<0?a:i?r:0;s<a;s++)if(((t=n[s]).selected||s===r)&&!t.disabled&&(!t.parentNode.disabled||!u(t.parentNode,"optgroup"))){if(t=x(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=x.makeArray(t),a=i.length;a--;)((r=i[a]).selected=-1<x.inArray(x.valHooks.option.get(r),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),x.each(["radio","checkbox"],function(){x.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<x.inArray(x(e).val(),t)}},y.checkOn||(x.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in g,/^(?:focusinfocus|focusoutblur)$/);x.extend(x.event,{trigger:function(e,t,n,r){var i,o,a,s,u,l,c,f=[n||b],d=z.call(e,"type")?e.type:e,p=z.call(e,"namespace")?e.namespace.split("."):[],h=c=o=n=n||b;if(3!==n.nodeType&&8!==n.nodeType&&!ht.test(d+x.event.triggered)&&(-1<d.indexOf(".")&&(d=(p=d.split(".")).shift(),p.sort()),s=d.indexOf(":")<0&&"on"+d,(e=e[x.expando]?e:new x.Event(d,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:x.makeArray(t,[e]),l=x.event.special[d]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){if(!r&&!l.noBubble&&!m(n)){for(a=l.delegateType||d,ht.test(a+d)||(h=h.parentNode);h;h=h.parentNode)f.push(h),o=h;o===(n.ownerDocument||b)&&f.push(o.defaultView||o.parentWindow||g)}for(i=0;(h=f[i++])&&!e.isPropagationStopped();)c=h,e.type=1<i?a:l.bindType||d,(u=(T.get(h,"events")||Object.create(null))[e.type]&&T.get(h,"handle"))&&u.apply(h,t),(u=s&&h[s])&&u.apply&&C(h)&&(e.result=u.apply(h,t),!1===e.result)&&e.preventDefault();return e.type=d,r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(f.pop(),t)||!C(n)||s&&v(n[d])&&!m(n)&&((o=n[s])&&(n[s]=null),x.event.triggered=d,e.isPropagationStopped()&&c.addEventListener(d,dt),n[d](),e.isPropagationStopped()&&c.removeEventListener(d,dt),x.event.triggered=void 0,o)&&(n[s]=o),e.result}},simulate:function(e,t,n){n=x.extend(new x.Event,n,{type:e,isSimulated:!0});x.event.trigger(n,null,t)}}),x.fn.extend({trigger:function(e,t){return this.each(function(){x.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return x.event.trigger(e,t,n,!0)}}),y.focusin||x.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){x.event.simulate(r,e.target,x.event.fix(e))}x.event.special[r]={setup:function(){var e=this.ownerDocument||this.document||this,t=T.access(e,r);t||e.addEventListener(n,i,!0),T.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=T.access(e,r)-1;t?T.access(e,r,t):(e.removeEventListener(n,i,!0),T.remove(e,r))}}}),x.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new g.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||x.error("Invalid XML: "+(n?x.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var gt=/\[\]$/,vt=/\r?\n/g,mt=/^(?:submit|button|image|reset|file)$/i,yt=/^(?:input|select|textarea|keygen)/i;x.param=function(e,t){function n(e,t){t=v(t)?t():t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var r,i=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!x.isPlainObject(e))x.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(Array.isArray(e))x.each(e,function(e,t){i||gt.test(r)?o(r,t):n(r+"["+("object"==typeof t&&null!=t?e:"")+"]",t,i,o)});else if(i||"object"!==h(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&")},x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=x.prop(this,"elements");return e?x.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!x(this).is(":disabled")&&yt.test(this.nodeName)&&!mt.test(e)&&(this.checked||!N.test(e))}).map(function(e,t){var n=x(this).val();return null==n?null:Array.isArray(n)?x.map(n,function(e){return{name:t.name,value:e.replace(vt,"\r\n")}}):{name:t.name,value:n.replace(vt,"\r\n")}}).get()}}),x.fn.extend({wrapAll:function(e){return this[0]&&(v(e)&&(e=e.call(this[0])),e=x(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return v(n)?this.each(function(e){x(this).wrapInner(n.call(this,e))}):this.each(function(){var e=x(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=v(t);return this.each(function(e){x(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){x(this).replaceWith(this.childNodes)}),this}}),x.expr.pseudos.hidden=function(e){return!x.expr.pseudos.visible(e)},x.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},y.createHTMLDocument=((e=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),x.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),r=!n&&[],(n=K.exec(e))?[t.createElement(n[1])]:(n=Ee([e],t,r),r&&r.length&&x(r).remove(),x.merge([],n.childNodes)));var r},x.offset={setOffset:function(e,t,n){var r,i,o,a,s=x.css(e,"position"),u=x(e),l={};"static"===s&&(e.style.position="relative"),o=u.offset(),r=x.css(e,"top"),a=x.css(e,"left"),s=("absolute"===s||"fixed"===s)&&-1<(r+a).indexOf("auto")?(i=(s=u.position()).top,s.left):(i=parseFloat(r)||0,parseFloat(a)||0),null!=(t=v(t)?t.call(e,n,x.extend({},o)):t).top&&(l.top=t.top-o.top+i),null!=t.left&&(l.left=t.left-o.left+s),"using"in t?t.using.call(e,l):u.css(l)}},x.fn.extend({offset:function(t){var e,n;return arguments.length?void 0===t?this:this.each(function(e){x.offset.setOffset(this,t,e)}):(n=this[0])?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===x.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===x.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=x(e).offset()).top+=x.css(e,"borderTopWidth",!0),i.left+=x.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-x.css(r,"marginTop",!0),left:t.left-i.left-x.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===x.css(e,"position");)e=e.offsetParent;return e||E})}}),x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;x.fn[t]=function(e){return f(this,function(e,t,n){var r;if(m(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?r.pageXOffset:n,o?n:r.pageYOffset):e[t]=n},t,e,arguments.length)}}),x.each(["top","left"],function(e,n){x.cssHooks[n]=Qe(y.pixelPosition,function(e,t){if(t)return t=j(e,n),Ve.test(t)?x(e).position()[n]+"px":t})}),x.each({Height:"height",Width:"width"},function(a,s){x.each({padding:"inner"+a,content:s,"":"outer"+a},function(r,o){x.fn[o]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return f(this,function(e,t,n){var r;return m(e)?0===o.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+a],r["scroll"+a],e.body["offset"+a],r["offset"+a],r["client"+a])):void 0===n?x.css(e,t,i):x.style(e,t,n,i)},s,n?e:void 0,n)}})}),x.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),x.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){x.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var bt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,xt=(x.proxy=function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),v(e))return n=s.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||x.guid++,r},x.holdReady=function(e){e?x.readyWait++:x.ready(!0)},x.isArray=Array.isArray,x.parseJSON=JSON.parse,x.nodeName=u,x.isFunction=v,x.isWindow=m,x.camelCase=d,x.type=h,x.now=Date.now,x.isNumeric=function(e){var t=x.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},x.trim=function(e){return null==e?"":(e+"").replace(bt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return x}),g.jQuery),wt=g.$;return x.noConflict=function(e){return g.$===x&&(g.$=wt),e&&g.jQuery===x&&(g.jQuery=xt),x},void 0===I&&(g.jQuery=g.$=x),x});