define(["../core","../core/toType","../core/isAttached","./var/rtagName","./var/rscriptType","./wrapMap","./getAll","./setGlobalEval"],function(u,m,C,g,v,y,T,x){"use strict";var w=/<|&#?\w+;/;return function(e,t,r,i,a){for(var n,o,l,s,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((n=e[d])||0===n)
// Add nodes directly
if("object"===m(n))
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
u.merge(p,n.nodeType?[n]:n);
// Convert non-html into a text node
else if(w.test(n)){for(o=o||f.appendChild(t.createElement("div")),
// Deserialize a standard representation
l=(g.exec(n)||["",""])[1].toLowerCase(),l=y[l]||y._default,o.innerHTML=l[1]+u.htmlPrefilter(n)+l[2],
// Descend through wrappers to the right content
c=l[0];c--;)o=o.lastChild;
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
u.merge(p,o.childNodes),
// Ensure the created nodes are orphaned (#12392)
(
// Remember the top-level container
o=f.firstChild).textContent=""}else p.push(t.createTextNode(n));
// Convert html into DOM nodes
// Remove wrapper from fragment
for(f.textContent="",d=0;n=p[d++];)
// Skip elements already in the context collection (trac-4087)
if(i&&-1<u.inArray(n,i))a&&a.push(n);else
// Capture executables
if(s=C(n),
// Append to fragment
o=T(f.appendChild(n),"script"),
// Preserve script evaluation history
s&&x(o),r)for(c=0;n=o[c++];)v.test(n.type||"")&&r.push(n);return f}});