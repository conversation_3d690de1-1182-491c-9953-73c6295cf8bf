//改文件功能： 负责 页面  和 背景页的通信
//2. content_script 向 background 发送信息
function contentScriptToBackground(e="request",t={},o,c="content_script"){chrome.runtime.sendMessage({type:c,cmd:e,objData:t},o)}
//3. 向background 获取是否有登陆
function checkIsLogin(){contentScriptToBackground(cmd="check_login","",call_isLogin)}
//4. 获取当前页面的id
function getProductsId(){var e,t=window.location.pathname.split("/");for(e in t){var o=t[e];if(-1!==o.indexOf("html"))return parseInt(o)}return!1}
//5. 获取当前产品的店铺id 
function getStoreId(){var e,t,o=window.location.pathname;return-1!==o.indexOf("/store/")?(t="",-1!==(e=(e=(o=null!=document.querySelector('link[rel="canonical"]')&&null!=(e=document.querySelector('link[rel="canonical"]').getAttribute("href"))&&""!=e?e:o).split("/"))[e.length-1]).indexOf(".html")?t=-1===e.indexOf("_")?parseInt(e):e.split("_")[0]:isNaN(e)||(t=e),{id:t=("number"!=typeof t||isNaN(t))&&0<(t=o.match(/\d+/g)).length?t[0]:t,is_index:1}):null==(t=$("#smt-store-id").val())||""==t||null==t?(t=!1,{id:"xxx",is_index:0}):{id:t,is_index:0}}
// 6.接收 background / content_script 通信
//7.收集要下载的图片的地址
function collectSourceUrls(e,t="image"){var o=[];switch(e){case"images-view-list":$(".images-view-list img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)});break;case"slider--item--FefNjlj":$(".slider--item--FefNjlj img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)});break;case"slider--item--RpyeewA":$(".slider--item--RpyeewA img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)});break;case"sku-property-list":$(".sku-property-image img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)});break;case"over_view":$(".tab-content div:first img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)});break;case"video-wrap":o=$(".image-viewer video").attr("src");break;case"Product_GalleryBarItem__barItem__11qng":var c=$($("figure").next()[0]).next()[0];$(c).find("img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)});break;default:"image"==t?$(e).each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&o.push(e)}):o=$(e).attr("src")}return o}ixRunParams="",
//1. 接收 inject 发送来的通信
window.addEventListener("message",function(e){var e=e.data,t=e.type,o=e.cmd,c=e.data,e=e.chosed_item;// 判断是选中哪个菜单栏
if("web_menu"===t)
// console.log(chosedItem,1233)
if("more"==e)createmoreDom();else{if("store"!=e){if(0==(n=getProductsId())&&"my_favted"!==e&&"my_persion"!==e)return void createNoInfoTipDom();var r=0,c={id:n};0<$("[rel='canonical']").length&&(a=$("[rel='canonical']").attr("href"),r=getProductId(a),c.x_object_id=r)}switch(createLoadingDom(),e){case"my_persion":contentScriptToBackground("persion",{},showMyPersion);break;case"my_favted":contentScriptToBackground("get_my_favted",{type:"product"},createfavtedDom);break;case"product":
//追加销量数据
c.sale=0,contentScriptToBackground("get_class_config",{},function(e){e=e.config_tj_class;c.sale=0,e.detail_sale_class?0<$(e.detail_sale_class).length&&(c.sale=$(e.detail_sale_class).text().split(" ")[0]):0<$(".reviewer--sold--ytPeoEy").length&&(c.sale=$(".reviewer--sold--ytPeoEy").text().split(" ")[0]),contentScriptToBackground("request_product",c,createproductDom)});break;case"store":var a,n,r=0;c={id:n},0<$("[rel='canonical']").length&&(a=$("[rel='canonical']").attr("href"),r=getProductId(a)),//{store_id:id,product_id:getProductsId()}
(
// if(id == false){
//     createNoInfoTipDom()
//     return
// }
c=n=getStoreId("")).product_id=getProductsId(),c.x_object_id=r,contentScriptToBackground("request_store",c,createstoreDom);break;case"img":contentScriptToBackground("request_img",c,createimgDom);break;case"chart":contentScriptToBackground("request_chart",c,createchartDom)}}else if("web_event"===t)switch(o){case"set_run_Params":ixRunParams=c;break;
//上报产品id关系
case"upload_product_id":contentScriptToBackground("upload_product_id",c,function(){});break;
//上报店铺的id关系
case"upload_store_id":contentScriptToBackground("upload_store_id",c,function(){});break;case"search_by_img":contentScriptToBackground("search_by_img",c,callSearchByImg);break;
//将产品收集产品运费价格的任务传到后端
case"prdocut_carrier_api":createCarrierFeeLoadingDom(),contentScriptToBackground("prdocut_carrier_api",c,callProductCarrier);break;
//展开产品相关词流量步骤图
case"show_kwds_pic":showKeywordsPic();break;case"get_keywords_info"://获得关键词信息
var s=localStorage.getItem("site_country"),i=[];if(c=localStorage.getItem("long_words_like"))
// console.log(objData)
if(c=JSON.parse(c),"RU"==s)for(var d in c)i.push(c[d]);else for(var d in c)for(var u in c[d])i.push(c[d][u].keywords);i&&((_={}).keyword=i,_.country=s,contentScriptToBackground("keyword_info_api",_,callKeywordInfo));break;case"set_site_country"://设置速卖通的国家
localStorage.setItem("site_country",c),contentScriptToBackground("set_site_country",c,function(e){});break;case"add_log":contentScriptToBackground("add_log",c,function(e){});break;case"more_long_words"://再次请求长尾词接口
createInputLoadingDom(),contentScriptToBackground("more_long_words",{},moreLongWordsShow);break;case"chang_aliexpress_com":changeAliexpressCom();break;case"get_long_words":contentScriptToBackground("get_long_words",c,showMyLongWords);break;
// case 'long_words_add':
//     contentScriptToBackground('long_words_add',objData,longWordsAddback)
//     break;
case"long_words_url"://请求长尾词接口
createInputLoadingDom(),contentScriptToBackground("long_words_url",c,longWordsSet);break;case"get_set_source":contentScriptToBackground("get_set_source",{},function(e){$("body").append("<input type='hidden' class='chrome_source_id' value='"+e+"'>"),localStorage.setItem("set_source",e)});break;case"check_monitor":createTjLoadingDom(),contentScriptToBackground("check_monitor",c,showMonitorData);break;case"get_my_favted":createLoadingDom("new"),$(".my-favted-ul").remove(),contentScriptToBackground("get_my_favted",c,createfavtedDom);break;case"show_same_product":showSameProductDom();break;case"request_favted_store":createTjLoadingDom(),contentScriptToBackground("collection_store",c,call_collectionStore);break;case"isLoginrequest_seven_data_todo":contentScriptToBackground("isLoginrequest_seven_data_todo",{});break;case"isLoginrequest_seven_data":contentScriptToBackground("isLoginrequest_seven_data",{},isLoginrequest_seven_data);break;case"request_seven_data":contentScriptToBackground("request_seven_data",c,showSevenData);break;case"show_small_chart_more":showSmallChartMore(c);break;case"get_class_config":contentScriptToBackground("get_class_config",c,function(e){var t=e.config_class,e=e.config_tj_class;localStorage.setItem("smt_config_class",JSON.stringify(t)),localStorage.setItem("smt_config_tj_class",JSON.stringify(e))});break;case"need_chose_countrys":alertInfo(bg_i18n("need_chose_countrys"),5e3);break;case"append_country_k":contentScriptToBackground("append_ocuntry_chose_k",{},appendCountryChosePrice);break;case"append_collect_process":appendCollectProcess(c);break;case"set_chose_countrys":contentScriptToBackground("set_chose_countrys",c,function(){});break;case"get_show_auto":contentScriptToBackground("get_show_auto",c,function(e){2!=e&&(e=1),localStorage.setItem("show_auto",e)});break;case"open_collection_tip":return void alertInfo(bg_i18n("lang_begin_collection_tip"),8e3);case"check_country_price":contentScriptToBackground("check_country_price",c,checkPermissionCountryPrice);break;case"set_lang":timerI18nTT();break;case"set_smt_country":contentScriptToBackground("set_smt_country",c,setSmtCountry);break;case"request_get_favted_ids":contentScriptToBackground("request_get_favted_ids",{},alreadyFavtedIds);break;case"request_version":contentScriptToBackground("request_version",{},function(e){localStorage.setItem("extention_version",e)});break;case"request_favted_favted":createTjLoadingDom(),contentScriptToBackground("glob_get_tags",c,favtedProductCallBack);break;case"request_tj_chart":createTjLoadingDom(),c.site="",-1!==location.href.indexOf("aliexpress.ru/")&&(c.site="ru"),contentScriptToBackground("request_tj_chart",c,tjProductData);break;case"request_product_chart":
// $('.hide-title-info').html(bg_i18n('lang_price'))
createSmallLoadingDom("small-chart-"+c.product_id),contentScriptToBackground("request_product_chart",c,creatSmallChart);break;case"request_year_chart":contentScriptToBackground("request_year_chart",c,updateYearChart);break;case"show_auto":s=c.flag;localStorage.setItem("show_auto",s),contentScriptToBackground("set_show_auto",c,function(){});break;case"request_shopify_goods":// 获取shopify 竞品数据及对应的DOM 渲染
contentScriptToBackground("request_shopify_goods",c,createdShopifyGoodsInfoDom);break;case"collection_goods":createTjLoadingDom(),contentScriptToBackground("collection_goods",c,call_collectionGoods);break;case"show_collection_box":createTjLoadingDom(),contentScriptToBackground("glob_get_tags",c,show_collection_box);break;case"show_collection_box_store":createTjLoadingDom(),contentScriptToBackground("glob_get_tags",c,show_collection_box_store);break;case"draw_product_chart":productChartSwitch(c);break;case"draw_store_chart":storeChartSwitch(c);break;case"append_down_button":-1!==location.href.indexOf("aliexpress.ru/")?(
// ru 域名下的下载按钮
// setTimeout(function () { PyccknnDownload() }, 2000)
// setTimeout(function () { createDownloadButtonDom() }, 2000)
setTimeout(function(){contentScriptToBackground("create_download_button_dom",{},createDownloadButtonDom)},2e3),setTimeout(function(){createSearchImgButtonDomRu()},2e3)):0==$("figure").length?(
//com 域名下的下载
setTimeout(function(){contentScriptToBackground("create_download_button_dom",{},createDownloadButtonDom)},2e3),
//插入以图搜图的入口
setTimeout(function(){createSearchImgButtonDom()},2e3)):(setTimeout(function(){PyccknnDownload()},2e3),setTimeout(function(){createSearchImgButtonDomRu()},2e3));break;case"sources_download":var _=bg_i18n("lang_wait_download");alertInfo(_,6e3),contentScriptToBackground("sources_download",c,downloadSources);break;case"need_update_close":contentScriptToBackground("need_update_close",{},function(e){});break;case"self_check_update":contentScriptToBackground("self_check_update",{},function(e){"no_update"==e&&$(".upGrade-button").css("display","none")});break;case"hidden_notify":// 关闭消息的展示栏目，则要记录本次的消息信息
contentScriptToBackground("record_notify",c,function(e){});break;case"get_notify"://事件消息
contentScriptToBackground("get_notify",{},showEventNotify);break;case"check_turn":
//contentScriptToBackground('get_notify','',function(res){})
contentScriptToBackground("check_turn",{value:c.value},function(e){});break;case"sales_auto":
//contentScriptToBackground('get_notify','',function(res){})
contentScriptToBackground("set_sales_auto",{value:c.value},function(e){});break;case"login_tip"://事件消息
contentScriptToBackground("login_tip",{},loginTip);break;case"remove_cookie":contentScriptToBackground("remove_cookie",//事件消息
c={country:document.domain});break;case"collectedShop":
//收藏店铺
createTjLoadingDom(),contentScriptToBackground("collection_store",c,collectedShopState);break;case"getSycmWordsData":
//获取速卖通关键词数据
contentScriptToBackground("get_sycm_words_data",c,exportProductKeyword);break;case"toExportExpertWordExcel":
//获取速卖通关键词数据
contentScriptToBackground("toExportExpertWordExcel",c,toExportExpertWordExcel);break;case"removeShop":
//取消收藏店铺
contentScriptToBackground("remove_shop",c,removeShopState);break;case"empty_func":
//空方法
contentScriptToBackground("empty_func",c,removeShopState);break;case"createSearchImgButtonDomRuSingle":contentScriptToBackground("createSearchImgButtonDomRuSingle",c,createSearchImgButtonDomRuSingle);break;case"remove_product":
//取消商品的收藏
contentScriptToBackground("remove_product",c,removeProduct)}}),chrome.runtime.onMessage.addListener(function(e,t,o){o("success");var c,r,o=e.type,a=e.cmd,e=e.data;if("popup"==o)"download_img"==a&&(c=collectSourceUrls(e.type),r=bg_i18n("lang_wait_download"),alertInfo(r,6e3),contentScriptToBackground("sources_download",c,downloadSources));else if("background"==o)return"user_info_change"==a&&(""==e||null==e||0==e?($(".h3-button").css("display","none"),$(".no_login_block").css("display","block"),$(".no_login_none").css("display","none")):($(".no_login_block").css("display","none"),$(".no_login_none").css("display","inline-block"),$(".h3-button").css("display","block"))),"show_update_tip"==a&&$(".upGrade-button").css("display","inline"),"need_update"==a?($(".upGrade-button").css("display","inline"),waringUpdate(1,e)):"must_update"==a?($(".upGrade-button").css("display","inline"),waringUpdate(2,e)):"close_update"==a?($(".upGrade-button").css("display","none"),waringUpdate(0,e),"success"):"site_notify"==a?siteMessageNotify(e):void 0});


