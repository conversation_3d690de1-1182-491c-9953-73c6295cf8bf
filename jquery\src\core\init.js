// Initialize a jQuery object
define(["../core","../var/document","../var/isFunction","./var/rsingleTag","../traversing/findFilter"],function(r,s,o,h){"use strict";
// A central reference to the root jQuery(document)
var a,
// A simple way to check for HTML strings
// Prioritize #id over <tag> to avoid XSS via location.hash (#9521)
// Strict HTML recognition (#11290: must start with <)
// Shortcut simple #id case for speed
f=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,t=r.fn.init=function(t,e,i){
// HANDLE: $(""), $(null), $(undefined), $(false)
if(t){
// Handle HTML strings
if(
// Method init() accepts an alternate rootjQuery
// so migrate can support jQuery.sub (gh-2101)
i=i||a,"string"!=typeof t)return t.nodeType?(this[0]=t,this.length=1,this):o(t)?void 0!==i.ready?i.ready(t):
// Execute immediately if ready is not present
t(r):r.makeArray(t,this);
// Match html or make sure no context is specified for #id
if(!(
// Assume that strings that start and end with <> are HTML and skip the regex check
n="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:f.exec(t))||!n[1]&&e)return(!e||e.jquery?e||i:this.constructor(e)).find(t);
// HANDLE: $(DOMElement)
// HANDLE: $(html) -> $(array)
if(n[1]){
// HANDLE: $(html, props)
if(e=e instanceof r?e[0]:e,
// Option to run scripts is true for back-compat
// Intentionally let the error be thrown if parseHTML is not present
r.merge(this,r.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:s,!0)),h.test(n[1])&&r.isPlainObject(e))for(var n in e)
// Properties of context are called as methods if possible
o(this[n])?this[n](e[n]):this.attr(n,e[n])}else(i=s.getElementById(n[2]))&&(
// Inject the element directly into the jQuery object
this[0]=i,this.length=1);
// HANDLE: $(expr, $(...))
}return this};
// Give the init function the jQuery prototype for later instantiation
return t.prototype=r.fn,
// Initialize central reference
a=r(s),t});