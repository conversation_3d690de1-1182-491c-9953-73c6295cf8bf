define(["../core","../core/stripAndCollapse","../var/isFunction","../var/rnothtmlwhite","../data/var/dataPriv","../core/init"],function(h,l,f,s,o){"use strict";function c(t){return t.getAttribute&&t.getAttribute("class")||""}function u(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(s)||[]}h.fn.extend({addClass:function(s){var t,e,i,r,a,n,o=0;if(f(s))return this.each(function(t){h(this).addClass(s.call(this,t,c(this)))});if((t=u(s)).length)for(;e=this[o++];)if(n=c(e),i=1===e.nodeType&&" "+l(n)+" "){for(a=0;r=t[a++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");
// Only assign if different to avoid unneeded rendering.
n!==(n=l(i))&&e.setAttribute("class",n)}return this},removeClass:function(s){var t,e,i,r,a,n,o=0;if(f(s))return this.each(function(t){h(this).removeClass(s.call(this,t,c(this)))});if(!arguments.length)return this.attr("class","");if((t=u(s)).length)for(;e=this[o++];)if(n=c(e),
// This expression is here for better compressibility (see addClass)
i=1===e.nodeType&&" "+l(n)+" "){for(a=0;r=t[a++];)
// Remove *all* instances
for(;-1<i.indexOf(" "+r+" ");)i=i.replace(" "+r+" "," ");
// Only assign if different to avoid unneeded rendering.
n!==(n=l(i))&&e.setAttribute("class",n)}return this},toggleClass:function(r,s){var a=typeof r,n="string"==a||Array.isArray(r);return"boolean"==typeof s&&n?s?this.addClass(r):this.removeClass(r):f(r)?this.each(function(t){h(this).toggleClass(r.call(this,t,c(this),s),s)}):this.each(function(){var t,s,e,i;if(n)for(
// Toggle individual class names
s=0,e=h(this),i=u(r);t=i[s++];)
// Check each className given, space separated list
e.hasClass(t)?e.removeClass(t):e.addClass(t);
// Toggle whole class name
else void 0!==r&&"boolean"!=a||((t=c(this))&&
// Store className if set
o.set(this,"__className__",t),
// If the element has a class name or if we're passed `false`,
// then remove the whole classname (if there was one, the above saved it).
// Otherwise bring back whatever was previously saved (if anything),
// falling back to the empty string if nothing was stored.
this.setAttribute&&this.setAttribute("class",!t&&!1!==r&&o.get(this,"__className__")||""))})},hasClass:function(t){for(var s,e=0,i=" "+t+" ";s=this[e++];)if(1===s.nodeType&&-1<(" "+l(c(s))+" ").indexOf(i))return!0;return!1}})});