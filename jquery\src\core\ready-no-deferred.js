define(["../core","../var/document","../var/isFunction"],function(n,t,d){"use strict";var i=[],o=function(e){i.push(e)};
/**
 * The ready event handler and self cleanup method
 */
function e(){t.removeEventListener("DOMContentLoaded",e),window.removeEventListener("load",e),n.ready()}
// Catch cases where $(document).ready() is called
// after the browser event has already occurred.
// Support: IE9-10 only
// Older IE sometimes signals "interactive" too soon
n.fn.ready=function(e){return o(e),this},n.extend({
// Is the DOM ready to be used? Set to true once it occurs.
isReady:!1,
// A counter to track how many items to wait for before
// the ready event fires. See #6781
readyWait:1,ready:function(e){
// Abort if there are pending holds or we're already ready
(!0===e?--n.readyWait:n.isReady)||(
// Remember that the DOM is ready
n.isReady=!0)!==e&&0<--n.readyWait||(o=function(e){for(i.push(e);i.length;)e=i.shift(),d(e)&&!function(e){
// Prevent errors from freezing future callback execution (gh-1823)
// Not backwards-compatible as this does not execute sync
window.setTimeout(function(){e.call(t,n)})}(e)})()}}),
// Make jQuery.ready Promise consumable (gh-1778)
n.ready.then=n.fn.ready,"complete"===t.readyState||"loading"!==t.readyState&&!t.documentElement.doScroll?
// Handle it asynchronously to allow scripts the opportunity to delay ready
window.setTimeout(n.ready):(
// Use the handy event callback
t.addEventListener("DOMContentLoaded",e),
// A fallback to window.onload, that will always work
window.addEventListener("load",e))});