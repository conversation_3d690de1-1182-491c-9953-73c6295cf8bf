define(["./support"],function(t){"use strict";
// We have to close these tags to support XHTML (#13200)
var o={
// XHTML parsers do not magically insert elements in the
// same way that tag soup parsers do. So we cannot shorten
// this by omitting <tbody> or other required elements.
thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};return o.tbody=o.tfoot=o.colgroup=o.caption=o.thead,o.th=o.td,
// Support: IE <=9 only
t.option||(o.optgroup=o.option=[1,"<select multiple='multiple'>","</select>"]),o});