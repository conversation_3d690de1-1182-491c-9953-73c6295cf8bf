{"_from": "j<PERSON>y", "_id": "jquery@3.6.0", "_inBundle": false, "_integrity": "sha512-JVzAR/AjBvVt2BmYhxRCSYysDsPcssdmTFnzyLEts9qNwmjmu4JTAMYubEfwVOSwpQ1I1sKKFcxhZCI2buerfw==", "_location": "/jquery", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "j<PERSON>y", "name": "j<PERSON>y", "escapedName": "j<PERSON>y", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/jquery/-/jquery-3.6.0.tgz", "_shasum": "c72a09f15c1bdce142f49dbf1170bdf8adac2470", "_spec": "j<PERSON>y", "_where": "E:\\code\\Chrome\\chrome-plugin-demo\\smt_v3", "author": {"name": "OpenJS Foundation and other contributors", "url": "https://github.com/jquery/jquery/blob/3.6.0/AUTHORS.txt"}, "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "bundleDependencies": false, "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}, "deprecated": false, "description": "JavaScript library for DOM operations", "devDependencies": {"@babel/core": "7.3.3", "@babel/plugin-transform-for-of": "7.2.0", "commitplease": "3.2.0", "core-js": "2.6.5", "eslint-config-jquery": "3.0.0", "grunt": "1.3.0", "grunt-babel": "8.0.0", "grunt-cli": "1.3.2", "grunt-compare-size": "0.4.2", "grunt-contrib-uglify": "3.4.0", "grunt-contrib-watch": "1.1.0", "grunt-eslint": "22.0.0", "grunt-git-authors": "3.2.0", "grunt-jsonlint": "1.1.0", "grunt-karma": "4.0.0", "grunt-newer": "1.3.0", "grunt-npmcopy": "0.2.0", "gzip-js": "0.3.2", "husky": "1.3.1", "insight": "0.10.1", "jsdom": "13.2.0", "karma": "5.2.3", "karma-browserstack-launcher": "1.4.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-ie-launcher": "1.0.0", "karma-jsdom-launcher": "8.0.2", "karma-qunit": "3.0.0", "load-grunt-tasks": "5.1.0", "native-promise-only": "0.8.1", "promises-aplus-tests": "2.1.2", "q": "1.5.1", "qunit": "2.9.2", "raw-body": "2.3.3", "requirejs": "2.3.6", "sinon": "2.3.7", "sizzle": "2.3.6", "strip-json-comments": "2.0.1", "testswarm": "1.1.2", "uglify-js": "3.4.7"}, "homepage": "https://jquery.com", "husky": {"hooks": {"commit-msg": "commitplease .git/COMMIT_EDITMSG", "pre-commit": "grunt lint:newer qunit_fixture"}}, "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "license": "MIT", "main": "dist/jquery.js", "name": "j<PERSON>y", "repository": {"type": "git", "url": "git+https://github.com/jquery/jquery.git"}, "scripts": {"build": "npm install && grunt", "jenkins": "npm run test:browserless", "start": "grunt watch", "test": "npm run test:slim && npm run test:no-deprecated && npm run test:no-sizzle && grunt && grunt test:slow && grunt karma:main && grunt karma:amd", "test:amd": "grunt && grunt karma:amd", "test:browser": "grunt && grunt karma:main", "test:browserless": "grunt && grunt test:slow", "test:no-deprecated": "grunt test:prepare && grunt custom:-deprecated && grunt karma:main", "test:no-sizzle": "grunt test:prepare && grunt custom:-sizzle && grunt karma:main", "test:slim": "grunt test:prepare && grunt custom:slim && grunt karma:main"}, "title": "j<PERSON><PERSON><PERSON>", "version": "3.6.0"}