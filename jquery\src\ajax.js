define(["./core","./var/document","./var/isFunction","./var/rnothtmlwhite","./ajax/var/location","./ajax/var/nonce","./ajax/var/rquery","./core/init","./core/parseXML","./event/trigger","./deferred","./serialize"],function(j,w,s,b,S,C,L){"use strict";var H=/%20/g,M=/#.*$/,R=/([?&])_=[^&]*/,D=/^(.*?):[ \t]*([^\r\n]*)$/gm,q=/^(?:GET|HEAD)$/,E=/^\/\//,
/* Prefilters
	 * 1) They are useful to introduce custom dataTypes (see ajax/jsonp.js for an example)
	 * 2) These are called:
	 *    - BEFORE asking for a transport
	 *    - AFTER param serialization (s.data is a string if s.processData is true)
	 * 3) key is the dataType
	 * 4) the catchall symbol "*" can be used
	 * 5) execution will start with transport dataType and THEN continue down to "*" if needed
	 */
F={},
/* Transports bindings
	 * 1) key is the dataType
	 * 2) the catchall symbol "*" can be used
	 * 3) selection will start with transport dataType and THEN go to "*" if needed
	 */
O={},
// Avoid comment-prolog char sequence (#10098); must appease lint and evade compression
A="*/".concat("*"),
// Anchor tag for parsing the document origin
N=w.createElement("a");
// Base "constructor" for jQuery.ajaxPrefilter and jQuery.ajaxTransport
function e(n){
// dataTypeExpression is optional and defaults to "*"
return function(e,t){"string"!=typeof e&&(t=e,e="*");var a,r=0,o=e.toLowerCase().match(b)||[];if(s(t))
// For each dataType in the dataTypeExpression
for(;a=o[r++];)
// Prepend if requested
"+"===a[0]?(a=a.slice(1)||"*",(n[a]=n[a]||[]).unshift(t)):(n[a]=n[a]||[]).push(t)}}
// Base inspection function for prefilters and transports
function $(t,r,o,n){var s={},i=t===O;function c(e){var a;return s[e]=!0,j.each(t[e]||[],function(e,t){t=t(r,o,n);return"string"!=typeof t||i||s[t]?i?!(a=t):void 0:(r.dataTypes.unshift(t),c(t),!1)}),a}return c(r.dataTypes[0])||!s["*"]&&c("*")}
// A special extend for ajax options
// that takes "flat" options (not to be deep extended)
// Fixes #9887
function a(e,t){var a,r,o=j.ajaxSettings.flatOptions||{};for(a in t)void 0!==t[a]&&((o[a]?e:r=r||{})[a]=t[a]);return r&&j.extend(!0,e,r),e}
/* Handles responses to an ajax request:
 * - finds the right dataType (mediates between content-type and expected dataType)
 * - returns the corresponding response
 */return N.href=S.href,j.extend({
// Counter for holding the number of active queries
active:0,
// Last-Modified header cache for next request
lastModified:{},etag:{},ajaxSettings:{url:S.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(S.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",
/*
		timeout: 0,
		data: null,
		dataType: null,
		username: null,
		password: null,
		cache: null,
		throws: false,
		traditional: false,
		headers: {},
		*/
accepts:{"*":A,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},
// Data converters
// Keys separate source (or catchall "*") and destination types with a single space
converters:{
// Convert anything to text
"* text":String,
// Text to html (true = no transformation)
"text html":!0,
// Evaluate text as a json expression
"text json":JSON.parse,
// Parse text as xml
"text xml":j.parseXML},
// For options that shouldn't be deep extended:
// you can add your own custom options here if
// and when you create one that shouldn't be
// deep extended (see ajaxExtend)
flatOptions:{url:!0,context:!0}},
// Creates a full fledged settings object into target
// with both ajaxSettings and settings fields.
// If target is omitted, writes into ajaxSettings.
ajaxSetup:function(e,t){return t?
// Building a settings object
a(a(e,j.ajaxSettings),t):
// Extending ajaxSettings
a(j.ajaxSettings,e)},ajaxPrefilter:e(F),ajaxTransport:e(O),
// Main method
ajax:function(e,t){
// If url is an object, simulate pre-1.5 signature
"object"==typeof e&&(t=e,e=void 0);
// Force options to be an object
var c,
// URL without anti-cache param
d,
// Response headers
f,a,
// timeout handle
p,
// Request state (becomes false upon send and true upon completion)
u,
// To know if global events are to be dispatched
l,
// Loop variable
r,
// Create the final options object
y=j.ajaxSetup({},t=t||{}),
// Callbacks context
h=y.context||y,
// Context for global events is callbackContext if it is a DOM node or jQuery collection
x=y.context&&(h.nodeType||h.jquery)?j(h):j.event,
// Deferreds
g=j.Deferred(),m=j.Callbacks("once memory"),
// Status-dependent callbacks
v=y.statusCode||{},
// Headers (they are sent all at once)
o={},n={},
// Default abort message
s="canceled",
// Fake xhr
T={readyState:0,
// Builds headers hashtable if needed
getResponseHeader:function(e){var t;if(u){if(!a)for(a={};t=D.exec(f);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},
// Raw string
getAllResponseHeaders:function(){return u?f:null},
// Caches the header
setRequestHeader:function(e,t){return null==u&&(e=n[e.toLowerCase()]=n[e.toLowerCase()]||e,o[e]=t),this},
// Overrides response content-type header
overrideMimeType:function(e){return null==u&&(y.mimeType=e),this},
// Status-dependent callbacks
statusCode:function(e){if(e)if(u)
// Execute the appropriate callbacks
T.always(e[T.status]);else
// Lazy-add the new callbacks in a way that preserves old ones
for(var t in e)v[t]=[v[t],e[t]];return this},
// Cancel the request
abort:function(e){e=e||s;return c&&c.abort(e),i(0,e),this}};
// Attach deferreds
// A cross-domain request is in order when the origin doesn't match the current origin.
if(g.promise(T),
// Add protocol if not provided (prefilters might expect it)
// Handle falsy url in the settings object (#10093: consistency with old signature)
// We also use the url parameter if available
y.url=((e||y.url||S.href)+"").replace(E,S.protocol+"//"),
// Alias method option to type as per ticket #12004
y.type=t.method||t.type||y.method||y.type,
// Extract dataTypes list
y.dataTypes=(y.dataType||"*").toLowerCase().match(b)||[""],null==y.crossDomain){e=w.createElement("a");
// Support: IE <=8 - 11, Edge 12 - 15
// IE throws exception on accessing the href property if url is malformed,
// e.g. http://example.com:80x/
try{e.href=y.url,
// Support: IE <=8 - 11 only
// Anchor's host property isn't correctly set when s.url is relative
e.href=e.href,y.crossDomain=N.protocol+"//"+N.host!=e.protocol+"//"+e.host}catch(e){
// If there is an error parsing the URL, assume it is crossDomain,
// it can be rejected by the transport if it is invalid
y.crossDomain=!0}}
// Convert data if not already a string
// If request was aborted inside a prefilter, stop there
if(y.data&&y.processData&&"string"!=typeof y.data&&(y.data=j.param(y.data,y.traditional)),
// Apply prefilters
$(F,y,t,T),!u){
// Check for headers option
for(r in
// We can fire global events as of now if asked to
// Don't fire events if jQuery.event is undefined in an AMD-usage scenario (#15118)
// Watch for a new set of requests
(l=j.event&&y.global)&&0==j.active++&&j.event.trigger("ajaxStart"),
// Uppercase the type
y.type=y.type.toUpperCase(),
// Determine if request has content
y.hasContent=!q.test(y.type),
// Save the URL in case we're toying with the If-Modified-Since
// and/or If-None-Match header later on
// Remove hash to simplify url manipulation
d=y.url.replace(M,""),
// More options handling for requests with no content
y.hasContent?y.data&&y.processData&&0===(y.contentType||"").indexOf("application/x-www-form-urlencoded")&&(y.data=y.data.replace(H,"+")):(
// Remember the hash so we can put it back
e=y.url.slice(d.length),
// If data is available and should be processed, append data to url
y.data&&(y.processData||"string"==typeof y.data)&&(d+=(L.test(d)?"&":"?")+y.data,
// #9682: remove data so that it's not used in an eventual retry
delete y.data),
// Add or update anti-cache param if needed
!1===y.cache&&(d=d.replace(R,"$1"),e=(L.test(d)?"&":"?")+"_="+C.guid+++e),
// Put hash and anti-cache on the URL that will be requested (gh-1732)
y.url=d+e),
// Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.
y.ifModified&&(j.lastModified[d]&&T.setRequestHeader("If-Modified-Since",j.lastModified[d]),j.etag[d])&&T.setRequestHeader("If-None-Match",j.etag[d]),
// Set the correct header, if data is being sent
(y.data&&y.hasContent&&!1!==y.contentType||t.contentType)&&T.setRequestHeader("Content-Type",y.contentType),
// Set the Accepts header for the server, depending on the dataType
T.setRequestHeader("Accept",y.dataTypes[0]&&y.accepts[y.dataTypes[0]]?y.accepts[y.dataTypes[0]]+("*"!==y.dataTypes[0]?", "+A+"; q=0.01":""):y.accepts["*"]),y.headers)T.setRequestHeader(r,y.headers[r]);
// Allow custom headers/mimetypes and early abort
if(y.beforeSend&&(!1===y.beforeSend.call(h,T,y)||u))
// Abort if not done already and return
return T.abort();
// Aborting is no longer a cancellation
// If no transport, we auto-abort
if(s="abort",
// Install callbacks on deferreds
m.add(y.complete),T.done(y.success),T.fail(y.error),
// Get transport
c=$(O,y,t,T)){
// If request was aborted inside ajaxSend, stop there
if(T.readyState=1,
// Send global event
l&&x.trigger("ajaxSend",[T,y]),u)return T;
// Timeout
y.async&&0<y.timeout&&(p=window.setTimeout(function(){T.abort("timeout")},y.timeout));try{u=!1,c.send(o,i)}catch(e){
// Rethrow post-completion exceptions
if(u)throw e;
// Propagate others as results
i(-1,e)}}
// Callback for when everything is done
else i(-1,"No Transport")}return T;function i(e,t,a,r){var o,n,s,i=t;
// Ignore repeat invocations
u||(u=!0,
// Clear timeout if it exists
p&&window.clearTimeout(p),
// Dereference transport for early garbage collection
// (no matter how long the jqXHR object will be used)
c=void 0,
// Cache response headers
f=r||"",
// Set readyState
T.readyState=0<e?4:0,
// Determine if successful
r=200<=e&&e<300||304===e,
// Get response data
a&&(s=function(e,t,a){
// Remove auto dataType and get content-type in the process
for(var r,o,n,s,i=e.contents,c=e.dataTypes;"*"===c[0];)c.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));
// Check if we're dealing with a known content-type
if(r)for(o in i)if(i[o]&&i[o].test(r)){c.unshift(o);break}
// Check to see if we have a response for the expected dataType
if(c[0]in a)n=c[0];else{
// Try convertible dataTypes
for(o in a){if(!c[0]||e.converters[o+" "+c[0]]){n=o;break}s=s||o}
// Or just use first one
n=n||s}
// If we found a dataType
// We add the dataType to the list if needed
// and return the corresponding response
if(n)return n!==c[0]&&c.unshift(n),a[n]}
/* Chain conversions given the request and the original response
 * Also sets the responseXXX fields on the jqXHR instance
 */(y,T,a)),
// Use a noop converter for missing script but not if jsonp
!r&&-1<j.inArray("script",y.dataTypes)&&j.inArray("json",y.dataTypes)<0&&(y.converters["text script"]=function(){}),
// Convert no matter what (that way responseXXX fields are always set)
s=function(e,t,a,r){var o,n,s,i,c,d={},
// Work with a copy of dataTypes in case we need to modify it for conversion
f=e.dataTypes.slice();
// Create converters map with lowercased keys
if(f[1])for(s in e.converters)d[s.toLowerCase()]=e.converters[s];
// Convert to each sequential dataType
for(n=f.shift();n;)if(e.responseFields[n]&&(a[e.responseFields[n]]=t),
// Apply the dataFilter if provided
!c&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),c=n,n=f.shift())
// There's only work to do if current dataType is non-auto
if("*"===n)n=c;
// Convert response if prev dataType is non-auto and differs from current
else if("*"!==c&&c!==n){
// If none found, seek a pair
if(!(
// Seek a direct converter
s=d[c+" "+n]||d["* "+n]))for(o in d)if((
// If conv2 outputs current
i=o.split(" "))[1]===n&&(
// If prev can be converted to accepted input
s=d[c+" "+i[0]]||d["* "+i[0]])){
// Condense equivalence converters
!0===s?s=d[o]:!0!==d[o]&&(n=i[0],f.unshift(i[1]));break}
// Apply converter (if not an equivalence)
if(!0!==s)
// Unless errors are allowed to bubble, catch and return them
if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+c+" to "+n}}}return{state:"success",data:t}}(y,s,T,r),
// If successful, handle type chaining
r?(
// Set the If-Modified-Since and/or If-None-Match header, if in ifModified mode.
y.ifModified&&((a=T.getResponseHeader("Last-Modified"))&&(j.lastModified[d]=a),a=T.getResponseHeader("etag"))&&(j.etag[d]=a),
// if no content
204===e||"HEAD"===y.type?i="nocontent":304===e?i="notmodified":(i=s.state,o=s.data,r=!(n=s.error))):(
// Extract error from statusText and normalize for non-aborts
n=i,!e&&i||(i="error",e<0&&(e=0))),
// Set data for the fake xhr object
T.status=e,T.statusText=(t||i)+"",
// Success/Error
r?g.resolveWith(h,[o,i,T]):g.rejectWith(h,[T,i,n]),
// Status-dependent callbacks
T.statusCode(v),v=void 0,l&&x.trigger(r?"ajaxSuccess":"ajaxError",[T,y,r?o:n]),
// Complete
m.fireWith(h,[T,i]),l&&(x.trigger("ajaxComplete",[T,y]),
// Handle the global AJAX counter
--j.active||j.event.trigger("ajaxStop")))}},getJSON:function(e,t,a){return j.get(e,t,a,"json")},getScript:function(e,t){return j.get(e,void 0,t,"script")}}),j.each(["get","post"],function(e,o){j[o]=function(e,t,a,r){
// The url can be an options object (which then must have .url)
// Shift arguments if data argument was omitted
return s(t)&&(r=r||a,a=t,t=void 0),j.ajax(j.extend({url:e,type:o,dataType:r,data:t,success:a},j.isPlainObject(e)&&e))}}),j.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),j});