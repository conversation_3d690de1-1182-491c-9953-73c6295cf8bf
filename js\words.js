!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Js2WordCloud=e():t.Js2WordCloud=e()}(this,function(){/******/
return n=[
/* 0 */
/***/function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(t,e,i){return e&&a(t.prototype,e),i&&a(t,i),t};function a(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var s=i(1),r=(i(2),i(6).default),l='<div class="__wc_loading_wrapper__"><div style="padding-left: 60px;" class="__wc_loading_wrapper_item__"><div class="__wc_loading_wrapper_item_inner__">',d="</div></div></div>",i=e.Js2WordCloud=(n(o,[{key:"setOption",value:function(t){var a=this,o=(this._option=r.copy(t,!0),this._option.hover);this._option.fontFamily=this._option.fontFamily||"Microsoft YaHei,Helvetica,Times,serif",this._fixWeightFactor(this._option),this._maskCanvas=null;t.tooltip&&!0===t.tooltip.show&&(this._tooltip||(this._tooltip=window.document.createElement("div"),this._tooltip.className="__wc_tooltip__",this._tooltip.style.backgroundColor=t.tooltip.backgroundColor||"rgba(0, 0, 0, 0.701961)",this._tooltip.style.color="#fff",this._tooltip.style.padding="5px",this._tooltip.style.borderRadius="5px",this._tooltip.style.fontSize="12px",this._tooltip.style.fontFamily=t.fontFamily||this._option.fontFamily,this._tooltip.style.lineHeight=1.4,this._tooltip.style.webkitTransition="left 0.2s, top 0.2s",this._tooltip.style.mozTransition="left 0.2s, top 0.2s",this._tooltip.style.transition="left 0.2s, top 0.2s",this._tooltip.style.position="absolute",this._tooltip.style.whiteSpace="nowrap",this._tooltip.style.zIndex=999,this._tooltip.style.display="none",this._wrapper.appendChild(this._tooltip),this._container.onmouseout=function(){a._tooltip.style.display="none"}),this._option.hover=function(t,e,i){var n;t?(n=t[0]+": "+t[1],"function"==typeof a._option.tooltip.formatter&&(n=a._option.tooltip.formatter(t)),a._tooltip.innerHTML=n,a._tooltip.style.top=i.offsetY+10+"px",a._tooltip.style.left=i.offsetX+15+"px",a._tooltip.style.display="block",a._wrapper.style.cursor="pointer"):(a._tooltip.style.display="none",a._wrapper.style.cursor="default"),o&&o(t,e,i)}),(t=this._option).list&&t.list.sort(function(t,e){return e[1]-t[1]}),(this._option&&/\.(jpg|png)$/.test(this._option.imageShape)?function(o){var r=this,s=window.document.createElement("img");s.crossOrigin="Anonymous",s.onload=function(){r._maskCanvas=window.document.createElement("canvas"),r._maskCanvas.width=s.width,r._maskCanvas.height=s.height;for(var t=r._maskCanvas.getContext("2d"),e=(t.drawImage(s,0,0,s.width,s.height),t.getImageData(0,0,r._maskCanvas.width,r._maskCanvas.height)),i=t.createImageData(e),n=0;n<e.data.length;n+=4){var a=e.data[n]+e.data[n+1]+e.data[n+2];e.data[n+3]<128||384<a?(
// Area not to draw
i.data[n]=i.data[n+1]=i.data[n+2]=255,i.data[n+3]=0):(
// Area to draw
i.data[n]=i.data[n+1]=i.data[n+2]=0,i.data[n+3]=255)}t.putImageData(i,0,0),c.call(r,o)},s.onerror=function(){c.call(this,o)},s.src=o.imageShape}:"circle"===this._option.shape?function(t){this._maskCanvas=window.document.createElement("canvas"),this._maskCanvas.width=500,this._maskCanvas.height=500;for(var e=this._maskCanvas.getContext("2d"),i=(e.fillStyle="#000000",e.beginPath(),e.arc(250,250,240,0,2*Math.PI,!0),e.closePath(),e.fill(),e.getImageData(0,0,this._maskCanvas.width,this._maskCanvas.height)),n=e.createImageData(i),a=0;a<i.data.length;a+=4){var o=i.data[a]+i.data[a+1]+i.data[a+2];i.data[a+3]<128||384<o?(
// Area not to draw
n.data[a]=n.data[a+1]=n.data[a+2]=255,n.data[a+3]=0):(
// Area to draw
n.data[a]=n.data[a+1]=n.data[a+2]=0,n.data[a+3]=255)}e.putImageData(n,0,0),c.call(this,t)}:c).call(this,this._option)}
/**
	         * 事件绑定
	         * @todo
	         */},{key:"on",value:function(){}},{key:"showLoading",value:function(t){var e,i,n="正在加载...",a='<div class="__wc_loading__"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>';t?(t.backgroundColor&&(this._dataMask.style.backgroundColor=t.backgroundColor),e=void 0===t.text?n:t.text,"spin"===t.effect?(this._showMask(l+a+e+d),i=(t=this._dataMask.childNodes[0].childNodes[0]).style.paddingLeft,t.style.paddingLeft=parseInt(i)+45+"px"):this._showMask(l+e+d)):this._showMask(l+a+n+d)}},{key:"hideLoading",value:function(){this._dataMask&&(this._dataMask.style.display="none")}},{key:"resize",value:function(){this._canvas.width=this._container.clientWidth,this._canvas.height=this._container.clientHeight,c.call(this,this._option)}},{key:"_init",value:function(){var t=this._container.clientWidth,e=this._container.clientHeight;this._container.innerHTML="",this._wrapper=window.document.createElement("div"),this._wrapper.style.position="relative",this._wrapper.style.width="100%",this._wrapper.style.height="inherit",this._dataMask=window.document.createElement("div"),this._dataMask.height="inherit",this._dataMask.style.textAlign="center",this._dataMask.style.color="#888",this._dataMask.style.fontSize="14px",this._dataMask.style.position="absolute",this._dataMask.style.left="0",this._dataMask.style.right="0",this._dataMask.style.top="0",this._dataMask.style.bottom="0",this._dataMask.style.display="none",this._wrapper.appendChild(this._dataMask),this._container.appendChild(this._wrapper),this._canvas=window.document.createElement("canvas"),this._canvas.width=t,this._canvas.height=e,this._wrapper.appendChild(this._canvas)}},{key:"_fixWeightFactor",value:function(e){if(e.maxFontSize="number"==typeof e.maxFontSize?e.maxFontSize:60,e.minFontSize="number"==typeof e.minFontSize?e.minFontSize:12,e.list&&0<e.list.length){for(var i,n,a,t=e.list[0][1],o=0,r=0,s=e.list.length;r<s;r++)t>e.list[r][1]&&(t=e.list[r][1]),o<e.list[r][1]&&(o=e.list[r][1]);
//用y=ax^r+b公式确定字体大小
t<o?(i="number"==typeof e.fontSizeFactor?e.fontSizeFactor:.1,n=(e.maxFontSize-e.minFontSize)/(Math.pow(o,i)-Math.pow(t,i)),a=e.maxFontSize-n*Math.pow(o,i),
// var x = (option.maxFontSize - option.minFontSize) / (1 - min / max)
// var y = option.maxFontSize - x
e.weightFactor=function(t){return Math.ceil(n*Math.pow(t,i)+a);
// var s = Math.ceil((size / max) * x + y)
// return s
}):e.weightFactor=function(t){return e.minFontSize}}}},{key:"_showMask",value:function(t){this._dataMask&&(this._dataMask.innerHTML=t,this._dataMask.style.display=""===t?"none":"block")}},{key:"_dataEmpty",value:function(){return!this._option||!this._option.list||this._option.list.length<=0}}]),o);function o(t){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this._container=t,this._wrapper=null,this._canvas=null,this._maskCanvas=null,this._dataMask=null,this._tooltip=null,this._wordcloud2=null,this._option=null,this._init()}function c(t){if(this._maskCanvas){t.clearCanvas=!1;for(
/* Determine bgPixel by creating
	         another canvas and fill the specified background color. */
var e=window.document.createElement("canvas").getContext("2d"),i=(e.fillStyle=t.backgroundColor||"#fff",e.fillRect(0,0,1,1),e.getImageData(0,0,1,1).data),e=window.document.createElement("canvas"),n=(e.width=this._canvas.width,e.height=this._canvas.height,e.getContext("2d")),a=(n.drawImage(this._maskCanvas,0,0,this._maskCanvas.width,this._maskCanvas.height,0,0,e.width,e.height),n.getImageData(0,0,e.width,e.height)),o=n.createImageData(a),r=0;r<a.data.length;r+=4)128<a.data[r+3]?(o.data[r]=i[0],o.data[r+1]=i[1],o.data[r+2]=i[2],o.data[r+3]=i[3]):(
// This color must not be the same w/ the bgPixel.
o.data[r]=i[0],o.data[r+1]=i[1],o.data[r+2]=i[2],o.data[r+3]=i[3]?i[3]-1:1);n.putImageData(o,0,0),(n=this._canvas.getContext("2d")).clearRect(0,0,this._canvas.width,this._canvas.height),n.drawImage(e,0,0)}this._dataEmpty()&&t&&t.noDataLoadingOption?(n="",t.noDataLoadingOption.textStyle&&("string"==typeof t.noDataLoadingOption.textStyle.color&&(n+="color: "+t.noDataLoadingOption.textStyle.color+";"),"number"==typeof t.noDataLoadingOption.textStyle.fontSize)&&(n+="font-size: "+t.noDataLoadingOption.textStyle.fontSize+"px;"),"string"==typeof t.noDataLoadingOption.backgroundColor&&(this._dataMask.style.backgroundColor=t.noDataLoadingOption.backgroundColor),e=t.noDataLoadingOption.text||"",this._showMask(l+'<span class="__wc_no_data_text__" style="'+n+'">'+e+"</span>"+d)):(this._showMask(""),this._wordcloud2=s(this._canvas,t))}t.exports=i},
/* 1 */
/***/function(o,r,t){var s;
/*** IMPORTS FROM imports-loader ***/
!function(){
/*!
	   * wordcloud2.js
	   * http://timdream.org/wordcloud2.js/
	   *
	   * Copyright 2011 - 2013 Tim Chien
	   * Released under the MIT license
	   */
"use strict";
// setImmediate
var i,n,t,e,G,$;function a(m,t){if(G){(m=Array.isArray(m)?m:[m]).forEach(function(t,e){if("string"==typeof t){if(m[e]=document.getElementById(t),!m[e])throw"The element id specified is not found."}else if(!t.tagName&&!t.appendChild)throw"You must pass valid HTML elements, or ID of the element."});
/* Default values to be overwritten by options object */
var e,b={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,// 0 to disable
weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",// opaque white = rgba(255, 255, 255, 1)
gridSize:8,drawOutOfBound:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,wait:0,abortThreshold:0,// disabled
abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(t)for(var i in t)i in b&&(b[i]=t[i]);
/* Convert weightFactor into a function */
/* Convert shape into a function */
if("function"!=typeof b.weightFactor&&(e=b.weightFactor,b.weightFactor=function(t){return t*e;//in px
}),"function"!=typeof b.shape)switch(b.shape){case"circle":
/* falls through */default:
// 'circle' is the default and a shortcut in the code loop.
b.shape="circle";break;case"cardioid":b.shape=function(t){return 1-Math.sin(t)};break;
/*
	            To work out an X-gon, one has to calculate "m",
	          where 1/(cos(2*PI/X)+m*sin(2*PI/X)) = 1/(cos(0)+m*sin(0))
	          http://www.wolframalpha.com/input/?i=1%2F%28cos%282*PI%2FX%29%2Bm*sin%28
	          2*PI%2FX%29%29+%3D+1%2F%28cos%280%29%2Bm*sin%280%29%29
	            Copy the solution into polar equation r = 1/(cos(t') + m*sin(t'))
	          where t' equals to mod(t, 2PI/X);
	            */case"diamond":case"square":
// http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+
// %28t%2C+PI%2F2%29%29%2Bsin%28mod+%28t%2C+PI%2F2%29%29%29%2C+t+%3D
// +0+..+2*PI
b.shape=function(t){t%=2*Math.PI/4;return 1/(Math.cos(t)+Math.sin(t))};break;case"triangle-forward":
// http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+
// %28t%2C+2*PI%2F3%29%29%2Bsqrt%283%29sin%28mod+%28t%2C+2*PI%2F3%29
// %29%29%2C+t+%3D+0+..+2*PI
b.shape=function(t){t%=2*Math.PI/3;return 1/(Math.cos(t)+Math.sqrt(3)*Math.sin(t))};break;case"triangle":case"triangle-upright":b.shape=function(t){t=(t+3*Math.PI/2)%(2*Math.PI/3);return 1/(Math.cos(t)+Math.sqrt(3)*Math.sin(t))};break;case"pentagon":b.shape=function(t){t=(t+.955)%(2*Math.PI/5);return 1/(Math.cos(t)+.726543*Math.sin(t))};break;case"star":b.shape=function(t){var e=(t+.955)%(2*Math.PI/10);return 0<=(t+.955)%(2*Math.PI/5)-2*Math.PI/10?1/(Math.cos(2*Math.PI/10-e)+3.07768*Math.sin(2*Math.PI/10-e)):1/(Math.cos(e)+3.07768*Math.sin(e))}}
/* Make sure gridSize is a whole number and is not smaller than 4px */b.gridSize=Math.max(Math.floor(b.gridSize),4);
/* shorthand */
var g,// 2d array containing filling information
w,v,// width and height of the grid
o,// position of the center of the cloud
p,n,a,x=b.gridSize,y=x-b.maskGapWidth,r=Math.abs(b.maxRotation-b.minRotation),s=Math.min(b.maxRotation,b.minRotation);switch(b.color){case"random-dark":a=function(){return J(10,50)};break;case"random-light":a=function(){return J(50,90)};break;default:"function"==typeof b.color&&(a=b.color)}
/* function for getting the classes of the text */var l,d,c,k=null,M=("function"==typeof b.classes&&(k=b.classes)
/* Interactive */,!1),C=[],P=function(t){var e=t.currentTarget,i=e.getBoundingClientRect(),
/** Detect if touches are available */
t=(t.touches?(n=t.touches[0].clientX,t.touches[0]):(n=t.clientX,t)).clientY,n=n-i.left,t=t-i.top,n=Math.floor(n*(e.width/i.width||1)/x),t=Math.floor(t*(e.height/i.height||1)/x);return C[n][t]},j=function(t){var e=P(t);l!==e&&((l=e)?b.hover(e.item,e.dimension,t):b.hover(void 0,void 0,t))},h=function(t){var e=P(t);e&&(b.click(e.item,e.dimension,t),t.preventDefault())},f=[],A=function(t){if(f[t])return f[t];
// Look for these number of points on each radius
var e=8*t,i=e,n=[];
// Getting all the points at this radius
for(0===t&&n.push([o[0],o[1],0]);i--;){
// distort the radius to put the cloud in shape
var a=1;"circle"!==b.shape&&(a=b.shape(i/e*2*Math.PI)),
// Push [x, y, t]; t is used solely for getTextColor()
n.push([o[0]+t*a*Math.cos(-i/e*2*Math.PI),o[1]+t*a*Math.sin(-i/e*2*Math.PI)*b.ellipticity,i/e*2*Math.PI])}return f[t]=n},S=function(){return 0<b.abortThreshold&&(new Date).getTime()-n>b.abortThreshold},W=function(){return 0===b.rotateRatio||Math.random()>b.rotateRatio?0:0===r?s:s+Math.random()*r},B=function(t,e,i){
// calculate the acutal font size
// fontSize === 0 means weightFactor function wants the text skipped,
// and size < minSize means we cannot draw the text.
var n=b.weightFactor(e);if(n<=b.minSize)return!1;
// Scale factor here is to make sure fillText is not limited by
// the minium font size set by browser.
// It will always be 1 or 2n.
var e=1,a=(n<$&&(e=function(){for(var t=2;t*n<$;)t+=2;return t}()),document.createElement("canvas")),o=a.getContext("2d",{willReadFrequently:!0}),r=(o.font=b.fontWeight+" "+(n*e).toString(10)+"px "+b.fontFamily,o.measureText(t).width/e),s=Math.max(n*e,o.measureText("m").width,o.measureText("Ｗ").width)/e,l=r+2*s,d=3*s,c=Math.ceil(l/x),h=Math.ceil(d/x),l=c*x,d=h*x,c=-r/2,h=.4*-s,f=Math.ceil((l*Math.abs(Math.sin(i))+d*Math.abs(Math.cos(i)))/x),l=Math.ceil((l*Math.abs(Math.cos(i))+d*Math.abs(Math.sin(i)))/x),u=l*x,d=f*x,p=(a.setAttribute("width",u),a.setAttribute("height",d),
// Scale the canvas with |mu|.
o.scale(1/e,1/e),o.translate(u*e/2,d*e/2),o.rotate(-i),
// Once the width/height is set, ctx info will be reset.
// Set it again here.
o.font=b.fontWeight+" "+(n*e).toString(10)+"px "+b.fontFamily,
// Fill the text into the fcanvas.
// XXX: We cannot because textBaseline = 'top' here because
// Firefox and Chrome uses different default line-height for canvas.
// Please read https://bugzil.la/737852#c6.
// Here, we use textBaseline = 'middle' and draw the text at exactly
// 0.5 * fontSize lower.
o.fillStyle="#000",o.textBaseline="middle",o.fillText(t,c*e,(h+.5*n)*e),o.getImageData(0,0,u,d).data);if(S())return!1;for(
// Read the pixels and save the information to the occupied array
var _,m,g,w=[],v=l,y=[f/2,l/2,f/2,l/2];v--;)for(_=f;_--;){g=x;t:for(;g--;)for(m=x;m--;)if(p[4*((_*x+g)*u+(v*x+m))+3]){w.push([v,_]),v<y[3]&&(y[3]=v),y[1]<v&&(y[1]=v),_<y[0]&&(y[0]=_),y[2]<_&&(y[2]=_);break t}}
// Return information needed to create the text on the real canvas
return{mu:e,occupied:w,bounds:y,gw:l,gh:f,fillTextOffsetX:c,fillTextOffsetY:h,fillTextWidth:r,fillTextHeight:s,fontSize:n}},U=function(t,e,i,n,a){for(
// Go through the occupied points,
// return false if the space is not available.
var o=a.length;o--;){var r=t+a[o][0],s=e+a[o][1];if(w<=r||v<=s||r<0||s<0){if(b.drawOutOfBound)continue;return!1}if(!g[r][s])return!1}return!0},H=function(s,l,d,c,t,e,i,h,f){var u=d.fontSize,p=a?a(c,t,u,e,i):b.color,_=k?k(c,t,u,e,i):b.classes,t=d.bounds;t[3],t[0],t[1],t[3],t[2],t[0];m.forEach(function(t){if(t.getContext){var e=t.getContext("2d"),i=d.mu;
// Save the current state before messing it
e.save(),e.scale(1/i,1/i),e.font=b.fontWeight+" "+(u*i).toString(10)+"px "+b.fontFamily,e.fillStyle=p,
// Translate the canvas position to the origin coordinate of where
// the text should be put.
e.translate((s+d.gw/2)*x*i,(l+d.gh/2)*x*i),0!==h&&e.rotate(-h),
// Finally, fill the text.
// XXX: We cannot because textBaseline = 'top' here because
// Firefox and Chrome uses different default line-height for canvas.
// Please read https://bugzil.la/737852#c6.
// Here, we use textBaseline = 'middle' and draw the text at exactly
// 0.5 * fontSize lower.
e.textBaseline="middle",e.fillText(c,d.fillTextOffsetX*i,(d.fillTextOffsetY+.5*u)*i),
// The below box is always matches how <span>s are positioned
/* ctx.strokeRect(info.fillTextOffsetX, info.fillTextOffsetY,
	              info.fillTextWidth, info.fillTextHeight); */
// Restore the state.
e.restore()}else{
// drawText on DIV element
var n,a=document.createElement("span"),i="",i="rotate("+-h/Math.PI*180+"deg) ",o=(1!==d.mu&&(i+="translateX(-"+d.fillTextWidth/4+"px) scale("+1/d.mu+")"),{position:"absolute",display:"block",font:b.fontWeight+" "+u*d.mu+"px "+b.fontFamily,left:(s+d.gw/2)*x+d.fillTextOffsetX+"px",top:(l+d.gh/2)*x+d.fillTextOffsetY+"px",width:d.fillTextWidth+"px",height:d.fillTextHeight+"px",lineHeight:u+"px",whiteSpace:"nowrap",transform:i,webkitTransform:i,msTransform:i,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"});for(n in p&&(o.color=p),a.textContent=c,o)a.style[n]=o[n];if(f)for(var r in f)a.setAttribute(r,f[r]);_&&(a.className+=_),t.appendChild(a)}})},N=function(t,e,i,n,a,o){for(var r,s,l,d,c,h=a.occupied,f=b.drawMask,u=(f&&((r=m[0].getContext("2d")).save(),r.fillStyle=b.maskColor),M&&(s={x:(t+(a=a.bounds)[3])*x,y:(e+a[0])*x,w:(a[1]-a[3]+1)*x,h:(a[2]-a[0]+1)*x}),h.length);u--;){var p=t+h[u][0],_=e+h[u][1];w<=p||v<=_||p<0||_<0||(_=_,l=f,d=s,c=o,w<=(p=p))||v<=_||p<0||_<0||(g[p][_]=!1,l&&m[0].getContext("2d").fillRect(p*x,_*x,y,y),M&&(C[p][_]={item:c,dimension:d}))}f&&r.restore()},X=function(o){Array.isArray(o)?(r=o[0],s=o[1]):(r=o.word,s=o.weight,l=o.attributes);var r,s,l,d=W(),c=B(r,s,d);
// not getting the info means we shouldn't be drawing this one.
if(c&&!S()){
// If drawOutOfBound is set to false,
// skip the loop if we have already know the bounding box of
// word is larger than the canvas.
if(!b.drawOutOfBound){var t=c.bounds;if(t[1]-t[3]+1>w||t[2]-t[0]+1>v)return!1}
// Determine the position to put the text by
// start looking for the nearest points
for(var h=p+1,e=function(t){var e=Math.floor(t[0]-c.gw/2),i=Math.floor(t[1]-c.gh/2),n=c.gw,a=c.gh;
// If we cannot fit the text at this position, return false
// and go to the next position.
return!!U(e,i,n,a,c.occupied)&&(
// Actually put the text on the canvas
H(e,i,c,r,s,p-h,t[2],d,l),
// Mark the spaces on the grid as filled
N(e,i,n,a,c,o),!0)};h--;){var i=A(p-h);if(b.shuffle){i=[].concat(i),u=a=n=f=void 0;for(var n,a,f=i,u=f.length;u;n=Math.floor(Math.random()*u),a=f[--u],f[u]=f[n],f[n]=a);}
// Try to fit the words by looking at each point.
// array.some() will stop and return true
// when putWordAtPoint() returns true.
// If all the points returns false, array.some() returns false.
if(i.some(e))
// leave putWord() and return true
return!0}
// we tried all distances but text won't fit, return false
}return!1},u=function(i,n,a){if(n)return!m.some(function(t){var e=document.createEvent("CustomEvent");return e.initCustomEvent(i,!0,n,a||{}),!t.dispatchEvent(e)},this);m.forEach(function(t){var e=document.createEvent("CustomEvent");e.initCustomEvent(i,!0,n,a||{}),t.dispatchEvent(e)},this)},_=m[0];
// Sending a wordcloudstart event which cause the previous loop to stop.
// Do nothing if the event is canceled.
if(v=_.getContext?(w=Math.ceil(_.width/x),Math.ceil(_.height/x)):(E=_.getBoundingClientRect(),w=Math.ceil(E.width/x),Math.ceil(E.height/x)),u("wordcloudstart",!0)){if(
// Determine the center of the word cloud
o=b.origin?[b.origin[0]/x,b.origin[1]/x]:[w/2,v/2],
// Maxium radius to look for space
p=Math.floor(Math.sqrt(w*w+v*v)),
/* Clear the canvas only if the clearCanvas is set,
	           if not, update the grid to the current canvas state */
g=[],!_.getContext||b.clearCanvas)for(m.forEach(function(t){var e;t.getContext?((e=t.getContext("2d")).fillStyle=b.backgroundColor,e.clearRect(0,0,w*(x+1),v*(x+1)),e.fillRect(0,0,w*(x+1),v*(x+1))):(t.textContent="",t.style.backgroundColor=b.backgroundColor,t.style.position="relative")}),
/* fill the grid with empty state */
L=w;L--;)for(g[L]=[],d=v;d--;)g[L][d]=!0;else{for(
/* Determine bgPixel by creating
	             another canvas and fill the specified background color. */
var I,T,E=document.createElement("canvas").getContext("2d"),Y=(E.fillStyle=b.backgroundColor,E.fillRect(0,0,1,1),E.getImageData(0,0,1,1).data),q=_.getContext("2d").getImageData(0,0,w*x,v*x).data,L=w;L--;)for(g[L]=[],d=v;d--;){T=x;t:for(;T--;)for(I=x;I--;)for(O=4;O--;)if(q[4*((d*x+T)*w*x+(L*x+I))+O]!==Y[O]){g[L][d]=!1;break t}!1!==g[L][d]&&(g[L][d]=!0)}q=E=Y=void 0}
// fill the infoGrid with empty state if we need it
if(b.hover||b.click){for(M=!0,
/* fill the grid with empty state */
L=w+1;L--;)C[L]=[];b.hover&&_.addEventListener("mousemove",j),b.click&&(_.addEventListener("click",h),_.addEventListener("touchstart",h),_.addEventListener("touchend",function(t){t.preventDefault()}),_.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),_.addEventListener("wordcloudstart",function t(){_.removeEventListener("wordcloudstart",t),_.removeEventListener("mousemove",j),_.removeEventListener("click",h),l=void 0})}var O=0,F=0!==b.wait?(c=window.setTimeout,window.clearTimeout):(c=window.setImmediate,window.clearImmediate),R=function(e,i){m.forEach(function(t){t.removeEventListener(e,i)},this)},z=function t(){R("wordcloudstart",t),F(D)},D=(!function(e,i){m.forEach(function(t){t.addEventListener(e,i)},this)}("wordcloudstart",z),c(function t(){var e;O>=b.list.length?(F(D),u("wordcloudstop",!1),R("wordcloudstart",z)):(n=(new Date).getTime(),e=X(b.list[O]),e=!u("wordclouddrawn",!0,{item:b.list[O],drawn:e}),S()||e?(F(D),b.abort(),u("wordcloudabort",!1),u("wordcloudstop",!1),R("wordcloudstart",z)):(O++,D=c(t,b.wait)))},b.wait))}}function J(t,e){return"hsl("+(360*Math.random()).toFixed()+","+(30*Math.random()+70).toFixed()+"%,"+(Math.random()*(e-t)+t).toFixed()+"%)"}}window.setImmediate||(window.setImmediate=window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||(window.postMessage&&window.addEventListener?(i=[void 0],n="zero-timeout-message",window.addEventListener("message",function(t){
// Skipping checking event source, retarded IE confused this window
// object with another in the presence of iframe
"string"==typeof t.data&&t.data.substr(0,n.length)===n
/* ||
	                                                                                             evt.source !== window */&&(t.stopImmediatePropagation(),t=parseInt(t.data.substr(n.length),36),i[t])&&(i[t](),i[t]=void 0)},!0),
/* specify clearImmediate() here since we need the scope */
window.clearImmediate=function(t){i[t]&&(i[t]=void 0)},function(t){var e=i.length;return i.push(t),window.postMessage(n+e.toString(36),"*"),e}):null)||
// fallback
function(t){window.setTimeout(t,0)}),window.clearImmediate||(window.clearImmediate=window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||
// "clearZeroTimeout" is implement on the previous block ||
// fallback
function(t){window.clearTimeout(t)}),t=this,G=!(!(e=document.createElement("canvas"))||!e.getContext||!((e=e.getContext("2d")).getImageData&&e.fillText&&Array.prototype.some&&Array.prototype.push)),$=function(){if(G){for(var t,e,i=document.createElement("canvas").getContext("2d"),n=20
// start from 20
;n;){if(i.font=n.toString(10)+"px sans-serif",i.measureText("Ｗ").width===t&&i.measureText("m").width===e)return n+1;t=i.measureText("Ｗ").width,e=i.measureText("m").width,n--}return 0}}(),a.isSupported=G,a.minFontSize=$,t.WordCloud=a,void 0!==(s=function(){return a}.apply(r,[]))&&(o.exports=s)}.call(window)},
/* 2 */
/***/function(t,e,i){
// style-loader: Adds some css to the DOM by adding a <style> tag
// load the styles
var n=i(3);"string"==typeof n&&(n=[[t.id,n,""]]),i(5)(n,{});n.locals&&(t.exports=n.locals);
// Hot Module Replacement
},
/* 3 */
/***/function(t,e,i){
// imports
// module
(t.exports=i(4)()).push([t.id,"@-webkit-keyframes __wc_loading_animation__ {\r\n  50% {\r\n    opacity: 0.3;\r\n    -webkit-transform: scale(0.4);\r\n            transform: scale(0.4); }\r\n\r\n  100% {\r\n    opacity: 1;\r\n    -webkit-transform: scale(1);\r\n            transform: scale(1); } }\r\n\r\n@keyframes __wc_loading_animation__ {\r\n  50% {\r\n    opacity: 0.3;\r\n    -webkit-transform: scale(0.4);\r\n            transform: scale(0.4); }\r\n\r\n  100% {\r\n    opacity: 1;\r\n    -webkit-transform: scale(1);\r\n            transform: scale(1); } }\r\n\r\n.__wc_loading_wrapper__ {\r\n  display: table;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.__wc_loading_wrapper_item__ {\r\n  display: table-cell;\r\n  vertical-align: middle;\r\n  text-align: center;\r\n  position: relative;\r\n  padding-right: 60px;\r\n  line-height: 1.4;\r\n}\r\n.__wc_loading_wrapper_item_inner__ {\r\n  position: relative;\r\n  text-align: left;\r\n  display: inline-block;\r\n}\r\n\r\n.__wc_loading__ {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: -40px;\r\n  margin-top: -6px;\r\n}\r\n\r\n  .__wc_loading__ > div:nth-child(1) {\r\n    top: 15px;\r\n    left: 0;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(2) {\r\n    top: 10.22726px;\r\n    left: 10.22726px;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.12s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.12s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(3) {\r\n    top: 0;\r\n    left: 15px;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.24s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.24s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(4) {\r\n    top: -10.22726px;\r\n    left: 10.22726px;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.36s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.36s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(5) {\r\n    top: -15px;\r\n    left: 0;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.48s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.48s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(6) {\r\n    top: -10.22726px;\r\n    left: -10.22726px;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.6s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.6s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(7) {\r\n    top: 0;\r\n    left: -15px;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.72s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.72s infinite linear; }\r\n  .__wc_loading__ > div:nth-child(8) {\r\n    top: 10.22726px;\r\n    left: -10.22726px;\r\n    -webkit-animation: __wc_loading_animation__ 1s 0.84s infinite linear;\r\n            animation: __wc_loading_animation__ 1s 0.84s infinite linear; }\r\n  .__wc_loading__ > div {\r\n    background-color: #d3d3d3;\r\n    width: 10px;\r\n    height: 10px;\r\n    border-radius: 100%;\r\n    margin: 2px;\r\n    -webkit-animation-fill-mode: both;\r\n            animation-fill-mode: both;\r\n    position: absolute; \r\n  }",""])},
/* 4 */
/***/function(t,e){
/*
		MIT License http://www.opensource.org/licenses/mit-license.php
		Author Tobias Koppers @sokra
	*/
// css base code, injected by the css-loader
t.exports=function(){var r=[];
// return the list of modules as css string
return r.toString=function(){for(var t=[],e=0;e<this.length;e++){var i=this[e];i[2]?t.push("@media "+i[2]+"{"+i[1]+"}"):t.push(i[1])}return t.join("")},
// import a list of modules into the list
r.i=function(t,e){"string"==typeof t&&(t=[[null,t,""]]);for(var i={},n=0;n<this.length;n++){var a=this[n][0];"number"==typeof a&&(i[a]=!0)}for(n=0;n<t.length;n++){var o=t[n];
// skip already imported module
// this implementation is not 100% perfect for weird media query combinations
//  when a module is imported multiple times with different media queries.
//  I hope this will never occur (Hey this way we have smaller bundles)
"number"==typeof o[0]&&i[o[0]]||(e&&!o[2]?o[2]=e:e&&(o[2]="("+o[2]+") and ("+e+")"),r.push(o))}},r};
/***/},
/* 5 */
/***/function(t,e,i){function n(t){var e;return function(){return e=void 0===e?t.apply(this,arguments):e}}
/*
		MIT License http://www.opensource.org/licenses/mit-license.php
		Author Tobias Koppers @sokra
	*/
var l={},a=n(function(){return/msie [6-9]\b/.test(self.navigator.userAgent.toLowerCase())}),o=n(function(){return document.head||document.getElementsByTagName("head")[0]}),s=null,d=0,r=[];function c(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=l[n.id];if(a){a.refs++;for(var o=0;o<a.parts.length;o++)a.parts[o](n.parts[o]);for(;o<n.parts.length;o++)a.parts.push(_(n.parts[o],e))}else{for(var r=[],o=0;o<n.parts.length;o++)r.push(_(n.parts[o],e));l[n.id]={id:n.id,refs:1,parts:r}}}}function h(t){for(var e=[],i={},n=0;n<t.length;n++){var a=t[n],o=a[0],a={css:a[1],media:a[2],sourceMap:a[3]};i[o]?i[o].parts.push(a):e.push(i[o]={id:o,parts:[a]})}return e}function f(t,e){var i=o(),n=r[r.length-1];if("top"===t.insertAt)n?n.nextSibling?i.insertBefore(e,n.nextSibling):i.appendChild(e):i.insertBefore(e,i.firstChild),r.push(e);else{if("bottom"!==t.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");i.appendChild(e)}}function u(t){t.parentNode.removeChild(t);t=r.indexOf(t);0<=t&&r.splice(t,1)}function p(t){var e=document.createElement("style");return e.type="text/css",f(t,e),e}function _(e,t){var i,n,a,o,r;return a=t.singleton?(o=d++,i=s=s||p(t),n=w.bind(null,i,o,!1),w.bind(null,i,o,!0)):e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(o=t,(r=document.createElement("link")).rel="stylesheet",f(o,r),i=r,n=function(t,e){var i=e.css,e=e.sourceMap;e&&(
// http://stackoverflow.com/a/26603875
i+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */");e=new Blob([i],{type:"text/css"}),i=t.href;t.href=URL.createObjectURL(e),i&&URL.revokeObjectURL(i)}
/***/.bind(null,i),function(){u(i),i.href&&URL.revokeObjectURL(i.href)}):(i=p(t),n=function(t,e){var i=e.css,e=e.media;e&&t.setAttribute("media",e);if(t.styleSheet)t.styleSheet.cssText=i;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(i))}}.bind(null,i),function(){u(i)}),n(e),function(t){t?t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap||n(e=t):a()}}t.exports=function(t,r){
// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>
// tags it will allow on a page
void 0===(r=r||{}).singleton&&(r.singleton=a()),
// By default, add <style> tags to the bottom of <head>.
void 0===r.insertAt&&(r.insertAt="bottom");var s=h(t);return c(s,r),function(t){for(var e=[],i=0;i<s.length;i++){var n=s[i];(a=l[n.id]).refs--,e.push(a)}t&&c(h(t),r);for(var a,i=0;i<e.length;i++)if(0===(a=e[i]).refs){for(var o=0;o<a.parts.length;o++)a.parts[o]();delete l[a.id]}}};m=[];var m,g=function(t,e){return m[t]=e,m.filter(Boolean).join("\n")};function w(t,e,i,n){var i=i?"":n.css;t.styleSheet?t.styleSheet.cssText=g(e,i):(n=document.createTextNode(i),(i=t.childNodes)[e]&&t.removeChild(i[e]),i.length?t.insertBefore(n,i[e]):t.appendChild(n))}},
/* 6 */
/***/function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});for(var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i={},n=["Null","Undefined","Number","Boolean","String","Object","Function","Array","RegExp","Date"],a=0;a<n.length;a++)i["[object "+n[a]+"]"]=n[a].toLowerCase();function l(t){return i[Object.prototype.toString.call(t)]||"Object"}e.default={copy:function(t,e){if(null===t||"object"!=(void 0===t?"undefined":s(t)))return t;o="array";var i,n,a,o,r=l(t)===o?[]:{};for(i in t)a=l(n=t[i]),r[i]=!e||"array"!==a&&"object"!==a?n:this.copy(n);return r}}}
/******/],a={},
/******/ // expose the modules object (__webpack_modules__)
/******/i.m=n,
/******/ // expose the module cache
/******/i.c=a,
/******/ // __webpack_public_path__
/******/i.p="",i(0);
/******/ // The require function
/******/function i(t){
/******/ // Check if module is in cache
/******/var e;
/******/ // Create a new module (and put it into the cache)
/******/return(a[t]||(
/******/ // Return the exports of the module
/******/e=a[t]={
/******/exports:{},
/******/id:t,
/******/loaded:!1
/******/},
/******/ // Execute the module function
/******/n[t].call(e.exports,e,e.exports,i),
/******/ // Flag the module as loaded
/******/e.loaded=!0,e
/******/)).exports}var n,a;
/************************************************************************/
/******/});