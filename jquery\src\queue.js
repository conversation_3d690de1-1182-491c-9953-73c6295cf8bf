define(["./core","./data/var/dataPriv","./deferred","./callbacks"],function(f,a){"use strict";return f.extend({queue:function(e,u,t){var r;if(e)return r=a.get(e,u=(u||"fx")+"queue"),
// Speed up dequeue by getting out quickly if this is just a lookup
t&&(!r||Array.isArray(t)?r=a.access(e,u,f.makeArray(t)):r.push(t)),r||[]},dequeue:function(e,u){u=u||"fx";var t=f.queue(e,u),r=t.length,n=t.shift(),i=f._queueHooks(e,u);
// If the fx queue is dequeued, always remove the progress sentinel
"inprogress"===n&&(n=t.shift(),r--),n&&(
// Add a progress sentinel to prevent the fx queue from being
// automatically dequeued
"fx"===u&&t.unshift("inprogress"),
// Clear up the last queue stop function
delete i.stop,n.call(e,function(){f.dequeue(e,u)},i)),!r&&i&&i.empty.fire()},
// Not public - generate a queueHooks object, or return the current one
_queueHooks:function(e,u){var t=u+"queueHooks";return a.get(e,t)||a.access(e,t,{empty:f.Callbacks("once memory").add(function(){a.remove(e,[u+"queue",t])})})}}),f.fn.extend({queue:function(u,t){var e=2;return"string"!=typeof u&&(t=u,u="fx",e--),arguments.length<e?f.queue(this[0],u):void 0===t?this:this.each(function(){var e=f.queue(this,u,t);
// Ensure a hooks for this queue
f._queueHooks(this,u),"fx"===u&&"inprogress"!==e[0]&&f.dequeue(this,u)})},dequeue:function(e){return this.each(function(){f.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},
// Get a promise resolved when queues of a certain type
// are emptied (fx is the type by default)
promise:function(e,u){function t(){--n||i.resolveWith(s,[s])}var r,n=1,i=f.Deferred(),s=this,o=this.length;for("string"!=typeof e&&(u=e,e=void 0),e=e||"fx";o--;)(r=a.get(s[o],e+"queueHooks"))&&r.empty&&(n++,r.empty.add(t));return t(),i.promise(u)}}),f});