define(["../var/document","../var/support"],function(e,t){"use strict";var a,c;return a=e.createElement("input"),c=e.createElement("select").appendChild(e.createElement("option")),a.type="checkbox",
// Support: Android <=4.3 only
// Default value for a checkbox should be "on"
t.checkOn=""!==a.value,
// Support: IE <=11 only
// Must access selectedIndex to make default options select
t.optSelected=c.selected,(
// Support: IE <=11 only
// An input loses its value after becoming a radio
a=e.createElement("input")).value="t",a.type="radio",t.radioValue="t"===a.value,t});