/*! Sizzle v2.3.6 | (c) JS Foundation and other contributors | js.foundation */
!function(n){function f(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function M(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}function P(){C()}var e,d,w,i,z,p,F,k,N,a,c,C,x,r,E,h,o,u,g,A="sizzle"+ +new Date,s=n.document,S=0,O=0,j=B(),G=B(),U=B(),m=B(),V=function(e,t){return e===t&&(c=!0),0},X={}.hasOwnProperty,t=[],J=t.pop,K=t.push,D=t.push,Q=t.slice,v=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},W="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",l="[\\x20\\t\\r\\n\\f]",y="(?:\\\\[\\da-fA-F]{1,6}"+l+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",Y="\\["+l+"*("+y+")(?:"+l+"*([*^$|!~]?=)"+l+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+y+"))|)"+l+"*\\]",Z=":("+y+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+Y+")*)|.*)\\)|)",_=new RegExp(l+"+","g"),b=new RegExp("^"+l+"+|((?:^|[^\\\\])(?:\\\\.)*)"+l+"+$","g"),ee=new RegExp("^"+l+"*,"+l+"*"),te=new RegExp("^"+l+"*([>+~]|"+l+")"+l+"*"),ne=new RegExp(l+"|>"),re=new RegExp(Z),oe=new RegExp("^"+y+"$"),T={ID:new RegExp("^#("+y+")"),CLASS:new RegExp("^\\.("+y+")"),TAG:new RegExp("^("+y+"|[*])"),ATTR:new RegExp("^"+Y),PSEUDO:new RegExp("^"+Z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+l+"*(even|odd|(([+-]|)(\\d*)n|)"+l+"*(?:([+-]|)"+l+"*(\\d+)|))"+l+"*\\)|)","i"),bool:new RegExp("^(?:"+W+")$","i"),needsContext:new RegExp("^"+l+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+l+"*((?:-\\d)?\\d*)"+l+"*\\)|)(?=[^-]|$)","i")},ie=/HTML$/i,ue=/^(?:input|select|textarea|button)$/i,le=/^h\d$/i,L=/^[^{]+\{\s*\[native \w/,ae=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ce=/[+~]/,q=new RegExp("\\\\[\\da-fA-F]{1,6}"+l+"?|\\\\([^\\r\\n\\f])","g"),se=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,fe=ve(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{D.apply(t=Q.call(s.childNodes),s.childNodes),t[s.childNodes.length].nodeType}catch(n){D={apply:t.length?function(e,t){K.apply(e,Q.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function I(e,t,n,r){var o,i,u,l,a,c,s=t&&t.ownerDocument,f=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return n;if(!r&&(C(t),t=t||x,E)){if(11!==f&&(l=ae.exec(e)))if(o=l[1]){if(9===f){if(!(c=t.getElementById(o)))return n;if(c.id===o)return n.push(c),n}else if(s&&(c=s.getElementById(o))&&g(t,c)&&c.id===o)return n.push(c),n}else{if(l[2])return D.apply(n,t.getElementsByTagName(e)),n;if((o=l[3])&&d.getElementsByClassName&&t.getElementsByClassName)return D.apply(n,t.getElementsByClassName(o)),n}if(d.qsa&&!m[e+" "]&&(!h||!h.test(e))&&(1!==f||"object"!==t.nodeName.toLowerCase())){if(c=e,s=t,1===f&&(ne.test(e)||te.test(e))){for((s=ce.test(e)&&ge(t.parentNode)||t)===t&&d.scope||((u=t.getAttribute("id"))?u=u.replace(se,M):t.setAttribute("id",u=A)),i=(a=p(e)).length;i--;)a[i]=(u?"#"+u:":scope")+" "+ye(a[i]);c=a.join(",")}try{return D.apply(n,s.querySelectorAll(c)),n}catch(t){m(e,!0)}finally{u===A&&t.removeAttribute("id")}}}return k(e.replace(b,"$1"),t,n,r)}function B(){var n=[];function r(e,t){return n.push(e+" ")>w.cacheLength&&delete r[n.shift()],r[e+" "]=t}return r}function R(e){return e[A]=!0,e}function $(e){var t=x.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function de(e,t){for(var n=e.split("|"),r=n.length;r--;)w.attrHandle[n[r]]=t}function pe(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function he(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&fe(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function H(u){return R(function(i){return i=+i,R(function(e,t){for(var n,r=u([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in d=I.support={},z=I.isXML=function(e){var t=e&&e.namespaceURI,e=e&&(e.ownerDocument||e).documentElement;return!ie.test(t||e&&e.nodeName||"HTML")},C=I.setDocument=function(e){var e=e?e.ownerDocument||e:s;return e!=x&&9===e.nodeType&&e.documentElement&&(r=(x=e).documentElement,E=!z(x),s!=x&&(e=x.defaultView)&&e.top!==e&&(e.addEventListener?e.addEventListener("unload",P,!1):e.attachEvent&&e.attachEvent("onunload",P)),d.scope=$(function(e){return r.appendChild(e).appendChild(x.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),d.attributes=$(function(e){return e.className="i",!e.getAttribute("className")}),d.getElementsByTagName=$(function(e){return e.appendChild(x.createComment("")),!e.getElementsByTagName("*").length}),d.getElementsByClassName=L.test(x.getElementsByClassName),d.getById=$(function(e){return r.appendChild(e).id=A,!x.getElementsByName||!x.getElementsByName(A).length}),d.getById?(w.filter.ID=function(e){var t=e.replace(q,f);return function(e){return e.getAttribute("id")===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&E)return(t=t.getElementById(e))?[t]:[]}):(w.filter.ID=function(e){var t=e.replace(q,f);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&E){var n,r,o,i=t.getElementById(e);if(i){if((n=i.getAttributeNode("id"))&&n.value===e)return[i];for(o=t.getElementsByName(e),r=0;i=o[r++];)if((n=i.getAttributeNode("id"))&&n.value===e)return[i]}return[]}}),w.find.TAG=d.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):d.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"!==e)return i;for(;n=i[o++];)1===n.nodeType&&r.push(n);return r},w.find.CLASS=d.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&E)return t.getElementsByClassName(e)},o=[],h=[],(d.qsa=L.test(x.querySelectorAll))&&($(function(e){var t;r.appendChild(e).innerHTML="<a id='"+A+"'></a><select id='"+A+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&h.push("[*^$]="+l+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||h.push("\\["+l+"*(?:value|"+W+")"),e.querySelectorAll("[id~="+A+"-]").length||h.push("~="),(t=x.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||h.push("\\["+l+"*name"+l+"*="+l+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||h.push(":checked"),e.querySelectorAll("a#"+A+"+*").length||h.push(".#.+[+~]"),e.querySelectorAll("\\\f"),h.push("[\\r\\n\\f]")}),$(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=x.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&h.push("name"+l+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&h.push(":enabled",":disabled"),r.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),h.push(",.*:")})),(d.matchesSelector=L.test(u=r.matches||r.webkitMatchesSelector||r.mozMatchesSelector||r.oMatchesSelector||r.msMatchesSelector))&&$(function(e){d.disconnectedMatch=u.call(e,"*"),u.call(e,"[s!='']:x"),o.push("!=",Z)}),h=h.length&&new RegExp(h.join("|")),o=o.length&&new RegExp(o.join("|")),e=L.test(r.compareDocumentPosition),g=e||L.test(r.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},V=e?function(e,t){var n;return e===t?(c=!0,0):!e.compareDocumentPosition-!t.compareDocumentPosition||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?e==x||e.ownerDocument==s&&g(s,e)?-1:t==x||t.ownerDocument==s&&g(s,t)?1:a?v(a,e)-v(a,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,u=[e],l=[t];if(!o||!i)return e==x?-1:t==x?1:o?-1:i?1:a?v(a,e)-v(a,t):0;if(o===i)return pe(e,t);for(n=e;n=n.parentNode;)u.unshift(n);for(n=t;n=n.parentNode;)l.unshift(n);for(;u[r]===l[r];)r++;return r?pe(u[r],l[r]):u[r]==s?-1:l[r]==s?1:0}),x},I.matches=function(e,t){return I(e,null,null,t)},I.matchesSelector=function(e,t){if(C(e),d.matchesSelector&&E&&!m[t+" "]&&(!o||!o.test(t))&&(!h||!h.test(t)))try{var n=u.call(e,t);if(n||d.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){m(t,!0)}return 0<I(t,x,null,[e]).length},I.contains=function(e,t){return(e.ownerDocument||e)!=x&&C(e),g(e,t)},I.attr=function(e,t){(e.ownerDocument||e)!=x&&C(e);var n=w.attrHandle[t.toLowerCase()],n=n&&X.call(w.attrHandle,t.toLowerCase())?n(e,t,!E):void 0;return void 0!==n?n:d.attributes||!E?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},I.escape=function(e){return(e+"").replace(se,M)},I.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},I.uniqueSort=function(e){var t,n=[],r=0,o=0;if(c=!d.detectDuplicates,a=!d.sortStable&&e.slice(0),e.sort(V),c){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return a=null,e},i=I.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=i(t);return n},(w=I.selectors={cacheLength:50,createPseudo:R,match:T,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(q,f),e[3]=(e[3]||e[4]||e[5]||"").replace(q,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||I.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&I.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return T.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&re.test(n)&&(t=(t=p(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(q,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=j[e+" "];return t||(t=new RegExp("(^|"+l+")"+e+"("+l+"|$)"))&&j(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=I.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e.replace(_," ")+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(h,e,t,g,m){var y="nth"!==h.slice(0,3),v="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,u,l,a,c=y!=v?"nextSibling":"previousSibling",s=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b,p=!1;if(s){if(y){for(;c;){for(u=e;u=u[c];)if(b?u.nodeName.toLowerCase()===f:1===u.nodeType)return!1;a=c="only"===h&&!a&&"nextSibling"}return!0}if(a=[v?s.firstChild:s.lastChild],v&&d){for(p=(l=(r=(o=(i=(u=s)[A]||(u[A]={}))[u.uniqueID]||(i[u.uniqueID]={}))[h]||[])[0]===S&&r[1])&&r[2],u=l&&s.childNodes[l];u=++l&&u&&u[c]||(p=l=0,a.pop());)if(1===u.nodeType&&++p&&u===e){o[h]=[S,l,p];break}}else if(!1===(p=d?l=(r=(o=(i=(u=e)[A]||(u[A]={}))[u.uniqueID]||(i[u.uniqueID]={}))[h]||[])[0]===S&&r[1]:p))for(;(u=++l&&u&&u[c]||(p=l=0,a.pop()))&&((b?u.nodeName.toLowerCase()!==f:1!==u.nodeType)||!++p||(d&&((o=(i=u[A]||(u[A]={}))[u.uniqueID]||(i[u.uniqueID]={}))[h]=[S,p]),u!==e)););return(p-=m)===g||p%g==0&&0<=p/g}}},PSEUDO:function(e,i){var t,u=w.pseudos[e]||w.setFilters[e.toLowerCase()]||I.error("unsupported pseudo: "+e);return u[A]?u(i):1<u.length?(t=[e,e,"",i],w.setFilters.hasOwnProperty(e.toLowerCase())?R(function(e,t){for(var n,r=u(e,i),o=r.length;o--;)e[n=v(e,r[o])]=!(t[n]=r[o])}):function(e){return u(e,0,t)}):u}},pseudos:{not:R(function(e){var r=[],o=[],l=F(e.replace(b,"$1"));return l[A]?R(function(e,t,n,r){for(var o,i=l(e,null,r,[]),u=e.length;u--;)(o=i[u])&&(e[u]=!(t[u]=o))}):function(e,t,n){return r[0]=e,l(r,null,n,o),r[0]=null,!o.pop()}}),has:R(function(t){return function(e){return 0<I(t,e).length}}),contains:R(function(t){return t=t.replace(q,f),function(e){return-1<(e.textContent||i(e)).indexOf(t)}}),lang:R(function(n){return oe.test(n||"")||I.error("unsupported lang: "+n),n=n.replace(q,f).toLowerCase(),function(e){var t;do{if(t=E?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===x.activeElement&&(!x.hasFocus||x.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:he(!1),disabled:he(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},header:function(e){return le.test(e.nodeName)},input:function(e){return ue.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:H(function(){return[0]}),last:H(function(e,t){return[t-1]}),eq:H(function(e,t,n){return[n<0?n+t:n]}),even:H(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:H(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:H(function(e,t,n){for(var r=n<0?n+t:t<n?t:n;0<=--r;)e.push(r);return e}),gt:H(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})w.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function ye(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function ve(u,e,t){var l=e.dir,a=e.next,c=a||l,s=t&&"parentNode"===c,f=O++;return e.first?function(e,t,n){for(;e=e[l];)if(1===e.nodeType||s)return u(e,t,n);return!1}:function(e,t,n){var r,o,i=[S,f];if(n){for(;e=e[l];)if((1===e.nodeType||s)&&u(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||s)if(o=(o=e[A]||(e[A]={}))[e.uniqueID]||(o[e.uniqueID]={}),a&&a===e.nodeName.toLowerCase())e=e[l]||e;else{if((r=o[c])&&r[0]===S&&r[1]===f)return i[2]=r[2];if((o[c]=i)[2]=u(e,t,n))return!0}return!1}}function be(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function we(e,t,n,r,o){for(var i,u=[],l=0,a=e.length,c=null!=t;l<a;l++)!(i=e[l])||n&&!n(i,r,o)||(u.push(i),c&&t.push(l));return u}function Ne(p,h,g,m,y,e){return m&&!m[A]&&(m=Ne(m)),y&&!y[A]&&(y=Ne(y,e)),R(function(e,t,n,r){var o,i,u,l=[],a=[],c=t.length,s=e||function(e,t,n){for(var r=0,o=t.length;r<o;r++)I(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),f=!p||!e&&h?s:we(s,l,p,n,r),d=g?y||(e?p:c||m)?[]:t:f;if(g&&g(f,d,n,r),m)for(o=we(d,a),m(o,[],n,r),i=o.length;i--;)(u=o[i])&&(d[a[i]]=!(f[a[i]]=u));if(e){if(y||p){if(y){for(o=[],i=d.length;i--;)(u=d[i])&&o.push(f[i]=u);y(null,d=[],o,r)}for(i=d.length;i--;)(u=d[i])&&-1<(o=y?v(e,u):l[i])&&(e[o]=!(t[o]=u))}}else d=we(d===t?d.splice(c,d.length):d),y?y(null,t,d,r):D.apply(t,d)})}function Ce(m,y){function e(e,t,n,r,o){var i,u,l,a=0,c="0",s=e&&[],f=[],d=N,p=e||b&&w.find.TAG("*",o),h=S+=null==d?1:Math.random()||.1,g=p.length;for(o&&(N=t==x||t||o);c!==g&&null!=(i=p[c]);c++){if(b&&i){for(u=0,t||i.ownerDocument==x||(C(i),n=!E);l=m[u++];)if(l(i,t||x,n)){r.push(i);break}o&&(S=h)}v&&((i=!l&&i)&&a--,e)&&s.push(i)}if(a+=c,v&&c!==a){for(u=0;l=y[u++];)l(s,f,t,n);if(e){if(0<a)for(;c--;)s[c]||f[c]||(f[c]=J.call(r));f=we(f)}D.apply(r,f),o&&!e&&0<f.length&&1<a+y.length&&I.uniqueSort(r)}return o&&(S=h,N=d),s}var v=0<y.length,b=0<m.length;return v?R(e):e}me.prototype=w.filters=w.pseudos,w.setFilters=new me,p=I.tokenize=function(e,t){var n,r,o,i,u,l,a,c=G[e+" "];if(c)return t?0:c.slice(0);for(u=e,l=[],a=w.preFilter;u;){for(i in n&&!(r=ee.exec(u))||(r&&(u=u.slice(r[0].length)||u),l.push(o=[])),n=!1,(r=te.exec(u))&&(n=r.shift(),o.push({value:n,type:r[0].replace(b," ")}),u=u.slice(n.length)),w.filter)!(r=T[i].exec(u))||a[i]&&!(r=a[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),u=u.slice(n.length));if(!n)break}return t?u.length:u?I.error(e):G(e,l).slice(0)},F=I.compile=function(e,t){var n,r=[],o=[],i=U[e+" "];if(!i){for(n=(t=t||p(e)).length;n--;)((i=function e(t){for(var r,n,o,i=t.length,u=w.relative[t[0].type],l=u||w.relative[" "],a=u?1:0,c=ve(function(e){return e===r},l,!0),s=ve(function(e){return-1<v(r,e)},l,!0),f=[function(e,t,n){return e=!u&&(n||t!==N)||((r=t).nodeType?c:s)(e,t,n),r=null,e}];a<i;a++)if(n=w.relative[t[a].type])f=[ve(be(f),n)];else{if((n=w.filter[t[a].type].apply(null,t[a].matches))[A]){for(o=++a;o<i&&!w.relative[t[o].type];o++);return Ne(1<a&&be(f),1<a&&ye(t.slice(0,a-1).concat({value:" "===t[a-2].type?"*":""})).replace(b,"$1"),n,a<o&&e(t.slice(a,o)),o<i&&e(t=t.slice(o)),o<i&&ye(t))}f.push(n)}return be(f)}(t[n]))[A]?r:o).push(i);(i=U(e,Ce(o,r))).selector=e}return i},k=I.select=function(e,t,n,r){var o,i,u,l,a,c="function"==typeof e&&e,s=!r&&p(e=c.selector||e);if(n=n||[],1===s.length){if(2<(i=s[0]=s[0].slice(0)).length&&"ID"===(u=i[0]).type&&9===t.nodeType&&E&&w.relative[i[1].type]){if(!(t=(w.find.ID(u.matches[0].replace(q,f),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=T.needsContext.test(e)?0:i.length;o--&&(u=i[o],!w.relative[l=u.type]);)if((a=w.find[l])&&(r=a(u.matches[0].replace(q,f),ce.test(i[0].type)&&ge(t.parentNode)||t))){if(i.splice(o,1),e=r.length&&ye(i))break;return D.apply(n,r),n}}return(c||F(e,s))(r,t,!E,n,!t||ce.test(e)&&ge(t.parentNode)||t),n},d.sortStable=A.split("").sort(V).join("")===A,d.detectDuplicates=!!c,C(),d.sortDetached=$(function(e){return 1&e.compareDocumentPosition(x.createElement("fieldset"))}),$(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),d.attributes&&$(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),$(function(e){return null==e.getAttribute("disabled")})||de(W,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null});var xe=n.Sizzle;I.noConflict=function(){return n.Sizzle===I&&(n.Sizzle=xe),I},"function"==typeof define&&define.amd?define(function(){return I}):"undefined"!=typeof module&&module.exports?module.exports=I:n.Sizzle=I}(window);
//# sourceMappingURL=sizzle.min.map