/* global Symbol */
// Defining this global in .eslintrc.json would create a danger of using the global
// unguarded in another place, it seems safer to define global only for this module
define(["./var/arr","./var/getProto","./var/slice","./var/flat","./var/push","./var/indexOf","./var/class2type","./var/toString","./var/hasOwn","./var/fnToString","./var/ObjectFunctionString","./var/support","./var/isFunction","./var/isWindow","./core/DOMEval","./core/toType"],function(t,n,r,c,e,o,i,u,a,l,s,f,h,p,g,v){"use strict";function
// Define a local copy of jQuery
y(t,n){
// The jQuery object is actually just the init constructor 'enhanced'
// Need init if jQuery is called (just allow error to be thrown if not included)
return new y.fn.init(t,n)}var b="3.6.0";function d(t){
// Support: real iOS 8.2 only (not reproducible in simulator)
// `in` check used to prevent JIT error (gh-2145)
// hasOwn isn't used here due to false negatives
// regarding Nodelist length in IE
var n=!!t&&"length"in t&&t.length,r=v(t);return!h(t)&&!p(t)&&("array"===r||0===n||"number"==typeof n&&0<n&&n-1 in t)}return y.fn=y.prototype={
// The current version of jQuery being used
jquery:b,constructor:y,
// The default length of a jQuery object is 0
length:0,toArray:function(){return r.call(this)},
// Get the Nth element in the matched element set OR
// Get the whole matched element set as a clean array
get:function(t){
// Return all the elements in a clean array
return null==t?r.call(this):t<0?this[t+this.length]:this[t];
// Return just the one element from the set
},
// Take an array of elements and push it onto the stack
// (returning the new matched element set)
pushStack:function(t){
// Build a new jQuery matched element set
t=y.merge(this.constructor(),t);
// Add the old object onto the stack (as a reference)
// Return the newly-formed element set
return t.prevObject=this,t},
// Execute a callback for every element in the matched set.
each:function(t){return y.each(this,t)},map:function(r){return this.pushStack(y.map(this,function(t,n){return r.call(t,n,t)}))},slice:function(){return this.pushStack(r.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(y.grep(this,function(t,n){return(n+1)%2}))},odd:function(){return this.pushStack(y.grep(this,function(t,n){return n%2}))},eq:function(t){var n=this.length,t=+t+(t<0?n:0);return this.pushStack(0<=t&&t<n?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},
// For internal use only.
// Behaves like an Array's method, not like a jQuery method.
push:e,sort:t.sort,splice:t.splice},y.extend=y.fn.extend=function(){var t,n,r,e,o,i=arguments[0]||{},u=1,c=arguments.length,a=!1;
// Handle a deep copy situation
for("boolean"==typeof i&&(a=i,
// Skip the boolean and the target
i=arguments[u]||{},u++),
// Handle case when target is a string or something (possible in deep copy)
"object"==typeof i||h(i)||(i={}),
// Extend jQuery itself if only one argument is passed
u===c&&(i=this,u--);u<c;u++)
// Only deal with non-null/undefined values
if(null!=(t=arguments[u]))
// Extend the base object
for(n in t)r=t[n],
// Prevent Object.prototype pollution
// Prevent never-ending loop
"__proto__"!==n&&i!==r&&(
// Recurse if we're merging plain objects or arrays
a&&r&&(y.isPlainObject(r)||(e=Array.isArray(r)))?(o=i[n],
// Ensure proper type for the source value
o=e&&!Array.isArray(o)?[]:e||y.isPlainObject(o)?o:{},e=!1,
// Never move original objects, clone them
i[n]=y.extend(a,o,r)):void 0!==r&&(i[n]=r));
// Return the modified object
return i},y.extend({
// Unique for each copy of jQuery on the page
expando:"jQuery"+(b+Math.random()).replace(/\D/g,""),
// Assume jQuery is ready without the ready module
isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){
// Detect obvious negatives
// Use toString instead of jQuery.type to catch host objects
return!(!t||"[object Object]"!==u.call(t)||
// Objects with no prototype (e.g., `Object.create( null )`) are plain
(t=n(t))&&("function"!=typeof(
// Objects with prototype are plain iff they were constructed by a global Object function
t=a.call(t,"constructor")&&t.constructor)||l.call(t)!==s))},isEmptyObject:function(t){for(var n in t)return!1;return!0},
// Evaluates a script in a provided context; falls back to the global one
// if not specified.
globalEval:function(t,n,r){g(t,{nonce:n&&n.nonce},r)},each:function(t,n){var r,e=0;if(d(t))for(r=t.length;e<r&&!1!==n.call(t[e],e,t[e]);e++);else for(e in t)if(!1===n.call(t[e],e,t[e]))break;return t},
// results is for internal usage only
makeArray:function(t,n){n=n||[];return null!=t&&(d(Object(t))?y.merge(n,"string"==typeof t?[t]:t):e.call(n,t)),n},inArray:function(t,n,r){return null==n?-1:o.call(n,t,r)},
// Support: Android <=4.0 only, PhantomJS 1 only
// push.apply(_, arraylike) throws on ancient WebKit
merge:function(t,n){for(var r=+n.length,e=0,o=t.length;e<r;e++)t[o++]=n[e];return t.length=o,t},grep:function(t,n,r){
// Go through the array, only saving the items
// that pass the validator function
for(var e=[],o=0,i=t.length,u=!r;o<i;o++)!n(t[o],o)!=u&&e.push(t[o]);return e},
// arg is for internal usage only
map:function(t,n,r){var e,o,i=0,u=[];
// Go through the array, translating each of the items to their new values
if(d(t))for(e=t.length;i<e;i++)null!=(o=n(t[i],i,r))&&u.push(o);
// Go through every key on the object,
else for(i in t)null!=(o=n(t[i],i,r))&&u.push(o);
// Flatten any nested arrays
return c(u)},
// A global GUID counter for objects
guid:1,
// jQuery.support is not used in Core but other projects attach their
// properties to it so it needs to exist.
support:f}),"function"==typeof Symbol&&(y.fn[Symbol.iterator]=t[Symbol.iterator]),
// Populate the class2type map
y.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,n){i["[object "+n+"]"]=n.toLowerCase()}),y});