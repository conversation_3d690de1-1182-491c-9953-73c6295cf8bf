define(["../core","../var/isFunction","./var/nonce","./var/rquery","../ajax"],function(p,c,a,l){"use strict";var i=[],u=/(=)\?(?=&|$)|\?\?/;
// Default jsonp settings
p.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var n=i.pop()||p.expando+"_"+a.guid++;return this[n]=!0,n}}),
// Detect, normalize options and install callbacks for jsonp requests
p.ajaxPrefilter("json jsonp",function(n,a,o){var r,t,e,s=!1!==n.jsonp&&(u.test(n.url)?"url":"string"==typeof n.data&&0===(n.contentType||"").indexOf("application/x-www-form-urlencoded")&&u.test(n.data)&&"data");
// Handle iff the expected data type is "jsonp" or we have a parameter to set
if(s||"jsonp"===n.dataTypes[0])
// Delegate to script
// Get callback name, remembering preexisting value associated with it
return r=n.jsonpCallback=c(n.jsonpCallback)?n.jsonpCallback():n.jsonpCallback,
// Insert callback into url or form data
s?n[s]=n[s].replace(u,"$1"+r):!1!==n.jsonp&&(n.url+=(l.test(n.url)?"&":"?")+n.jsonp+"="+r),
// Use data converter to retrieve json after script execution
n.converters["script json"]=function(){return e||p.error(r+" was not called"),e[0]},
// Force json dataType
n.dataTypes[0]="json",
// Install callback
t=window[r],window[r]=function(){e=arguments},
// Clean-up function (fires after converters)
o.always(function(){
// If previous value didn't exist - remove it
void 0===t?p(window).removeProp(r):window[r]=t,
// Save back as free
n[r]&&(
// Make sure that re-using the options doesn't screw things around
n.jsonpCallback=a.jsonpCallback,
// Save the callback name for future use
i.push(r)),
// Call if it was a function and we have a response
e&&c(t)&&t(e[0]),e=t=void 0}),"script"})});