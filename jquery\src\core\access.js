define(["../core","../core/toType","../var/isFunction"],function(a,s,v){"use strict";
// Multifunctional method to get and set values of a collection
// The value/s can optionally be executed if it's a function
function d(n,l,e,r,c,t,i){var o=0,u=n.length,f=null==e;
// Sets many values
if("object"===s(e))for(o in c=!0,e)d(n,l,o,e[o],!0,t,i);
// Sets one value
else if(void 0!==r&&(c=!0,v(r)||(i=!0),l=f?
// Bulk operations run against the entire set
i?(l.call(n,r),null):(f=l,function(n,l,e){return f.call(a(n),e)}):l))for(;o<u;o++)l(n[o],e,i?r:r.call(n[o],o,l(n[o],e)));return c?n:
// Gets
f?l.call(n):u?l(n[0],e):t}return d});