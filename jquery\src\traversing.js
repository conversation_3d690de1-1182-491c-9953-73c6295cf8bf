define(["./core","./var/getProto","./var/indexOf","./traversing/var/dir","./traversing/var/siblings","./traversing/var/rneedsContext","./core/nodeName","./core/init","./traversing/findFilter","./selector"],function(s,t,e,r,i,c,o){"use strict";var u=/^(?:parents|prev(?:Until|All))/,
// Methods guaranteed to produce a unique set when starting from a unique set
l={children:!0,contents:!0,next:!0,prev:!0};function f(n,t){for(;(n=n[t])&&1!==n.nodeType;);return n}return s.fn.extend({has:function(n){var t=s(n,this),e=t.length;return this.filter(function(){for(var n=0;n<e;n++)if(s.contains(this,t[n]))return!0})},closest:function(n,t){var e,r=0,i=this.length,o=[],u="string"!=typeof n&&s(n);
// Positional selectors never match, since there's no _selection_ context
if(!c.test(n))for(;r<i;r++)for(e=this[r];e&&e!==t;e=e.parentNode)
// Always skip document fragments
if(e.nodeType<11&&(u?-1<u.index(e):
// Don't pass non-elements to Sizzle
1===e.nodeType&&s.find.matchesSelector(e,n))){o.push(e);break}return this.pushStack(1<o.length?s.uniqueSort(o):o)},
// Determine the position of an element within the set
index:function(n){
// No argument, return index in parent
return n?
// Index in selector
"string"==typeof n?e.call(s(n),this[0]):e.call(this,
// If it receives a jQuery object, the first element is used
n.jquery?n[0]:n):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(n,t){return this.pushStack(s.uniqueSort(s.merge(this.get(),s(n,t))))},addBack:function(n){return this.add(null==n?this.prevObject:this.prevObject.filter(n))}}),s.each({parent:function(n){n=n.parentNode;return n&&11!==n.nodeType?n:null},parents:function(n){return r(n,"parentNode")},parentsUntil:function(n,t,e){return r(n,"parentNode",e)},next:function(n){return f(n,"nextSibling")},prev:function(n){return f(n,"previousSibling")},nextAll:function(n){return r(n,"nextSibling")},prevAll:function(n){return r(n,"previousSibling")},nextUntil:function(n,t,e){return r(n,"nextSibling",e)},prevUntil:function(n,t,e){return r(n,"previousSibling",e)},siblings:function(n){return i((n.parentNode||{}).firstChild,n)},children:function(n){return i(n.firstChild)},contents:function(n){return null!=n.contentDocument&&
// Support: IE 11+
// <object> elements with no `data` attribute has an object
// `contentDocument` with a `null` prototype.
t(n.contentDocument)?n.contentDocument:(
// Support: IE 9 - 11 only, iOS 7 only, Android Browser <=4.3 only
// Treat the template element as a regular one in browsers that
// don't support it.
o(n,"template")&&(n=n.content||n),s.merge([],n.childNodes))}},function(r,i){s.fn[r]=function(n,t){var e=s.map(this,i,n);return(t="Until"!==r.slice(-5)?n:t)&&"string"==typeof t&&(e=s.filter(t,e)),1<this.length&&(
// Remove duplicates
l[r]||s.uniqueSort(e),u.test(r))&&e.reverse(),this.pushStack(e)}}),s});