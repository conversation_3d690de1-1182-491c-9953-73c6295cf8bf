define(["../core"],function(o){"use strict";var
// Map over jQuery in case of overwrite
w=window.jQuery,
// Map over the $ in case of overwrite
i=window.$;o.noConflict=function(n){return window.$===o&&(window.$=i),n&&window.jQuery===o&&(window.jQuery=w),o},
// Expose jQuery and $ identifiers, even in AMD
// (#7102#comment:10, https://github.com/jquery/jquery/pull/557)
// and CommonJS for browser emulators (#13566)
"undefined"==typeof noGlobal&&(window.jQuery=window.$=o)});