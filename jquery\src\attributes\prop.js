define(["../core","../core/access","./support","../selector"],function(p,n,e){"use strict";var o=/^(?:input|select|textarea|button)$/i,r=/^(?:a|area)$/i;p.fn.extend({prop:function(e,t){return n(this,p.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[p.propFix[e]||e]})}}),p.extend({prop:function(e,t,n){var o,r,a=e.nodeType;
// Don't get/set properties on text, comment and attribute nodes
if(3!==a&&8!==a&&2!==a)return 1===a&&p.isXMLDoc(e)||(
// Fix name and attach hooks
t=p.propFix[t]||t,r=p.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(o=r.set(e,n,t))?o:e[t]=n:r&&"get"in r&&null!==(o=r.get(e,t))?o:e[t]},propHooks:{tabIndex:{get:function(e){
// Support: IE <=9 - 11 only
// elem.tabIndex doesn't always return the
// correct value when it hasn't been explicitly set
// https://web.archive.org/web/20141116233347/http://fluidproject.org/blog/2008/01/09/getting-setting-and-removing-tabindex-values-with-javascript/
// Use proper attribute retrieval(#12072)
var t=p.find.attr(e,"tabindex");return t?parseInt(t,10):o.test(e.nodeName)||r.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),
// Support: IE <=11 only
// Accessing the selectedIndex property
// forces the browser to respect setting selected
// on the option
// The getter ensures a default option is selected
// when in an optgroup
// eslint rule "no-unused-expressions" is disabled for this code
// since it considers such accessions noop
e.optSelected||(p.propHooks.selected={get:function(e){
/* eslint no-unused-expressions: "off" */
e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){
/* eslint no-unused-expressions: "off" */
e=e.parentNode;e&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),p.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){p.propFix[this.toLowerCase()]=this})});