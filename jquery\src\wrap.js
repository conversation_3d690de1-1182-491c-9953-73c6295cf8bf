define(["./core","./var/isFunction","./core/init","./manipulation",// clone
"./traversing"],function(e,r){"use strict";return e.fn.extend({wrapAll:function(t){return this[0]&&(r(t)&&(t=t.call(this[0])),
// The elements to wrap the target around
t=e(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(i){return r(i)?this.each(function(t){e(this).wrapInner(i.call(this,t))}):this.each(function(){var t=e(this),n=t.contents();n.length?n.wrapAll(i):t.append(i)})},wrap:function(n){var i=r(n);return this.each(function(t){e(this).wrapAll(i?n.call(this,t):n)})},unwrap:function(t){return this.parent(t).not("body").each(function(){e(this).replaceWith(this.childNodes)}),this}}),e});