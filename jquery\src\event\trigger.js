define(["../core","../var/document","../data/var/dataPriv","../data/var/acceptData","../var/hasOwn","../var/isFunction","../var/isWindow","../event"],function(f,y,m,w,h,x,b){"use strict";function P(e){e.stopPropagation()}var E=/^(?:focusinfocus|focusoutblur)$/;return f.extend(f.event,{trigger:function(e,t,n,r){var a,i,p,o,s,u,l,d=[n||y],g=h.call(e,"type")?e.type:e,c=h.call(e,"namespace")?e.namespace.split("."):[],v=l=i=n=n||y;
// Don't do events on text and comment nodes
if(3!==n.nodeType&&8!==n.nodeType&&!E.test(g+f.event.triggered)&&(-1<g.indexOf(".")&&(
// Namespaced trigger; create a regexp to match event type in handle()
g=(c=g.split(".")).shift(),c.sort()),o=g.indexOf(":")<0&&"on"+g,
// Trigger bitmask: & 1 for native handlers; & 2 for jQuery (always true)
(
// Caller can pass in a jQuery.Event object, Object, or just an event type string
e=e[f.expando]?e:new f.Event(g,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=c.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+c.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,
// Clean up the event in case it is being reused
e.result=void 0,e.target||(e.target=n),
// Clone any incoming data and prepend the event, creating the handler arg list
t=null==t?[e]:f.makeArray(t,[e]),
// Allow special events to draw outside the lines
u=f.event.special[g]||{},r||!u.trigger||!1!==u.trigger.apply(n,t))){
// Determine event propagation path in advance, per W3C events spec (#9951)
// Bubble up to document, then to window; watch for a global ownerDocument var (#9724)
if(!r&&!u.noBubble&&!b(n)){for(p=u.delegateType||g,E.test(p+g)||(v=v.parentNode);v;v=v.parentNode)d.push(v),i=v;
// Only add window if we got to document (e.g., not plain obj or detached DOM)
i===(n.ownerDocument||y)&&d.push(i.defaultView||i.parentWindow||window)}
// Fire handlers on the event path
for(a=0;(v=d[a++])&&!e.isPropagationStopped();)l=v,e.type=1<a?p:u.bindType||g,(
// jQuery handler
s=(m.get(v,"events")||Object.create(null))[e.type]&&m.get(v,"handle"))&&s.apply(v,t),(
// Native handler
s=o&&v[o])&&s.apply&&w(v)&&(e.result=s.apply(v,t),!1===e.result)&&e.preventDefault();return e.type=g,
// If nobody prevented the default action, do it now
r||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(d.pop(),t)||!w(n)||
// Call a native DOM method on the target with the same name as the event.
// Don't do default actions on window, that's where global variables be (#6170)
o&&x(n[g])&&!b(n)&&(
// Don't re-trigger an onFOO event when we call its FOO() method
(i=n[o])&&(n[o]=null),
// Prevent re-triggering of the same event, since we already bubbled it above
f.event.triggered=g,e.isPropagationStopped()&&l.addEventListener(g,P),n[g](),e.isPropagationStopped()&&l.removeEventListener(g,P),f.event.triggered=void 0,i)&&(n[o]=i),e.result}
// focus/blur morphs to focusin/out; ensure we're not firing them right now
},
// Piggyback on a donor event to simulate a different one
// Used only for `focus(in | out)` events
simulate:function(e,t,n){n=f.extend(new f.Event,n,{type:e,isSimulated:!0});f.event.trigger(n,null,t)}}),f.fn.extend({trigger:function(e,t){return this.each(function(){f.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return f.event.trigger(e,t,n,!0)}}),f});