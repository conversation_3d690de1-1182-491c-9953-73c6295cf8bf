define(["../core","../queue","../effects"],function(n){"use strict";
// Based off of the plugin by <PERSON>, with permission.
// https://web.archive.org/web/20100324014747/http://blindsignals.com/index.php/2009/07/jquery-delay/
return n.fn.delay=function(u,e){return u=n.fx&&n.fx.speeds[u]||u,this.queue(e=e||"fx",function(e,n){var t=window.setTimeout(e,u);n.stop=function(){window.clearTimeout(t)}})},n.fn.delay});