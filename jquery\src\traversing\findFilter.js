define(["../core","../var/indexOf","../var/isFunction","./var/rneedsContext","../selector"],function(u,n,i,e){"use strict";
// Implement the identical functionality for filter and not
function r(t,e,r){return i(e)?u.grep(t,function(t,n){return!!e.call(t,n,t)!==r}):
// Single element
e.nodeType?u.grep(t,function(t){return t===e!==r}):
// Arraylike of elements (jQuery, arguments, Array)
"string"!=typeof e?u.grep(t,function(t){return-1<n.call(e,t)!==r}):u.filter(e,t,r)}u.filter=function(t,n,e){var r=n[0];return e&&(t=":not("+t+")"),1===n.length&&1===r.nodeType?u.find.matchesSelector(r,t)?[r]:[]:u.find.matches(t,u.grep(n,function(t){return 1===t.nodeType}))},u.fn.extend({find:function(t){var n,e,r=this.length,i=this;if("string"!=typeof t)return this.pushStack(u(t).filter(function(){for(n=0;n<r;n++)if(u.contains(i[n],this))return!0}));for(e=this.pushStack([]),n=0;n<r;n++)u.find(t,i[n],e);return 1<r?u.uniqueSort(e):e},filter:function(t){return this.pushStack(r(this,t||[],!1))},not:function(t){return this.pushStack(r(this,t||[],!0))},is:function(t){return!!r(this,
// If this is a positional/relative selector, check membership in the returned set
// so $("p:first").is("p:last") won't return true for a doc with two "p".
"string"==typeof t&&e.test(t)?u(t):t||[],!1).length}})});