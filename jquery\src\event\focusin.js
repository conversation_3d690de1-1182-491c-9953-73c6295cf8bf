define(["../core","../data/var/dataPriv","./support","../event","./trigger"],function(t,i,e){"use strict";
// Support: Firefox <=44
// Firefox doesn't have focus(in | out) events
// Related ticket - https://bugzilla.mozilla.org/show_bug.cgi?id=687787
//
// Support: Chrome <=48 - 49, Safari <=9.0 - 9.1
// focus(in | out) events fire after focus & blur events,
// which is spec violation - http://www.w3.org/TR/DOM-Level-3-Events/#events-focusevent-event-order
// Related ticket - https://bugs.chromium.org/p/chromium/issues/detail?id=449857
return e.focusin||t.each({focus:"focusin",blur:"focusout"},function(n,s){function c(e){t.event.simulate(s,e.target,t.event.fix(e))}t.event.special[s]={setup:function(){
// Handle: regular nodes (via `this.ownerDocument`), window
// (via `this.document`) & document (via `this`).
var e=this.ownerDocument||this.document||this,t=i.access(e,s);t||e.addEventListener(n,c,!0),i.access(e,s,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=i.access(e,s)-1;t?i.access(e,s,t):(e.removeEventListener(n,c,!0),i.remove(e,s))}}}),t});