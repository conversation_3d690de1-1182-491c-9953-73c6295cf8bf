define(["./core","./core/nodeName","./core/camelCase","./core/toType","./var/isFunction","./var/isWindow","./var/slice","./deprecated/ajax-event-alias","./deprecated/event"],function(i,e,r,a,t,n,c){"use strict";
// Support: Android <=4.0 only
// Make sure we trim BOM and NBSP
var o=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;
// Bind a function to a context, optionally partially applying any
// arguments.
// jQuery.proxy is deprecated to promote standards (specifically Function#bind)
// However, it is not slated for removal any time soon
i.proxy=function(e,r){var a,n;
// Quick check to determine if target is callable, in the spec
// this throws a TypeError, but we will just return undefined.
if("string"==typeof r&&(n=e[r],r=e,e=n),t(e))
// Simulated bind
return a=c.call(arguments,2),
// Set the guid of unique handler to the same of original handler, so it can be removed
(n=function(){return e.apply(r||this,a.concat(c.call(arguments)))}).guid=e.guid=e.guid||i.guid++,n},i.holdReady=function(e){e?i.readyWait++:i.ready(!0)},i.isArray=Array.isArray,i.parseJSON=JSON.parse,i.nodeName=e,i.isFunction=t,i.isWindow=n,i.camelCase=r,i.type=a,i.now=Date.now,i.isNumeric=function(e){
// As of jQuery 3.0, isNumeric is limited to
// strings and numbers (primitives or objects)
// that can be coerced to finite numbers (gh-2662)
var r=i.type(e);return("number"===r||"string"===r)&&
// parseFloat NaNs numeric-cast false positives ("")
// ...but misinterprets leading-number strings, particularly hex literals ("0x...")
// subtraction forces infinities to NaN
!isNaN(e-parseFloat(e))},i.trim=function(e){return null==e?"":(e+"").replace(o,"")}});