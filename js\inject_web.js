// 该文件是插入到页面中的，所有的插件页面点击事件的方法都在这里触发
//-------------各个事件方法------------------begin
function keyword_table_colse(){$("#ixspy-keyword-table").remove()}
//1.选中标签的事件,工具栏上对应的菜单入口
function web_choseItemMneu(e,t,n){-1!==window.location.pathname.indexOf("/store/")&&"store"!=n&&"more"!=n&&"my_favted"!=n&&"my_persion"!=n||("more"!==n&&($("#inject-hide").nextAll().remove(),$("div").removeClass("inject-active"),$("div").removeClass("is-active"),$(".hide-title-info").html(e),$("div").removeClass("border_bottom"),$(t).addClass("border_bottom"),$(".inject-show-content-div").show()),"my_favted"==n&&(//选中默认内容
$("#tag_id_value").val(0),$("#favted_type_value").val("product")),injectSendParamsToContentScript(n)}
//2. 点击收藏产品id
function web_doFavtedGoodsId(e){if(pub_isEmpty(e))alert("id is empty");else{let n=[];$(".smt_favted_goods_box .active-tags").each((e,t)=>{n.push($(t).data("value"))});var e={productId:e,tags:n},t=$("#collection_goods").text()+"......";$("#collection_goods").html(t),injectSendParamsToContentScript("",e,cmd="collection_goods",type="web_event")}}
//打开收藏标签选择页
function web_favtedGoodsId(e){if(pub_isEmpty(e))alert("id is empty");else if(!(0<$(".smt_favted_goods_box").length)){if($(".smt_already_collect_"+e).hasClass("smt_already_collect"))
//取消收藏
return injectSendParamsToContentScript("",{goods_id:e},cmd="remove_product",type="web_event"),!1;var t=$("#collection_goods").text()+"......";$("#collection_goods").html(t),
// injectSendParamsToContentScript('', params, cmd = 'collection_goods', type = 'web_event')
injectSendParamsToContentScript("",{productId:e},cmd="show_collection_box",type="web_event")}}
//关闭收藏操作
function cancelCollect(){0<$(".smt_favted_goods_box").length&&$(".smt_favted_goods_box").remove()}function toIxspy(e){window.open(e)}function autoSales(){document.getElementById("toggleSale").addEventListener("change",function(){this.checked?(
// 执行开启操作
injectSendParamsToContentScript("",{value:1},cmd="sales_auto",type="web_event"),localStorage.setItem("smt_sales_auto",1),$(".temp-seven-data").css("display","block")):(
// 执行关闭操作
injectSendParamsToContentScript("",{value:0},cmd="sales_auto",type="web_event"),localStorage.setItem("smt_sales_auto",0),$(".temp-seven-data").css("display","none"))})}
//3. 产品图表数据的 line 和 bar 的切换 (增量和总量的切换)
function choseProductChartType(e,t){params={},injectSendParamsToContentScript("",params=(("all-total"==e||"all-inc"==e?(
// $("[class*='-total-button']").length
$("[class*='-total-button']").removeClass("btn-active"),$("[class*='-inc-button']").removeClass("btn-active"),"all-total"==e?$("[class*='-total-button']"):$("[class*='-inc-button']")):(("inc"==t?$("."+e+"-total-button"):$("."+e+"-inc-button")).removeClass("btn-active"),$("."+e+"-"+t+"-button"))).addClass("btn-active"),{id:e,type:t}),cmd="draw_product_chart",t="web_event")}
//4.店铺图表数据 line 和 bar 的切换choseStoreChartType(增量和总量的切换)
function choseStoreChartType(e,t){("inc"==t?$("."+e+"-total-button"):$("."+e+"-inc-button")).removeClass("btn-active"),$("."+e+"-"+t+"-button").addClass("btn-active"),injectSendParamsToContentScript("",{id:e,type:t},cmd="draw_store_chart",t="web_event")}
//5.隐藏显示内容的模块 （工具栏的展示框）
function hideShowContent(){$(".inject-show-content-div").hide()}function cancelLogin(){$(".login-k-r").remove()}
//6. 点击获取shopify 的竞品信息
function web_clickCompetitor(e,t){var n;pub_isEmpty(e)||pub_isEmpty(t)?alert("id is empty"):(n=$("#found_shopify").text()+"......",$("#found_shopify").html(n),injectSendParamsToContentScript("",{product_id:e,source_product_id:t},cmd="request_shopify_goods",type="web_event"))}
//7.获取到对应下载内容传入到 content_script 再传到 background 去下载
function downloadImgOrVideo(e){var t,n=[];switch(e){case"mods--sumImageWrap--FTNV4nk":console.error("mods--sumImageWrap--FTNV4nk"),$(".mods--sumImageWrap--FTNV4nk img").each(function(){console.error($(this).attr("src"));var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"images-view-list":$(".images-view-list img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"slider--item--FefNjlj":$(".slider--item--FefNjlj img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"sku-property-list":$(".sku-property-image img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"sku-property-list-new":$(".sku-item--image--mXsHo3h img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"sku-item--image--jMUnnGA":$(".sku-item--image--jMUnnGA img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"over_view":$(".tab-content div:first img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"over_view-new":0!=$(".detail-desc-decorate-richtext img").length?$(".detail-desc-decorate-richtext img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):$("#product-description img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"video-wrap":n=$(".video-wrap video").attr("src");break;case"Product_GalleryBarItem__barItem__11qng":var a=$($("figure").next()[0]).next()[0];$(a).find("img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"Product_SkuValueBaseItem__item__o90dx":$('[class*="Product_Sku__container"] img').each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"detailmodule_text-image":0<$(".detail-desc-decorate-richtext img").length?$(".detail-desc-decorate-richtext img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):0<$("[data-spm='quick_shift_tab'] img").length&&$("[data-spm='quick_shift_tab'] img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"Product_GalleryVideo__video__9fbpv":$(".Product_GalleryVideo__video__9fbpv").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"images-div-list-ru":$(".SnowProductGallery_SnowProductGallery__container__zm9pu img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"images-div-list-ru-new":if(0<$(".SnowProductGallery_SnowProductGallery__galleryContainer__jy810 img").length)$(".SnowProductGallery_SnowProductGallery__galleryContainer__jy810 img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});else if(0<$(".SnowProductGallery_SnowProductGallery__previews__jy810 img").length){a=$(this).attr("src");if(-1===a.indexOf("http"))return;n.push(a)}break;case"images-div-list-ru-v9f35":0<$(".SnowProductGallery_SnowProductGallery__previews__v9f35 img").length?$(".SnowProductGallery_SnowProductGallery__previews__v9f35 img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):0<$(".SnowProductGallery_SnowProductGallery__previews__jy810 img").length?$(".SnowProductGallery_SnowProductGallery__previews__jy810 img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):0<$(".gallery_Gallery__picListWrapper__crhgwn.SnowProductGallery_SnowProductGallery__galleryWrapper__1ryr7 .gallery_Gallery__image__crhgwn").length?$(".gallery_Gallery__picListWrapper__crhgwn.SnowProductGallery_SnowProductGallery__galleryWrapper__1ryr7 .gallery_Gallery__image__crhgwn").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):0<$(".SnowProductGallery_SnowProductGallery__previewItem__z6imb img").length?$(".SnowProductGallery_SnowProductGallery__previewItem__z6imb img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):0<$(".SnowProductGallery_SnowProductGallery__previewItem__xeihu img").length&&$(".SnowProductGallery_SnowProductGallery__previewItem__xeihu img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"images-div-sku-ru":$("#ixspy_sku_image img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"images-div-sku-ru-new":$("#ixspy_sku_image img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"images-div-desc-ru":$(".SnowProductContent_SnowProductContent__content__1vco7 img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"video-div-list-ru":$(".SnowProductGallery_SnowProductGallery__container__zm9pu video").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"video-div-list-ru-v9f35":0<$('div[class="gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn"] video').length?(
//视频默认一条
$('div[class="gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn"] video').each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}),1==n.length&&(n=n[0])):0<$('div[class="gallery_Gallery__picture__15bdcj gallery_Gallery__imgIsLoading__15bdcj"] video').length&&(
//视频默认一条
$('div[class="gallery_Gallery__picture__15bdcj gallery_Gallery__imgIsLoading__15bdcj"] video').each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}),1==n.length)&&(n=n[0]);break;case"video-div-list-ru-new":0<$(".gallery_Gallery__video__re6q0q video").length?$(".gallery_Gallery__video__re6q0q video").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):$(".gallery_Gallery__video__15bdcj video")&&$(".gallery_Gallery__video__15bdcj video").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"scription-div-list-ru":$(".SnowProductContent_SnowProductContent__content__1t9gm img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"scription-div-list-ru-new":0<$(".SnowProductContent_SnowProductContent__content__1dttw img").length?$(".SnowProductContent_SnowProductContent__content__1dttw img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):$(".SnowProductContent_SnowProductContent__content__1thry img").length?$(".SnowProductContent_SnowProductContent__content__1thry img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):$("#content_anchor img").length&&$("#content_anchor img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"slider-div-box-img-list":$(".slider--img--D7MJNPZ img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;case"new-class-donwload-video":n=$(".video--wrap--NfR8r9l > video > source").attr("src");break;case"video--wrap--EhkqzuR":n=$(".video--wrap--EhkqzuR > video > source").attr("src");break;case"descript-img-list-download":0<$("#nav-description img").length?$("#nav-description img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}):$("#product-description img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)});break;default:0<$(e).length&&(0<$(e).find("video").length?n=$(e).find("source").attr("src"):$(e).each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&n.push(e)}))}""==n||null==n||0==n.length?alert("无法获取到下载资源"):(t=(t=(
// var imgBase64 = [];
// var imageSuffix = [];//图片后缀
// var zip = new JSZip();
// var img = zip.folder("images");
// for(var i=0;i<objUrls.length;i++){
// 	var src = objUrls[i].replace("_.webp","")
// 	var suffix = src.substring(src.lastIndexOf("."));
// 	imageSuffix.push(suffix);
//     console.log(src)
// 	 getBase64(src)
//         .then(function(base64){
//         	imgBase64.push(base64.replace(/^data:image\/(png|jpg|jpeg);base64,/, ""))
//             console.log(base64)
//             if(objUrls.length == imgBase64.length){
//                 for(var i=0;i<objUrls.length;i++){
//                     img.file(i+imageSuffix[i], imgBase64[i], {base64: true});
//                 }
//                 console.log(zip.generateAsync)
//                 zip.generateAsync({type:"blob"}).then(function(content) {
//                     console.log(content)
//                     saveAs(content, "image.zip")
//                 })
//             }
//         },function(err){
//               console.log(err);//打印异常信息
//         });       
// }
t=-1!==location.href.indexOf("aliexpress.ru/")?(t=""===(t=0<$(".HazeProductDescription_HazeProductDescription__root__1bnud").find("h1").eq(0).length?$(".HazeProductDescription_HazeProductDescription__root__1bnud").find("h1").eq(0).html():"")?$('h1[class="snow-ali-kit_Typography__base__1shggo snow-ali-kit_Typography-Primary__base__1xop0e snow-ali-kit_Typography__strong__1shggo snow-ali-kit_Typography__sizeHeadingL__1shggo HazeProductDescription_HazeProductDescription__name__8s9ws HazeProductDescription_HazeProductDescription__smallText__8s9ws"]').html():t)||$('h1[class^="snow-ali-kit_Typography__base__1shggo snow-ali-kit_Typography-Primary__base__1xop0e snow-ali-kit_Typography__strong__1shggo snow-ali-kit_Typography__sizeHeadingL__1shggo"]').html():$('[data-pl="product-title"]').text()).replace(/[/?*:<>|"\\]/g," ")).replace(/\uFEFF/g,""),injectSendParamsToContentScript("",{urls:n,product_name:t},"sources_download","web_event"))}
//传入图片路径，返回base64
function getBase64(e){var r=new Image,o=(r.crossOrigin="Anonymous",r.src=e,$.Deferred());if(e)return r.onload=function(){var e,t,n,a;o.resolve((e=r,(a=document.createElement("canvas")).width=t||e.width,a.height=n||e.height,a.getContext("2d").drawImage(e,0,0,a.width,a.height),a.toDataURL()))},o.promise()}
//12 图片放大功能
function clickBigImg(e){$("#big-img img").attr("src",e),1==localStorage.getItem("smt_turn")?$("#big-img").css({display:"block",right:"-288px"}):$("#big-img").css({display:"block",left:"-288px"})}function clearCookie(){injectSendParamsToContentScript("","","remove_cookie","web_event")}function toWeChatGroup(){$(".maskwechat").css("display","block")}function cancelMask(){$(".maskwechat").css("display","none")}
//13. 图片移开
function removeBigImg(){$("#big-img").css("display","none")}function loginTip(){injectSendParamsToContentScript("","","login_tip","web_event")}
//14 鼠标移开提示框
function tipMouseout(e,t,n){
// var res= $(self).hasClass("border_bottom")
// console.log(res)
$("#hover_"+e).attr("src",t),$("#text_"+e).css("color","#fff")}function dialoupdateout(e){$(".endupdate-"+e).css("display","none")}function dialogupdateover(e){$(".endupdate-"+e).css("display","block")}
//15 鼠标移到提示框
function tipMouseover(e,t){$("#hover_"+e).attr("src",t),$("#text_"+e).css("color","#0076f1")}function tipTurnOver(e,t){var n=localStorage.getItem("smt_turn"),a=$(".tools-width").width();0==n?(145==a?$(".hover_turn_"+t):$(".hover_turn_"+e)).css({display:"block",left:"-44px",right:""}):(145==a?($(".hover_turn_"+t).css({display:"block",left:"",right:"-44px"}),$(".hover_turn_"+e)):($(".hover_turn_"+e).css({display:"block",left:"",right:"-44px"}),$(".hover_turn_"+t))).css({display:"none"})}function tipTurnOut(e,t){(145==$(".tools-width").width()?($(".hover_turn_"+t).css("display","none"),$(".hover_turn_"+e)):($(".hover_turn_"+e).css("display","none"),$(".hover_turn_"+t))).css("display","none")}function tipLeft(e,t){0==localStorage.getItem("smt_turn")?$(".hover_turn_"+e).css({display:"none",left:"",right:"-44px"}):$(".hover_turn_"+t).css("display","none")}function tipRight(e,t){0==localStorage.getItem("smt_turn")?$(".hover_turn_"+e).css({display:"block",left:"-44px",right:""}):$(".hover_turn_"+t).css({display:"block",left:"",right:"-44px"})}function turnLeft(){var e=$(".tools-width").width(),t=$("body").width()-(e+20);$(".inject-button").css({right:t}),$("#turn-left").css("display","none"),$("#turn-right").css("display","block"),$(".hover_turn_left").css("display","none"),610==e?($("#open-left").css("display","block"),$("#open-right").css("display","none")):($("#open-left").css("display","none"),$("#open-right").css("display","block")),$(".imgDock").css({right:"-35px",left:""}),$(".imgOpen").css({right:"-35px",left:""}),injectSendParamsToContentScript("",{value:1},cmd="check_turn",type="web_event"),localStorage.setItem("smt_turn","1")}function turnRight(){var e=$(".tools-width").width();$(".inject-button").css({right:"20px",left:""}),$("#turn-left").css("display","block"),$("#turn-right").css("display","none"),$(".hover_turn_right").css("display","none"),610==e?($("#open-left").css("display","none"),$("#open-right").css("display","block")):($("#open-left").css("display","block"),$("#open-right").css("display","none")),$(".imgDock").css({left:"-35px",right:""}),$(".imgOpen").css({left:"-35px",right:""}),injectSendParamsToContentScript("",{value:0},cmd="check_turn",type="web_event"),localStorage.setItem("smt_turn","0")}function hide(){var e=$("body").width()-180;$(".hide-all").css("display","none"),$(".tools-width").animate({width:"145"},"fast"),$(".text-ixspy").css({top:"10px",marginLeft:"15px"}),$(".inject-show-content-div").css("display","none"),$("div").removeClass("border_bottom"),0==localStorage.getItem("smt_turn")?($("#open-left").css("display","block"),$("#open-right").css("display","none")):($("#open-left").css("display","none"),$("#open-right").css("display","block"),$(".inject-button").css("right",e)),document.getElementById("my_btn")&&(document.getElementById("my_btn").href="javascript:show_smt();")}function hidefast(){var e=$("body").width()-180;$(".hide-all").css("display","none"),$(".tools-width").css("width","145"),$(".text-ixspy").css({top:"10px",marginLeft:"15px"}),$(".inject-show-content-div").css("display","none"),$("div").removeClass("border_bottom"),0==localStorage.getItem("smt_turn")?($("#open-left").css("display","block"),$("#open-right").css("display","none")):($("#open-left").css("display","none"),$("#open-right").css("display","block"),$(".inject-button").css("right",e)),document.getElementById("my_btn")&&(document.getElementById("my_btn").href="javascript:show_smt();")}function show_smt(){var e=$("body").width()-630;$(".hide-all").css("display","inline-block"),$(".tools-width").animate({width:"610"},"slow"),$(".text-ixspy").css({top:"-20px",marginLeft:"5px"}),$(".hover_turn_open").css("display","none"),$(".hover_turn_stow").css("display","none"),0==localStorage.getItem("smt_turn")?($("#open-left").css("display","none"),$("#open-right").css("display","block")):($("#open-left").css("display","block"),$("#open-right").css("display","none"),$(".inject-button").css("right",e)),document.getElementById("my_btn").href="javascript:hide();"}
//16.关闭提示模态框
function closeUpdateTip(){$(".update-tip").remove(),$(".update-tip").css("height","0"),injectSendParamsToContentScript("",null,"need_update_close","web_event")}
//17.主动检查是否更新
function checkUpgrade(){injectSendParamsToContentScript("",null,"self_check_update","web_event")}
//19 关闭消息通知，并且标记获取的消息为已读
function closeNotify(){$("body").append("<input type='hidden' value='close' id='notifyClose' />"),$(".site-notify").remove(),injectSendParamsToContentScript("",{id:$("#notifyIds").val()},"hidden_notify","web_event")}
//20. 通知获取消息
function getNotify(){
//先判断是否已经获取了
1!=$("#notifyClose").length&&(console.log("获取消息通知1"),injectSendParamsToContentScript("",{},"get_notify","web_event"))}
//21. 获取一整年的数据
function getYearChart(e,t,n="",a=""){"store_trade_chart"==e?($(".store_trade-inc-button").removeClass("btn-active"),$(".store_trade-total-button").addClass("btn-active")):"store_wishlis_chart"==e?($(".store_wishlis-inc-button").removeClass("btn-active"),$(".store_wishlis-total-button").addClass("btn-active")):"store_reviews_chart"==e?($(".store_reviews-inc-button").removeClass("btn-active"),$(".store_reviews-total-button").addClass("btn-active")):"price_chart"==e?($(".price-inc-button").removeClass("btn-active"),$(".price-total-button").addClass("btn-active")):"reviews_chart"==e?($(".reviews-inc-button").removeClass("btn-active"),$(".reviews-total-button").addClass("btn-active")):"trade_chart"==e?($(".trade-inc-button").removeClass("btn-active"),$(".trade-total-button").addClass("btn-active")):"wishlis_chart"==e?($(".wishlis-inc-button").removeClass("btn-active"),$(".wishlis-total-button").addClass("btn-active")):"order_chart"==e&&($(".order-inc-button").removeClass("btn-active"),$(".order-total-button").addClass("btn-active"));var r,o=0;0<$("[rel='canonical']").length&&(r=$("[rel='canonical']").attr("href"),o=getProductsId(r)),injectSendParamsToContentScript("",{flag:e,type:t,product_id:a,store_id:n,x_object_id:o},cmd="request_year_chart",t="web_event")}
//22.统计数据
function TjData(){window.location.pathname;var e=getInjProductDiv(),n=[];if(0==e.length){var t=$(".temp-seven-data");0<t.length&&(t.each((e,t)=>{null!=$(t).attr("id")&&(t=$(t).attr("id").split("-id-")[1],n.push(t))}),0<n.length)&&injectSendParamsToContentScript("",{ids:n},cmd="request_tj_chart",type="web_event")}else{for(var a in e)isNaN(a)||""!=(a=eachGetAttribute(e[a],"product_a_href"))&&""!=(a=getProductId(a))&&n.push(a);t=getProductLinkId(!0);injectSendParamsToContentScript("",{ids:n=0<t.length?t:n},cmd="request_tj_chart",type="web_event")}}
//23.点击某个产品看图表数据
function clickSmallChart(e,t){try{t.preventDefault(),t.stopPropagation()}catch(e){
//console.log(error)
}
// var id = 'small-chart-' + product_id
// $("#" + id).remove()
// var div_id = 'seven-data-product-' + product_id
// $("." + div_id).remove()
// $(".sales-"+product_id).not(":first").remove()
$(".seven-data-product-"+e).css("display","block");var t={product_id:e},n="<div style='height:50px;width:100%;max-width:258px' id='small-chart-"+e+"'></div>",a=e+"_h3",n=(0==$("#small-chart-"+e).length&&$("."+a).append(n),
//console.log($("."+className).prev()[0])
$($("."+a).prev()).css("height","auto"),getProductLinkIdByDetailId(e));t.x_object_id=n,injectSendParamsToContentScript("",t,cmd="request_product_chart",type="web_event")}
//24.关闭统计框
function closeTj(){$(".dialog_tj").remove()}
//25. 添加产品收藏
function favted_products(e,t){if(t.preventDefault(),t.stopPropagation(),$(".favted_"+e).hasClass("smt_already_collect"))
//取消收藏
return injectSendParamsToContentScript("",{goods_id:e},cmd="remove_product",type="web_event"),!1;
// injectSendParamsToContentScript('', params, cmd = 'request_favted_favted', type = 'web_event')
injectSendParamsToContentScript("",{productId:e},cmd="show_collection_box",type="web_event")}
//25. 添加店铺收藏
function favtedShop(e,t){t.preventDefault(),t.stopPropagation(),
// console.log(id)
injectSendParamsToContentScript("",{store_id:e=null!=window.runParams?("undefined"==window.runParams.data.commonModule||null==window.runParams.data.commonModule||null==window.runParams.data.commonModule?window.runParams.data.storeModule:window.runParams.data.commonModule).sellerAdminSeq:e},cmd="request_favted_store",type="web_event")}
//18. 自动展开勾选事件
function checkBoxClick(){var e;"checked"==$("input[name='checkname']").attr("checked")?($("input[name='checkname']").attr("checked",!1),e={flag:2},$(".inject-show-content-div").css("display","none"),console.log(123)):($("input[name='checkname']").attr("checked",!0),e={flag:1}),injectSendParamsToContentScript("",e,"show_auto","web_event")}
//查看当前产品的更多图表信息
function lookMoreChart(e,t){try{t.preventDefault(),t.stopPropagation()}catch(e){}injectSendParamsToContentScript("",{product_id:$(e).attr("value").split("-")[2]},"show_small_chart_more","web_event")}
//loading
function appendFontLoading(){var e=pub_format(String(function(){
/*!  
            <div class="alert alert-primary smt-alert" role="alert">
                Loading...
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),{});$("body").append(e)}
//判断获取产品的div
function getInjProductDiv(){var e=localStorage.getItem("smt_config_tj_class"),e=(e=JSON.parse(e)).product_div,r=[];return e.forEach(e=>{var t,n,a,e=$(e);0!=e.length&&(a=n=t="",t=eachGetAttribute(e[0],"product_a_href"),n=eachGetAttribute(e[0],"shop_a_href"),a=getPosition(e[0],"store_product_order"),""!=t&&-1!==t.indexOf("/item/")&&""!=n&&-1!==n.indexOf("/store/")||""!=t&&-1!==t.indexOf("/item/")&&""!=a)&&!(0<r.length)&&(r=e);
//查询是否有 产品的链接 和 店铺的链接
}),0===r.length&&(e=$(".self-my-icon").parent().parent(),-1!==location.href.indexOf(".ru/")&&(e=$(".self-my-icon").parent()),r=e),r}
// 判断是否存在该 class 或者标志位
function getPosition(t,e){var n=localStorage.getItem("smt_config_tj_class"),n=(n=JSON.parse(n))[e],a="";return n.forEach(e=>{0<$(t).find(e).length&&""==a&&(a=e)}),a}
//遍历获取属性根据class
function eachGetAttribute(r,o,e=""){var t=localStorage.getItem("smt_config_tj_class"),t=(t=JSON.parse(t))[o],i="",s="";return t.forEach(e=>{var t="",n=(
//分割属性，判断得到的属性是什么
-1!==(e=e.replaceAll("/\\/","")).indexOf("|")&&(e=(n=e.split("|"))[0],t=n[1]),"");if("self"==e){if(null!=(n=$(r).attr(t))&&""!=n&&null!=n){if("shop_a_href"==o&&-1===n.indexOf("/store/"))return;if("product_a_href"==o&&-1===n.indexOf("/item/"))return;i=n}}else{var a=$(r).find(e);0!=a.length&&null!=(n=$(a[0]).attr(t))&&""!=n&&null!=n&&""==i&&(i=n,s=e)}}),""==i&&(s=""),"attr"==e?s:i}
//根据 href 获取 产品id
function getProductId(e){var t,n=e.split("/"),a="";for(t in n){var r=n[t];-1!==r.indexOf("html")&&(a=parseInt(r))}return a}
//关闭事件通知
function closeEventNotify(){$(".event-notify-extention").remove()}
//获取收藏的id
function getFavtedId(){setTimeout(()=>{injectSendParamsToContentScript("",{},cmd="request_get_favted_ids",type="web_event")},2e3)}
//28.关闭登陆框
function closeLogin(){$(".login-k-r").remove()}
//模拟登录成功
function LoginSuccess() {
    $(".login-k-r").remove();
    // 不需要刷新页面
}
//点击相同产品 显示出来
function showSameProduct(){injectSendParamsToContentScript("",{},"show_same_product","web_event")}
//关闭相同产品的模态框
function closeSameProduct(){$(".dialog-product-show").remove(),$(".dialog-show").remove()}
//获取收藏的店铺 或者是 商品
function getMyfavted(e){$("#favted_type_value").remove(),$("body").append("<input type='hidden' id='favted_type_value' value='"+e+"' />"),
//标签切换回默认
$("#tag_id_value").val(0),
//console.log('切换收藏的内容',type)
// $('.btn-default').removeClass('btn-active')
// $('.favted-'+type).addClass('btn-active')
// $('.favted-'+type).css("baclground","#000")
injectSendParamsToContentScript("",{type:e},"get_my_favted","web_event")}
//开启监控跳转到站点的收藏列表页
function openMonitor(e){window.open("https://ixspy.com/data#/mycenter/myfavted?type=aliexpress_store&store_id="+e,"_blank")}
//查看监控结果
function checkMonitor(e){injectSendParamsToContentScript("",{id:e},"check_monitor","web_event")}
//定时获取配置信息
function timerGetJg(e=180){var t=Date.parse(new Date)/1e3,n=localStorage.getItem("message_timer");return null==n||""==n?(localStorage.setItem("message_timer",t),injectSendParamsToContentScript("",{},"get_class_config",type="web_event"),getNotify(),!0):!(t-parseInt(n)<=e||(localStorage.setItem("message_timer",t),injectSendParamsToContentScript("",{},"get_class_config",type="web_event"),getNotify(),0))}
//选中标签
function clickChoseTag(e,t){$("#tag_id_value").remove(),$("body").append("<input type='hidden' id='tag_id_value' value='"+e+"' />"),$(".tags-span").removeClass("active-tags"),$(t).addClass("active-tags");
//console.log('选中标签',id)
//injectSendParamsToContentScript('',{id:id}, 'favted', type = 'web_event')
t=$("#favted_type_value").val();injectSendParamsToContentScript("",{type:t=""!=t&&null!=t?t:"product",id:e},"get_my_favted","web_event")}function clickChoseTagByCollect(e,t){$(t).hasClass("active-tags")?$(t).removeClass("active-tags"):$(t).addClass("active-tags")}
//点击长尾词进行搜索
function clickSearchWords(e){console.log("点击搜索事件长尾词",e),0==$("#search-key").length?($("[name='SearchText']").val(e),$("[name='SearchText']").attr("value",e),setTimeout(()=>{$($("form")[0]).submit()},500)):($("#search-key").val(e),$(".search-button").click())}
//获取我的搜索词
function mySearchWords(){
//console.log('获取我的长尾词')
injectSendParamsToContentScript("",{},"get_long_words","web_event")}
//下载长尾词信息
function downloadWords(){
//console.log('点击下载长尾词信息')
//获取搜索的长尾词提交到后台数据
var e=localStorage.getItem("long_words_like");if(!pub_isEmpty(e)){objInfo=JSON.parse(e);var t,n={},a="";for(r in a=(a=""!=$("input[id='search-key']").val()&&null!=$("input[id='search-key']").val()&&""!=$("input[id='search-key']").val()?$("#search-key").val():""!=$("input[id='searchInput']").val()&&null!=$("input[id='searchInput']").val()&&""!=$("input[id='searchInput']").val()?$("#searchInput").val():""!=$("input[id='SearchText']").val()&&null!=$("input[id='SearchText']").val()&&""!=$("input[id='SearchText']").val()?$("#SearchText").val():""!=$("input[id='search-words']").val()&&null!=$("input[id='search-words']").val()&&""!=$("input[id='search-words']").val()?$("#search-words").val():"search key words").replaceAll(" ","-"),objInfo)n[a]=objInfo[r];if(t=objInfo,""==a)console.log("关键词获取失败");else{
//保存当前的长尾词信息
// localStorage.setItem('long_words_like',JSON.stringify(temp))
// injectSendParamsToContentScript('', {data:JSON.stringify(temp),cmd:'long_words_add'}, cmd = 'long_words_add', type = 'web_event')
var r,o=new Array;for(r in t)if(null!=t[r].trace&&null!=t[r].trace.utLogMap){n={display_keyword:t[r].trace.utLogMap.display_keyword};o.push(n)}else if("string"!=typeof t[r])for(var i in t[r]){n={display_keyword:t[r][i].keywords};o.push(n)}else{n={display_keyword:t[r]};o.push(n)}
//下载长尾词信息
localStorage.getItem("text-2");e=localStorage.getItem("text-1"),e=(o.unshift({display_keyword:e}),XLSX.utils.json_to_sheet(o,{skipHeader:!0})),e={SheetNames:["sheet"],//保存的表标题
Sheets:{sheet:Object.assign(e,//内容
{})}};tmpDown=new Blob([s2ab(XLSX.write(e,{
// bookType can be 'xlsx' or 'xlsm' or 'xlsb'
bookType:"xlsx",bookSST:!1,// 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
type:"binary"}))],{type:""});//创建二进制对象写入转换好的字节流
const s=document.createElement("a");e=URL.createObjectURL(tmpDown);// 创建对象超链接
s.download=a+".xlsx",// 下载名称
s.style.display="none",s.href=e,// 绑定a标签
document.body.appendChild(s),s.click(),// 模拟点击实现下载
setTimeout(function(){
// 延时释放
URL.revokeObjectURL(tmpDown),// 用URL.revokeObjectURL()来释放这个object URL
document.body.removeChild(s)},100)}}}
//下载长尾词分析
function todoDownKeywords(e,t){e=JSON.stringify(e);var n,a,r=new RegExp("aaaaaabbcccccc","g"),o=(e=e.replace(r,"'"),e=JSON.parse(e),new Array);for(n in e)a=e[n].click_rate?{keywords:n,lang_category:e[n].category_id,search_popularity:e[n].search_cookie_cnt,search_index:e[n].search_cnt,click_rate:(100*e[n].click_rate).toFixed(2)+"%",order_conversion_rate:(100*e[n].trans_rate).toFixed(2)+"%",competition_index:e[n].sply_dem_rate,supply_index:e[n].supply_rate}:{keywords:n,lang_category:e[n].category_id,search_popularity:e[n].search_cookie_cnt,search_index:e[n].search_cnt,click_rate:"",order_conversion_rate:"",competition_index:e[n].sply_dem_rate,supply_index:e[n].supply_rate},o.push(a);r={keywords:t.keywords,lang_category:t.lang_category,search_popularity:t.search_popularity,search_index:t.search_index,click_rate:t.click_rate,order_conversion_rate:t.order_conversion_rate,competition_index:t.competition_index,supply_index:t.supply_index},t=t.analysis_lang_words,o.unshift(r),r=XLSX.utils.json_to_sheet(o,{skipHeader:!0}),r={SheetNames:["sheet"],//保存的表标题
Sheets:{sheet:Object.assign(r,//内容
{})}};tmpDown=new Blob([s2ab(XLSX.write(r,{
// bookType can be 'xlsx' or 'xlsm' or 'xlsb'
bookType:"xlsx",bookSST:!1,// 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
type:"binary"}))],{type:""});//创建二进制对象写入转换好的字节流
const i=document.createElement("a");r=URL.createObjectURL(tmpDown);// 创建对象超链接
i.download=t+".xlsx",// 下载名称
i.style.display="none",i.href=r,// 绑定a标签
document.body.appendChild(i),i.click(),// 模拟点击实现下载
setTimeout(function(){
// 延时释放
URL.revokeObjectURL(tmpDown),// 用URL.revokeObjectURL()来释放这个object URL
document.body.removeChild(i)},100)}function getKeywordsInfo(){
//获得关键词详细信息
injectSendParamsToContentScript("",{},"get_keywords_info","web_event")}
// 字符串转字符流
function s2ab(e){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),a=0;a!=e.length;++a)n[a]=255&e.charCodeAt(a);return t}
//查看更多的搜索词
function lookMoreWords(e){e.preventDefault(),e.stopPropagation(),injectSendParamsToContentScript("",{},cmd="more_long_words",type="web_event")}
//点击产品相关词流量
function showWordsLinPic(){injectSendParamsToContentScript("",{},cmd="show_kwds_pic",type="web_event")}
//关闭产品相关词流量
function closeShowWordsLinPic(){$(".show-keywords-pic").remove()}
//跳转到产品搜索图
function toSearchByImg(e,t,n,a){n.preventDefault(),n.stopPropagation(),0<$(a).prev('div[class="choise_image_search_type_box_hidden"]').length&&$(a).prev('div[class="choise_image_search_type_box_hidden"]').css({display:"block"})}
//产品区域定价走api
function startGetProductPriceInfoByApi(e){injectSendParamsToContentScript("",{},"append_country_k","web_event");
//injectSendParamsToContentScript('', {product_id:id}, cmd = 'prdocut_carrier_api', type = 'web_event')
}function choseAllCountry(e){
// 查看当前全选是否被选中
e=e.checked;
// 根据status来设置其他多选框的状态
$(".country-chose-input").prop("checked",e),$("#cc_RU").prop("checked",!0)}
//提交产品运费价格获取到后端
function getChoseCountryS(){var e,t=document.getElementsByName("country_groups"),n=[];for(k in t)t[k].checked&&n.push(t[k].value);localStorage.setItem("country_groups",JSON.stringify(n)),1==n.length?
//alert('请先选择采集的国家')
injectSendParamsToContentScript("",{},"need_chose_countrys","web_event"):(e=getProductsId(),
// localStorage.setItem('first_tip', 1)
// appendFontLoading()
//removeCountryGroup()
removeCountryGroup(),injectSendParamsToContentScript("",{product_id:e,country:n},cmd="prdocut_carrier_api",type="web_event"))}
//获取产品id
function getProductsId(){var e,t=window.location.pathname.split("/");for(e in t){var n=t[e];if(-1!==n.indexOf("html"))return parseInt(n)}return!1}function removeCountryGroup(){$(".chose_country_checkboxs").remove(),$(".chose_country_checkboxs_zh").remove()}
//判断是否是店铺页面,是否获取到store_id 和 真实的 storeId 的数据，上报给后端
function getRealStoreIdLink(){try{var e=window.pageConfig;
// console.log('检测是否有店铺关系隐射',params)
if(null!=e){var t=getCStoreId();if(""==t)return!1;e.url_store_id=t,injectSendParamsToContentScript("",{data:e},cmd="upload_store_id",type="web_event")}}catch(e){console.log(e,"错误的店铺关系映射")}}
//判断是否产品列表页面 和 产品详情页面，来获取产品别名id 的关系
function getProductLinkId(e=!1){try{var t=[],n=window.runParams;if(localStorage.setItem("run_params",JSON.stringify(n)),null==n||""==n||null==n)return[];if(null==n.mods||""==n.mods||null==n.mods)return[];if(null==n.mods.itemList)return[];var a,r=n.mods.itemList.content;for(a in r){var o,i,s=r[a],c=s.productId;null!=c&&(o=s.trace.utLogMap.x_object_id,i=s.lunchTime,1==e?t.push(o):o!=c&&t.push({x_object_id:o,product_id:c,lunch_time:i}))}}catch(e){console.log("获取产品关联id错误",e)}return 0==t.length?[]:1==e?t:void injectSendParamsToContentScript("",{data:t},cmd="upload_product_id",type="web_event")}
//判断是否产品详情页，获取产品id关联信息
function getProductIdByProductDetail(){var e,t;-1!==window.location.href.indexOf("/item/")&&0!=(e=getProductsId())&&0!=$("[rel='canonical']").length&&(t=$("[rel='canonical']").attr("href"),0!=(t=getProductsId(t)))&&e!=t&&injectSendParamsToContentScript("",{data:[{x_object_id:t,product_id:e,lunch_time:0}]},cmd="upload_product_id",type="web_event")}
//获取产品id
function getProductsId(e=""){var t,n=window.location.pathname,a=(n=""!=e?e:n).split("/");for(t in a){var r=a[t];if(-1!==r.indexOf("html"))return parseInt(r)}return!1}function getCStoreId(){var e,t=window.location.pathname;return-1!==t.indexOf("/store/")&&(e="",-1!==(t=(t=t.split("/"))[t.length-1]).indexOf(".html")?e=-1===t.indexOf("_")?parseInt(t):t.split("_")[0]:isNaN(t)||(e=t),e)}
/**
 * 选择搜图方式
 * @param {*} self 
 */function toSearchByImgInDetail(e){0===$(e).prev('div[class="choise_image_search_type_box"]').length&&$(e).before(showImageSearchChoise());
// if(imgs.length == 0){
//     $(".detail_page_search_by_img").remove()
//     return 
// }
// var imgUrl= $(imgs[0]).attr('src')
// var productId = getProductsId()
// event.stopPropagation();
// var url = 'https://ixspy.com/data#/dashboard?search_img='+imgUrl+'&type=search_by_img&id='+productId
// injectSendParamsToContentScript('', {url:url}, cmd = 'search_by_img', type = 'web_event')
}
/**
 * 展示选择搜图方式的选择
 * @returns 
 */function showImageSearchChoise(){event.preventDefault(),event.stopPropagation();var e=(e=navigator.language)||navigator.userLanguage;let t=`<div style="
    position: absolute;
    bottom: 30px;
    right: 70px;
    padding: 10px;
    z-index: 100;
    display: block;" onmouseleave="closeImageSearchChoise()" class="choise_image_search_type_box">`;return t+='<div class="smt_img_search_choise_item" onclick="todoSearchImage(this,1,event)">',
// dom += `速卖通以图搜图`
e.includes("zh")?t+="速卖通以图搜图":t+="Search the same Aliexpress products by image",t=t+"</div>"+'<div class="smt_img_search_choise_item" onclick="todoSearchImage(this,2,event)">',
// dom += `1688以图搜图`
e.includes("zh")?t+="1688以图搜图":t+="Searching for products by pictures in 1688",t=t+"</div>"+"</div>"}
/**
 * 关闭搜图框
 */function closeImageSearchChoise(){0<$(".choise_image_search_type_box").length&&$(".choise_image_search_type_box").remove()}function closeImageSearchHidden(){0<$(".choise_image_search_type_box_hidden").length&&$(".choise_image_search_type_box_hidden").css({display:"none"})}
/**
 * 搜图
 * @param {*} self 
 * @param {*} type 
 * @returns 
 */function todoSearchImage(e,t,n){n.preventDefault(),n.stopPropagation();if(0==(n=$(e).parent().parent().parent().find("img")).length)$(".detail_page_search_by_img").remove();else{if(!(a=(-1!=location.href.indexOf("aliexpress.ru")?$(n[1]):$(n[0])).attr("src")).includes("http")){
//换一个图片位置
n=$(e).parent().parent().parent().parent().find("img");if(0==(n=-1!=location.href.indexOf("aliexpress.ru")?$(e).parent().parent().parent().parent().parent().find("img"):n).length)return void $(".detail_page_search_by_img").remove();var a=$(n[0]).attr("src");
// if (imgUrl.includes('//ae01.alicdn.com') && !imgUrl.includes('http')) { 
//     imgUrl = 'https:' + imgUrl
// } else if (imgUrl.includes('//ae01.alicdn.com') && !imgUrl.includes('http')) {
// }
}
// console.log(imgs,imgUrl)
e=getProductsId();
// event.stopPropagation();
injectSendParamsToContentScript("",{url:"https://ixspy.com/data#/dashboard?search_img="+a+"&type="+{1:"search_by_img",2:"search_by_img_1688"}[t]+"&id="+e},cmd="search_by_img",t="web_event")}}function toSearchByImgInDetailRU(e){var t,e=$(e).parent().parent().find("img");0==e.length?$(".detail_page_search_by_img").remove():(t=(-1!=location.href.indexOf("aliexpress.ru")?(t=$(".SnowProductGallery_SnowProductGallery__galleryContainer__jy810").attr("data-spm"),$(e[t])):$(e[0])).attr("src"),e=getProductsId(),event.stopPropagation(),injectSendParamsToContentScript("",{url:"https://ixspy.com/data#/dashboard?search_img="+t+"&type=search_by_img&id="+e},cmd="search_by_img",type="web_event"))}
//根据id 获取 x_object_id
function getProductLinkIdByDetailId(e){try{var t=window.runParams;if(null==t||""==t||null==t)return e;if(null==t.mods||""==t.mods||null==t.mods)return e;if(null==t.mods.itemList)return e;var n,a=t.mods.itemList.content;for(n in a){var r=a[n],o=r.productId;if(null!=o&&o==e)return r.trace.utLogMap.x_object_id}}catch(e){}return e}
//收藏店铺
function collectedId(e){let n=[];$(".smt_favted_goods_box .active-tags").each((e,t)=>{n.push($(t).data("value"))}),injectSendParamsToContentScript("",{store_id:e,tags:n},cmd="collectedShop",type="web_event")}
/**
 * 展示标签
 * @param {*} id 
 */function collectedIdShow(e){injectSendParamsToContentScript("",{productId:e},cmd="show_collection_box_store",type="web_event")}
//取消收藏店铺
function removeShop(e){injectSendParamsToContentScript("",{store_id:e},cmd="removeShop",type="web_event")}
// 产品关键词下载
function downloadProductKeyword(){const n=["hot-words","soar-words","less-words"];let a="";
//判断来源是哪种类型的关键词搜索
var e,t,r;$(".oui-tab-switch-item").each((e,t)=>{(t=$(t)).hasClass("oui-tab-switch-item-active")&&(a=n[e])});switch(a){case"hot-words":resData=sycmHotWords();break;case"soar-words":resData=sycmSoarWords();break;case"less-words":resData=sycmLessWords();break;default:return}e=resData.url,t=resData.name,r=resData.title,e&&t&&
//调用接口返回数据
injectSendParamsToContentScript("",{url:e,name:t,type:a,title:r},cmd="getSycmWordsData",a="web_event")}
// 阻止事件的方法
function emptyFunc(){
//调用接口返回数据
injectSendParamsToContentScript("",{},cmd="empty_func",type="web_event")}
//-------------end-------------------------------
function sycmKeywordDeal(e,t){let n=window.location.search;
// 去除开头的问号
// 将请求参数字符串拆分成键值对数组
var a=(n=-1!==n.indexOf("?")?n.substring(1):n).split("&");
//从参数信息中获取请求信息
let r="",o="",i="",s="";a.forEach(function(e,t){"country"===(e=e.split("="))[0]?i=e[1]:"dateType"===e[0]?o=e[1]:"dateRange"===e[0]?r=e[1].split("%7C")[1]:"cate"===e[0]&&(s=e[1],e=decodeURIComponent(s),e=JSON.parse(e),s=e.cateId)});
//获取关键词
a=$('span[class="ant-input-search input-search ant-input-affix-wrapper"]').children("input").eq(0).val();
//获取分类名
let c=$('a[class="common-picker-header"]').attr("title"),l=(c=c.replace(">","-"),$('li[class=" ant-pagination-next"]'));var d=20*(
//没找到下一页按钮,代表只有一页数据
l=0===(l=0===l.length?$('li[class="ant-pagination-next"]'):l).length?1:parseInt(l.prev().html()));
//设置excel的文件名
let p="";let h="searchCnt",_="searchCookieCnt,searchCnt,clickRate,transRate,splyDemRate";var u="recent7"===o?"7天":"30天",m="ALL"===i?"全部":i,g=trim($($(".oui-tab-switch-item")[0]).html()),f=trim($($(".oui-tab-switch-item")[1]).html()),v=trim($($(".oui-tab-switch-item")[2]).html());let y="",w=$(".ant-table-thead");switch(w=w.find("th"),t){case"hot-words":h="searchCnt",_="searchCookieCnt,searchCnt,clickRate,transRate,splyDemRate",p=g+"_"+u+"_"+r+"_"+c+"_"+m,y=$(w[0]).children("span").html().replace(/^\s+|\s+$/g,"")+","+$(w[1]).children("span").children("div").html().replace(/<[^>]*>/g,"").replace(/^\s+|\s+$/g,"")+","+$(w[2]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[3]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[4]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[5]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[6]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[7]).children("span").html().replace(/^\s+|\s+$/g,"")+"\r\n";break;case"soar-words":h="searchCntRiseIndex",_="searchCnt,searchCntRiseIndex,exposeProdRate,exposeSellerRate",p=f+"_"+u+"_"+r+"_"+c+"_"+m,y=$(w[0]).children("span").html().replace(/^\s+|\s+$/g,"")+","+$(w[1]).children("span").children("div").html().replace(/<[^>]*>/g,"").replace(/^\s+|\s+$/g,"")+","+$(w[2]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[3]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[4]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[5]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+"\r\n";break;case"less-words":h="searchCnt",_="exposeProdRate,searchCnt,searchCookieCnt";
//获取商品结果范围数量
let e=$('div[class="less-select"]').find('div[class="ant-select-selection-selected-value"]').eq(0).html();p=v+"_"+u+"_"+r+"_"+c+"_"+m+"_"+e,y=$(w[0]).children("span").html().replace(/^\s+|\s+$/g,"")+","+$(w[1]).children("span").children("div").html().replace(/<[^>]*>/g,"").replace(/^\s+|\s+$/g,"")+","+$(w[2]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[3]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+","+$(w[4]).children("span").children("div").children("span").eq(0).html().replace(/^\s+|\s+$/g,"")+"\r\n",e=(e=(e=e.split("(")[1]).split(")")[0]).split("-"),maxCnt=1<e.length?(minCnt=parseInt(e[0]),parseInt(e[1])):(minCnt=parseInt(e[0].split("+")[0]),"");break;default:
//TODO: 错误提示处理
return!1}d={dateType:o,//7天,30天
pageSize:d,//请求数量
page:1,//页码
order:"desc",//排序
orderBy:h,keyword:a};return"less-words"===t&&(d.minCnt=minCnt,d.maxCnt=maxCnt),d.country=i,//国家
d.categoryId=s,//分类ID
d.statDate=r,//结束日期
d.indexCode=_,//字段信息
d._=(new Date).getTime(),{url:e+=$.param(d),name:p,title:y}}
//处理商家后台飙升关键词导出数据
function sycmSoarWords(){return sycmKeywordDeal("https://sycm.aliexpress.com/api/search-analysis/soar-words?","soar-words")}
//处理商家后台热搜关键词导出数据
function sycmHotWords(){return sycmKeywordDeal("https://sycm.aliexpress.com/api/search-analysis/hot-words?","hot-words")}
//处理商家后台零少词导出数据
function sycmLessWords(){return sycmKeywordDeal("https://sycm.aliexpress.com/api/search-analysis/less-words?","less-words")}function alinkEventDisable(e){e.preventDefault(),e.stopPropagation()}
//定时监听俄罗斯详情页banner
function intervalRuBanner(){setInterval(()=>{var e;-1!==location.href.indexOf(".ru/")&&-1!==location.href.indexOf("/item/")&&(
//详情页
0<$(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn").length?0<(e=$(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn")).find("picture").length&&(e=e.find("picture")[0],0==(e=$(e)).find(".detail_page_search_by_img").length)&&
//追加以图搜图按钮
injectSendParamsToContentScript("",{},cmd="createSearchImgButtonDomRuSingle",type="web_event"):0<$('div[class="gallery_Gallery__picListWrapper__15bdcj SnowProductGallery_SnowProductGallery__galleryWrapper__z6imb"]').children("div").length&&(e=$('div[class="gallery_Gallery__picListWrapper__15bdcj SnowProductGallery_SnowProductGallery__galleryWrapper__z6imb"]').children("div").find("img"),0<$(e).length)&&0==e.find(".detail_page_search_by_img").length&&
//追加以图搜图按钮
injectSendParamsToContentScript("",{},cmd="createSearchImgButtonDomRuSingle",type="web_event"))},1e3)}
//选品专家关键词下载
function downloadExpertProductKeyword(){var//类型
e=trim($('li[aria-selected="true"]').children("div").html()),//分类
t=trim($('input[name="categoryId"]').val()),//国家
n=trim($('input[name="country"]').attr("aria-valuetext")),//日期类型
a=(trim($("#translate").children("div").attr("aria-checked")?"English":"Normal"),trim($("#compareDate").children("div").children("span").eq(0).children("span").children("span").eq(0).children("em").html())),//日期区间
r=trim($("#compareDate").children("div").children("span").eq(1).children("input").val());let s=[];var o=$("#table"),i=(data_dom_body=o.find("tbody")).children("tr");let c=o.find("thead");
//不同类型的字段不一样分开处理
o=$("#keywordType").find("li");if(0<$(".next-table-empty").length)console.log("data empty");else{if("true"==$(o[0]).attr("aria-selected")){
//热搜词
c=c.find("th");var l=$(c[0]).find(".render-table-column").children("span").eq(0).html()+","+$(c[1]).find(".render-table-column").children("span").eq(0).html()+","+$(c[2]).find(".render-table-column").children("span").eq(0).html()+","+$(c[3]).find(".render-table-column").children("span").eq(0).html()+","+$(c[4]).find(".render-table-column").children("span").eq(0).html()+","+$(c[5]).find(".render-table-column").children("span").eq(0).html()+","+$(c[6]).find(".render-table-column").children("span").eq(0).html()+","+$(c[7]).find(".render-table-column").children("span").eq(0).html()+"\r\n";i.each((e,t)=>{var t=$(t).children("td"),n=$($(t[3]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[3]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[3]).find("span")[2]).html().replaceAll(",",""),a=$($(t[4]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[4]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[4]).find("span")[2]).html().replaceAll(",",""),r=$($(t[5]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[5]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[5]).find("span")[2]).html().replaceAll(",",""),o=$($(t[6]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[6]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[6]).find("span")[2]).html().replaceAll(",",""),i=$($(t[7]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[7]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[7]).find("span")[2]).html().replaceAll(",",""),t={keyword:$(t[0]).find("span").html(),keyword_translate:$(t[1]).find("span").html(),//翻译词
is_brand:$(t[2]).find("span").html(),//品牌
search_popularity:n,//人气
search_cnt:a,//指数
click_rate:r,transaction_rate:o,competitive_index:i};s.push(t)})}else if("true"==$(o[1]).attr("aria-selected")){
// 飙升词
c=c.find("th");l=$(c[0]).find(".render-table-column").children("span").eq(0).html()+","+$(c[1]).find(".render-table-column").children("span").eq(0).html()+","+$(c[2]).find(".render-table-column").children("span").eq(0).html()+","+$(c[3]).find(".render-table-column").children("span").eq(0).html()+","+$(c[4]).find(".render-table-column").children("span").eq(0).html()+","+$(c[5]).find(".render-table-column").children("span").eq(0).html()+","+$(c[6]).find(".render-table-column").children("span").eq(0).html()+"\r\n";i.each((e,t)=>{t=$(t).children("td"),t={keyword:$(t[0]).find("span").html(),keyword_translate:$(t[1]).find("span").html(),//翻译词
is_brand:$(t[2]).find("span").html(),//品牌
search_cnt:$($(t[3]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[3]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[3]).find("span")[2]).html().replaceAll(",",""),//指数
surge:$($(t[4]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[4]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[4]).find("span")[2]).html().replaceAll(",",""),//飙升幅度
product_growth_rate:$($(t[5]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[5]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[5]).find("span")[2]).html().replaceAll(",",""),//商品增长幅度
seller_growth_rate:$($(t[6]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[6]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[6]).find("span")[2]).html().replaceAll(",","")};s.push(t)})}else{if("true"!=$(o[2]).attr("aria-selected"))return void console.log("keyword type error");
//导出数据
//零少词
c=c.find("th");l=$(c[0]).find(".render-table-column").children("span").eq(0).html()+","+$(c[1]).find(".render-table-column").children("span").eq(0).html()+","+$(c[2]).find(".render-table-column").children("span").eq(0).html()+","+$(c[3]).find(".render-table-column").children("span").eq(0).html()+","+$(c[4]).find(".render-table-column").children("span").eq(0).html()+","+$(c[5]).find(".render-table-column").children("span").eq(0).html()+"\r\n";i.each((e,t)=>{var t=$(t).children("td"),n=$($(t[3]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[3]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[3]).find("span")[2]).html().replaceAll(",",""),n={keyword:$(t[0]).find("span").html(),keyword_translate:$(t[1]).find("span").html(),//翻译词
is_brand:$(t[2]).find("span").html(),//品牌
search_popularity:n,//人气
search_cnt:$($(t[4]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[4]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[4]).find("span")[2]).html().replaceAll(",",""),//指数
product_growth_rate:$($(t[5]).find("span")[0]).html().replaceAll(",","")+";"+$($(t[5]).find("span")[1]).html().replaceAll(",","")+";"+$($(t[5]).find("span")[2]).html().replaceAll(",","")};s.push(n)})}o=e+"_"+t+"_"+n+"_"+a+"_"+r;console.log(s),0<s.length&&
//调用接口返回数据
injectSendParamsToContentScript("",{data:JSON.stringify(s),name:o,title:l},cmd="toExportExpertWordExcel","web_event")}}function trim(e){return e.replace(/^\s+|\s+$/g,"")}function exportExcel(o,i,e,t={}){if(console.log(o,typeof o),console.log(i,typeof i),console.log(e,typeof e),console.log(t,typeof t),1<t.length){let r=XLSX.utils.book_new();t.forEach((e,t)=>{let n=[],a=!1;null!==o[t]&&(n=o[t]),(a=null!==i[t]?i[t]:a)&&n.unshift(a),console.log(n);t=XLSX.utils.json_to_sheet(n,{skipHeader:!0});XLSX.utils.book_append_sheet(r,t,e)});var n=new Blob([s2ab(XLSX.write(r,{bookType:"xlsx",bookSST:!1,type:"binary"}))],{type:""})}else{o.unshift(i);t=XLSX.utils.json_to_sheet(o,{skipHeader:!0}),t={sheet_name:["sheet"],//保存的表标题
Sheets:{sheet:Object.assign(t,//内容
{})}},n=new Blob([s2ab(XLSX.write(t,{
// bookType can be 'xlsx' or 'xlsm' or 'xlsb'
bookType:"xlsx",bookSST:!1,// 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
type:"binary"}))],{type:""})}const a=document.createElement("a");t=URL.createObjectURL(n);// 创建对象超链接
a.download=e+".xlsx",// 下载名称
a.style.display="none",a.href=t,// 绑定a标签
document.body.appendChild(a),a.click(),// 模拟点击实现下载
setTimeout(function(){
// 延时释放
URL.revokeObjectURL(n),// 用URL.revokeObjectURL()来释放这个object URL
document.body.removeChild(a)},100)}
//---------------------公共方法begin------------------
//1.封装的通信方法 inject 向 content_script 通信
function injectSendParamsToContentScript(e,t=null,n="request",a="web_menu"){window.postMessage({type:a,data:t,cmd:n,chosed_item:e},"*")}
//----------------------end--------------------------
//---------------------加载该js文件 触发的事件begin
//向 content_script 通信 判断是否要自动展开工具栏
$(function(){console.log("初始化参数11"),(
//执行代码
runParams=window.runParams)&&injectSendParamsToContentScript("",runParams,cmd="set_run_Params",type="web_event");
//获得当前是什么国家
var e=$("span[class='ship-to']"),t="";0<e.length&&("css_flag css_us"==(e=e.eq(0).children().eq(0).attr("class"))?t="US":"css_flag css_br"==e?t="BR":"css_flag css_fr"==e?t="FR":"css_flag css_es"==e&&(t="ES")),t||-1!==location.href.indexOf(".ru/")&&(t="RU"),
// if (country){
injectSendParamsToContentScript("",t,cmd="set_site_country",type="web_event"),injectSendParamsToContentScript("","","append_down_button","web_event")}),window.onload=function(){},window.addEventListener("resize",show_smt),$("#checkall").click(function(){this.checked?($("input[name='checkname']").attr("checked",!0),injectSendParamsToContentScript("",{flag:1},"show_auto","web_event")):($("input[name='checkname']").attr("checked",!1),injectSendParamsToContentScript("",{flag:2},"show_auto","web_event"))}),$("body").click(function(){$(".ui-autocomplete").css("display","none")}),intervalRuBanner(),injectSendParamsToContentScript("",{},"get_show_auto",type="web_event"),
//向 content_script 通信 ，插入下载的dom （只有在产品详情页才会插入）
// injectSendParamsToContentScript('', '', 'append_down_button', 'web_event')
//26.获取版本是否要更新
injectSendParamsToContentScript("",{},cmd="request_version",type="web_event"),
//27.获取收藏的产品id
getFavtedId(),
//获取当前插件的安装方式
injectSendParamsToContentScript("",{},"get_set_source",type="web_event"),
//定时获取配置信息
timerGetJg(120),
//加载的时候就获取消息通知一次
getNotify(),
//获取产品的关联id,在产品列表页，并上报
getProductLinkId(),
//在产品详情页获取产品的关联id 并上报
getProductIdByProductDetail(),
//获取店铺的关联id 并上报
getRealStoreIdLink();
//---------------------------------加载该js文件 触发的事件end

