define(["../core","../var/document","./var/rsingleTag","../manipulation/buildFragment",
// This is the only module that needs core/support
"./support"],function(a,o,c,i,l){"use strict";
// Argument "data" should be string of html
// context (optional): If specified, the fragment will be created in this context,
// defaults to document
// keepScripts (optional): If true, will include scripts passed in the html string
return a.parseHTML=function(e,t,n){var r;return"string"!=typeof e?[]:(
// Single tag
"boolean"==typeof t&&(n=t,t=!1),t||(
// Stop scripts or inline event handlers from being executed immediately
// by using document.implementation
l.createHTMLDocument?((
// Set the base href for the created document
// so any parsed elements with URLs
// are based on the document's URL (gh-2965)
r=(t=o.implementation.createHTMLDocument("")).createElement("base")).href=o.location.href,t.head.appendChild(r)):t=o),r=!n&&[],(n=c.exec(e))?[t.createElement(n[1])]:(n=i([e],t,r),r&&r.length&&a(r).remove(),a.merge([],n.childNodes)))},a.parseHTML});