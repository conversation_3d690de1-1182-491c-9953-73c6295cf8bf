//该js文件功能： 向页面插入不同的 DOM , 展示各样的数据
!function(){var t;
// window.location.href.indexOf("gsp.aliexpress.") == -1 &&
// window.location.href.indexOf("sycm.aliexpress.") == -1 &&
-1==window.location.href.indexOf("report.aliexpress.")&&-1==window.location.href.indexOf("customerservice.aliexpress.")&&-1==window.location.href.indexOf("sg-cgmp.aliexpress.")&&-1==window.location.href.indexOf("feedback.aliexpress.")&&-1==window.location.href.indexOf("helpcenter.aliexpress.")&&(appendJsCss("js/jquery.js","js"),appendJsCss("js/xlsx.full.min.js","js"),appendJsCss("js/echarts.min.js","js"),appendJsCss("js/words.js","js"),
//csp.aliexpress.com/m_apps/csp-sycm-op/searchAnalysis
-1==window.location.href.indexOf("sycm.aliexpress.")&&-1==window.location.href.indexOf("csp.aliexpress.")&&-1==window.location.href.indexOf("gsp.aliexpress.")&&(appendJsCss("css/plugin.css","css"),appendJsCss("css/bootstrap.min.css","css")),appendJsCss("js/common_func.js","js"),appendJsCss("js/jszip.js","js"),appendJsCss("js/filesaver.js","js"),setTimeout(()=>{appendJsCss("js/exten.cookie.js","js"),appendJsCss("js/inject_web.js","js"),appendJsCss("js/xhr.js","js")},3e3),
//向背景页通信，判断是否要展开工具栏
contentScriptToBackground("get_show_auto",{},function(e){localStorage.setItem("show_auto",e)}),
//向背景页通信，获取选中的国家信息，缓存在页面中
contentScriptToBackground("get_chose_countrys",{},function(e){localStorage.setItem("country_groups",JSON.stringify(e))}),contentScriptToBackground("get_turn",{},function(e){1==e?localStorage.setItem("smt_turn",1):localStorage.setItem("smt_turn",0)}),contentScriptToBackground("get_sales_auto",{},function(e){0==e?localStorage.setItem("smt_sales_auto",0):localStorage.setItem("smt_sales_auto",1)}),contentScriptToBackground("get_ads",{},function(e){0==e.length?localStorage.setItem("adsNew_smt",0):localStorage.setItem("adsNew_smt",JSON.stringify(e))}),-1!=document.domain.indexOf(".com")&&-1!=document.domain.indexOf("www.")||appendClearCookieButton(),t=setInterval(()=>{var e=localStorage.getItem("smt_config_class"),o=JSON.parse(e);null!=o&&(clearInterval(t),console.log(123),
//插入工具栏
setTimeout(()=>{appendToolDom()},2e3),
//判断当前页面是否是产品详情页，获取关联id ,上报到服务端
setTimeout(()=>{var e,t,n,a=window.location.href;-1!==a.indexOf("/item/")&&(e=getProductId(a),t=0,e=getProductId(a),0<$("[rel='canonical']").length&&(t=getProductId($("[rel='canonical']").attr("href"))),a=0,(n=o).detail_sale_class?0<$(n.detail_sale_class).length&&(a=$(n.detail_sale_class).text().split(" ")[0]):0<$(".reviewer--sold--ytPeoEy").length&&(a=$(".reviewer--sold--ytPeoEy").text().split(" ")[0]),contentScriptToBackground("request_product",{id:e,x_object_id:t,sale:a},appendToolCountryPrice),contentScriptToBackground("request_product",{id:e,x_object_id:t},appendWebProductInfo))},2e3),
//插入产品区域定价按钮
// appendToolCountryPrice()
//插入产品个店铺的收藏按钮
appendStoreAndProductFavtedButton(),
//后台插入导出按钮
appendSycmSearchWordExcelButton())},1e3))}();var glob_product_chart_info=[],glob_store_chart_info=[],similarPic=chrome.runtime.getURL("img/similar.png"),tipImg=bg_i18n("lang_search_img"),login_url="https://data.ixspy.cn/login";//henan的安装包需要用https://data.ixspy.cn,其他用https://ixspy.com
//1. 插入 Web.js / css 文件
function appendJsCss(e,t){var n;"js"==t?((n=document.createElement("script")).src=chrome.runtime.getURL(e),document.body.appendChild(n)):"css"==t&&(n=document,t=chrome.runtime.getURL(e),(e=n.createElement("link")).setAttribute("rel","stylesheet"),e.setAttribute("type","text/css"),e.setAttribute("href",t),((t=n.getElementsByTagName("head")).length?t[0]:documentElement).appendChild(e))}
//2. 插入插件的工具栏
function appendToolDom(e=""){var t,n,a,o,i,r,l,_,s,d,c,g,p,m,u,h,f,v,w,y,b,x,S,k,I,D,P,C=window.location.pathname;
//(pathName.indexOf("/af") === -1 && pathName.indexOf("/wholesale") === -1) &&
-1===C.indexOf("/item/")&&-1===C.indexOf("/store/")&&""==e||(n=localStorage.getItem("adsNew_smt"),0!=(n=JSON.parse(n)).length&&""!=n&&null!=n&&"false"!=n&&0!=n&&"undefined"!=n&&(t=n[0].url,n=-1!==navigator.language.indexOf("zh-CN")?n[0].name:n[0].en_name),k=chrome.runtime.getURL("img/product.png"),I=chrome.runtime.getURL("img/store-fill.png"),D=chrome.runtime.getURL("img/store-fill-hover.png"),P=chrome.runtime.getURL("img/chart.png"),a=chrome.runtime.getURL("img/chart-hover.png"),o=chrome.runtime.getURL("img/more.png"),i=chrome.runtime.getURL("img/img.png"),r=chrome.runtime.getURL("img/img-hover.png"),l=chrome.runtime.getURL("img/love.png"),_=chrome.runtime.getURL("img/love-hover.png"),s=chrome.runtime.getURL("img/enter.png"),d=chrome.runtime.getURL("img/enter-hover.png"),c=chrome.runtime.getURL("img/upgrade.png"),g=chrome.runtime.getURL("img/dock-left.png"),p=chrome.runtime.getURL("img/dock-right.png"),m=chrome.runtime.getURL("img/open-left.png"),u=chrome.runtime.getURL("img/open-right.png"),h=chrome.runtime.getURL("img/lingdang.png"),f=bg_i18n("lang_product"),v=bg_i18n("lang_store"),w=bg_i18n("lang_price"),y=bg_i18n("lang_order"),b=bg_i18n("lang_img"),x=bg_i18n("lang_my_favted"),S=bg_i18n("quick_enter"),(k={url1:k,url2:I,url2_hover:D,url3:P,url3_hover:a,url4:o,url5:i,url5_hover:r,favtedImg:l,favtedImg_hover:_,persion:s,persion_hover:d,imgDockLeft:g,imgDockRight:p,imgOpenLeft:m,imgOpenRight:u,msg_ads:n,adsUrl:t,imgNew:h,tip_stow:bg_i18n("lang_stow"),tip_open:bg_i18n("lang_open"),tip_dock_left:bg_i18n("lang_dock_left"),tip_dock_right:bg_i18n("lang_dock_right"),name1:f,name2:v,name3:w,name4:y,name5:b,upgrade:c,titleMyFavted:x,titlePersion:S}).tip_msg=bg_i18n("lang_tip_ixspy"),k.tip_msg_stroe=bg_i18n("lang_store"),k.tip_msg_product=bg_i18n("lang_product"),k.tip_msg_chart=bg_i18n("lang_price"),k.tip_msg_pic=bg_i18n("lang_img"),k.tip_check_upgrade=bg_i18n("lang_tip_check_upgrade"),k.lang_auto_expand=bg_i18n("lang_auto_expand"),k.tip_myfavted=bg_i18n("lang_my_favted"),k.tip_persion=bg_i18n("quick_enter"),I=pub_format(String(function(){
/*!
        <div class='inject-button' style="z-index:2147483648">
            <div class="inject-button-1">
                <div class="inject-button-2">
                    <div class='inject-show-content-div is-active tools-width'>
                    <div style="display:inline-block;float:right;margin-top:16px;margin-right:15px;">
                                    <span class="new" onclick="toIxspy('#{adsUrl}')">
                                        <span  style="padding:0 20px;">#{msg_ads}</span>
                                        <img   style="width:25px;height:25px;margin:0 10px;"  src="#{imgNew}">
                                    </span>
                                </div>
                        <div class="tools-height">
                            <div id='inject-hide' class="inject-hide-style">
                                <span class="hide-title-info">播主信息概览</span>
                                <span onclick='hideShowContent()' class='glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>
                                <span class="upGrade-button">
                                    <div style="left:395px !important" class="hover-titip_update_tip">#{tip_check_upgrade}</div>
                                    <img   onclick="checkUpgrade()" style="margin-top: 10px;margin-right: 20px;" class="dy-white float-right cursor" src="#{upgrade}">
                                </span>

                                <span style="float: right;margin-right: 15px;margin-top: 10px;">
                                    <input onclick='checkBoxClick(this)' style="opacity: 1 !important;" type="checkbox" id="checkall" checked  name="checkname">
                                    <span>#{lang_auto_expand}</span>
                                </span>

                            </div>
                        </div>
                        <div class="join-group">
                            <span><a class="black" href='#{groupUrl}' target="_blank">#{textGroup}</a></span>
                        </div>
                    </div>
                    
                    <div class='tools-width'>
                        <nav   class="navbar navbar-inverse tool-nav-class">
                            
                            <div onmouseout="tipMouseout('store','#{url2}',this)" style="margin-left:7px;"  onmouseover="tipMouseover('store','#{url2_hover}',this)" class=" displayMenu store_click_event hide-all" onclick="web_choseItemMneu('#{name2}',this,'store')">
                                <img id="hover_store" class="dy-white" src="#{url2}">
                                <p class="tip_product" id="text_store">#{tip_msg_stroe}</p>
                            </div>

                            <div onmouseout="tipMouseout('chart','#{url3}',this)"  onmouseover="tipMouseover('chart','#{url3_hover}',this)" class=" displayMenu menu_product_chart produtc_click_small_chart hide-all" onclick="web_choseItemMneu('#{name3}',this,'chart')">
                                <img id="hover_chart" class="dy-white" src="#{url3}">
                                <p class="tip_product" id="text_chart">#{tip_msg_chart}</p>
                            </div>

                            <div onmouseout="tipMouseout('pic','#{url5}',this)"  onmouseover="tipMouseover('pic','#{url5_hover}',this)" class=" displayMenu hide-all" onclick="web_choseItemMneu('#{name5}',this,'img')">
                                <img id="hover_pic" class="dy-white" src="#{url5}">
                                <p class="tip_product" id="text_pic">#{tip_msg_pic}</p>
                            </div>

                            <div onmouseout="tipMouseout('myfavted','#{favtedImg}',this)"  onmouseover="tipMouseover('myfavted','#{favtedImg_hover}',this)" class=" displayMenu d_my_favted hide-all" onclick="web_choseItemMneu('#{titleMyFavted}',this,'my_favted')">
                                <img id="hover_myfavted" class="dy-white" src="#{favtedImg}">
                                <p class="tip_product" id="text_myfavted">#{tip_myfavted}</p>
                            </div>

                            <div onmouseout="tipMouseout('persion','#{persion}',this)"  onmouseover="tipMouseover('persion','#{persion_hover}',this)" class=" displayMenu d_my_persion hide-all" onclick="web_choseItemMneu('#{titlePersion}',this,'my_persion')">
                                <img id="hover_persion" class="dy-white" src="#{persion}">
                                <p class="tip_product" id="text_persion">#{tip_persion}</p>
                            </div>
                            
                            <div style="position: relative;top:-20px;tex" class="displayMenu text-ixspy" onclick="web_choseItemMneu('#{name4}',this,'more')">
                                <span onmouseout="tipMouseout('ixspy')" onmouseover="tipMouseover('ixspy')">IXSPY.COM</span>
                                <span class="tool-version" style="width:60px;left:58px;font-size:12px;position: absolute;top:22px;"></span>
                            </div>
                            <div class="hover_turn_stow">#{tip_stow}</div>
                            <div class="hover_turn_open">#{tip_open}</div>
                            <div class="hover_turn_left">#{tip_dock_left}</div>
                            <div class="hover_turn_right">#{tip_dock_right}</div>
                            <div class="imgDock" onmouseout="tipLeft('left','right')" onmouseover="tipRight('left','right')">
                                 <img id="turn-left" class="dy-white" onclick="turnLeft()" src="#{imgDockLeft}">
                                 <img id="turn-right"  class="dy-white" onclick="turnRight()" style="display:none" src="#{imgDockRight}">
                            </div>
                            <a href="javascript:hide()" onmouseout="tipTurnOut('stow','open')" onmouseover="tipTurnOver('stow','open')" class="imgOpen" id="my_btn">
                                 <img id="open-left" class="dy-white" style="display:none"  src="#{imgOpenLeft}">
                                 <img id="open-right"  class="dy-white"   src="#{imgOpenRight}">
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),k),$("body").append(I),setTimeout(()=>{var e=localStorage.getItem("extention_version"),t=localStorage.getItem("set_source");"local"==t?e+="-3":"google"==t?e+="-1":"microsoft"==t&&(e+="-2"),$(".tool-version").html("V"+e)},2e3),0!=n.length&&""!=n&&null!=n&&"false"!=n&&"undefined"!=n&&0!=n||$(".new").css("display","none"),1==localStorage.getItem("smt_turn")?(D=$("body").width()-630,$(".inject-button").css({right:D}),$(".imgDock").css({right:"-35px"}),$("#turn-left").css("display","none"),$("#turn-right").css("display","block"),$(".imgOpen").css({right:"-35px"}),$("#open-left").css("display","block"),$("#open-right").css("display","none")):($(".inject-button").css({right:"20px"}),$(".imgDock").css({left:"-35px"}),$(".imgOpen").css({left:"-35px"}),$("#open-left").css("display","none"),$("#open-right").css("display","block")),2==(P=localStorage.getItem("show_auto"))&&""==e?(
// initCss()
// $(".tools-width").css("width","145")
hideTools(),$("input[name='checkname']").attr("checked",!1)):(2!=P&&$("input[name='checkname']").attr("checked",!0),2==P&&$("input[name='checkname']").attr("checked",!1),""!==e&&setTimeout(()=>{$(".produtc_click_small_chart").addClass("inject-active"),$(".displayMenu").css("pointer-events","none"),$(".displayMenu").css("cursor","not-allowed"),$(".d_my_persion").css("pointer-events","auto"),$(".d_my_favted").css("pointer-events","auto"),$(".d_my_persion").css("cursor","pointer"),$(".d_my_favted").css("cursor","pointer"),$(".inject-show-content-div").removeClass("is-active"),$(".hide-title-info").html(w),createLoadingDom(),
//createSmallLoadingDom("small-chart-"+productId)
contentScriptToBackground("request_product_chart",{product_id:e},function(e){createchartDom(e)})},500),-1!==C.indexOf("/item/")&&""==e&&($(".displayMenu").css("cursor","pointer"),setTimeout(()=>{$(".produtc_click_small_chart")[0].click();getProductsId();
// contentScriptToBackground('getHrefStoreId',{id:id},function(response){
//     createStoreIdDom(response.admin_seq)
// })
},2e3)),-1!==C.indexOf("/store/")&&""==e&&($(".displayMenu").css("cursor","not-allowed"),setTimeout(()=>{$(".store_click_event")[0].click(),$(".store_click_event").css("cursor",""),$(".text-ixspy").css("cursor",""),$(".d_my_favted").css("cursor",""),$(".d_my_persion").css("cursor","")},2e3))))}function appendClearCookieButton(){var e=pub_format(String(function(){
/*!
       <div class="clear-cookie-button" onclick="clearCookie()">
           <span class="jump-button">一键跳转aliexpress.com</span>
       </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""));$("body").append(e)}function hideTools(){var e=$("body").width()-180;$(".hide-all").css("display","none"),$(".tools-width").css("width","145"),$(".text-ixspy").css({top:"10px",marginLeft:"15px"}),$(".inject-show-content-div").css("display","none"),$("div").removeClass("border_bottom"),0==localStorage.getItem("smt_turn")?($("#open-left").css("display","block"),$("#open-right").css("display","none")):($("#open-left").css("display","none"),$("#open-right").css("display","block"),$(".inject-button").css("right",e)),document.getElementById("my_btn").href="javascript:show_smt();"}
//2.1 在产品页插入国家价格的收集
function appendToolCountryPrice(p){
//这个需要了先
setInterval(()=>{var e=window.location.pathname;if(-1!==e.indexOf("/item/")){var e={lang_check_country_price:bg_i18n("lang_check_country_price"),lang_1019:bg_i18n("lang_1019")},t={lang_orders:bg_i18n("lang_orders"),orders:p.trade_count,img:chrome.runtime.getURL("/img/stars.png"),imgs:chrome.runtime.getURL("/img/star.png"),rating:p.ratings,reviews:p.reviews_count},n=localStorage.getItem("smt_config_class"),a=(JSON.parse(n).go_to_api,""),o=getProductsId();if(e.goodsId=o,orders=pub_format(String(function(){
/*!
            <div style="margin-top:15px">
            <div class="rating">
               <img style="    position: absolute;top: -15px;z-index: 1;" src="#{img}">
               <img class="starsimg" src="#{imgs}">
            </div>
            <span>#{rating}</span>
            <span style="margin-left:8px">#{reviews} Reviews</span> 
                <div style="color:#3a3e4a;font-size:14px;"  class="check-product-carrier-msg check-orders check-country-price-tmp">
                <span>#{orders} #{lang_orders}</span></div>
            </div>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t),
//obj.langAlready = bg_i18n('lang_already_task') 
a=pub_format(String(function(){
/*!
                <div onclick="startGetProductPriceInfoByApi(#{goodsId})" class="check-product-carrier-msg check-country-price check-country-price-tmp"><span >#{lang_check_country_price}</span></div>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e),p||(
// button = pub_format(String(function () {/*!
// <div class="check-country-price check-country-price-tmp"><span>#{lang_1019}</span></div>
// */ }).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g, ''), obj);
a="<span></span>"),!(0<$(".check-country-price-tmp").length))
//获取配置文件
try{var i,n=localStorage.getItem("smt_config_class"),r=(i=JSON.parse(n)).sold_review_rating,l=i.append_sold_review_rating;for(c in i=i.collection_price_button_new){var _=i[c];if(-1!==_.indexOf("|")){var s=_.split("|"),d=s[c];if(0<$("."+d).length){$($("."+d)[s[1]]).append(a);break}}else for(var c in 0<$("."+_).length&&$("."+_).append(a),r){var g=r[c];0==$("."+g).length&&p.trade_count&&0==$(".rating").length&&(0<$("div[data-pl='product-reviewer']").length?
//如果官方有大致销量,则调整位置,并只显示数量
0==$(".sold-after-order-span-ixb").length&&$("div[data-pl='product-reviewer']").append('&nbsp;&nbsp;<span class="sold-after-order-span-ixb">('+p.trade_count+" "+t.lang_orders+")</span>"):$("."+l).append(orders),setRating(p.ratings))}}
// if(className != undefined && className != ""){
//     if(className.indexOf("|") !== -1){
//         var objClassName = className.split("|")
//         for(var i in objClassName){
//             var tempName = objClassName[i]
//             if($("."+tempName).length > 0){
//                 $("."+tempName).append(button)
//                 break
//             }
//         }
//     }else{
//         if($("."+className).length > 0){
//             $("."+className).append(button)
//         }
//     }
// }else{
//     if($(".product-price").length > 0 && $(".check-country-price").length == 0){
//         $(".product-price").append(button)
//     }
// if($(".product-reviewer").length > 0){
//     $(".product-reviewer").append(button)
// }
// }
}catch(e){console.log("插入按钮出错",e),0<$(".product-price").length&&0==$(".check-country-price").length&&$(".product-price").append(a)}}},3e3)}function setRating(e){e=90-e/5*90+"px";$(".starsimg").css("clip-path","inset(0 "+e+" 0 0)")}
//3. 判断是否登陆的回调
function call_isLogin(e){"ok"==e?($(".no_login_block").css("display","none"),$(".no_login_none").css("display","block"),$(".h3-button").css("display","block")):($(".no_login_block").css("display","block"),$(".no_login_none").css("display","none"),$(".h3-button").css("display","none")),"ok"==localStorage.getItem("isIdHere")&&$("#collection_goods").remove()}
//4 产品DOM
async function createproductDom(e){var t;$(".smt-loading").remove(),"update_tip"!=e&&(0==e?createNoInfoTipDom:(createStoreIdDom(e.admin_seq),e.sameProductDom=cycleSameProductDom(e.like_goods),t=bg_i18n("lang_no_login_button"),e.loginDom=createLoginOrRegDom(t),localStorage.setItem("isIdHere",e.isIdHere),e.km_trade_count=pub_numTransKm(e.trade_count),e.km_reviews_count=pub_numTransKm(e.reviews_count),e.km_wishlist_count=pub_numTransKm(e.wishlist_count),e.shopify_goods_dom=createdShopifyGoodsInfoDom(e.shopify_goods),e.lang_currency=bg_i18n("lang_currey"),e.lang_price=bg_i18n("lang_product_price"),e.lang_rating=bg_i18n("lang_product_rating"),e.lang_total_orders=bg_i18n("lang_product_total_orders"),e.lang_total_reviews=bg_i18n("lang_product_total_reviews"),e.lang_wishlist=bg_i18n("lang_product_wishlist"),e.lang_add_favted=bg_i18n("lang_add_favted"),e.lang_more_product=bg_i18n("lang_more_product_info"),e.pathName=await productCategoryNew(e.category_path),e.category_name=bg_i18n("lang_category"),
// response.add_time_new = pub_timeToMDY(response.add_time)
e.add_time_new=e.add_date,e.update_time_new=pub_timeToMDY(e.update_time),e.lang_first_discover=bg_i18n("lang_first_discover"),e.lang_last_update=bg_i18n("lang_last_update"),uniteInsertMenuContentDom(pub_format(String(function(){
/*!
        <div class="product-part-1">
            <div style="padding-top:20px;">
                <div class="inline product-div-2">
                    <div class="inline"> 
                        <span title="#{product_name}" class='product-title-name'>#{product_name}</span> 
                    </div>
                    
                </div>
            </div>

            <div>
                <div>
                    <div>
                       #{category_name}: #{pathName}
                    </div>
                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_currency}</span></div>
                        <div class="six-div-2"><span>#{currency}</span></div>
                    </div>
                    <div class="inline w6">
                        <div class="six-div-1"><span>#{lang_price}</span></div>
                        <div class="six-div-2"><span>#{min_price}-#{max_price}</span></div>
                    </div>

                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_rating}</span></div>
                        <div class="six-div-2"><span>#{ratings}</span></div>
                    </div>
                </div>
                
                <div>
                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_total_orders}</span></div>
                        <div class="six-div-2"><span>#{km_trade_count}</span></div>
                    </div>
                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_total_reviews}</span></div>
                        <div class="six-div-2"><span>#{km_reviews_count}</span></div>
                    </div>
                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_wishlist}</span></div>
                        <div class="six-div-2"><span>#{km_wishlist_count}</span></div>
                    </div>
                </div>
                <div>
                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_first_discover}</span></div>
                        <div class="six-div-2"><span>#{add_time_new}</span></div>
                    </div>
                    <div class="inline w6">
                        <div class="six-div-1"><span >#{lang_last_update}</span></div>
                        <div class="six-div-2"><span>#{update_time_new}</span></div>
                    </div>
                </div>

                <div>
                    <div class="turn-to-ixspy" style="margin-top:15px;">
                        <a style="color:black" href="https://ixspy.com/data#/dashboard?id=#{product_id}&type=aliexpress_product" target="_blank"> <span>#{lang_more_product}</span> </a>
                    </div>
                    <div style="clear:both"></div>
                </div>
            </div>
            #{loginDom}
            <div id="collection_goods" class="button-black no_login_none smt_already_collect_#{product_id}" onclick="web_favtedGoodsId(#{product_id})">
                <span>#{lang_add_favted}</span>
            </div>
                #{sameProductDom}
            <div id="shopify_info_detail">
                #{shopify_goods_dom}
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e),type="product",e),checkIsLogin))()}
//5 店铺DOM
function createstoreDom(e){var t;
// console.log(response,123)
$(".smt-loading").remove(),"update_tip"!=e&&(0==e?createNoInfoTipDom():(
//response.topProducts = cycleHotOreNewProductsDom(response)
e.loginDom=createLoginOrRegDom(),e.km_reviews_count=pub_numTransKm(e.reviews_count),e.km_feedback_total_num=pub_numTransKm(e.feedback_total_num),e.km_wishlist_count=pub_numTransKm(e.wishlist_count),e.lang_age=bg_i18n("lang_age"),e.lang_feed_back=bg_i18n("lang_positive"),e.lang_reviews=bg_i18n("lang_reviews"),e.lang_wishlist=bg_i18n("lang_favted"),e.lang_sales_chart=bg_i18n("lang_store_Estimated_sales"),e.lang_favted_chart=bg_i18n("lang_favted_chart"),e.lang_review_chart=bg_i18n("lang_review_chart"),e.lang_goods_rate=bg_i18n("lang_goods_rate"),e.top_img=chrome.runtime.getURL("img/top_brands.png"),e.lang_total=bg_i18n("lang_total"),e.lang_inc=bg_i18n("lang_inc"),e.lang_more_shop=bg_i18n("lang_more_shop_info"),1<(t=e.store_age)?(e.new_store_age=parseInt(t),e.lang_shop_age=bg_i18n("lang_shop_age")):(e.new_store_age=10*t,e.lang_shop_age=bg_i18n("lang_shop_age_month")),e.lang_year_chart=bg_i18n("lang_year_chart"),e.love=chrome.runtime.getURL("img/love.png"),e.already_favted=chrome.runtime.getURL("img/already_favted.png"),e.display_collected=e.is_collection?"none":"inline-block",e.display_uncollected=e.is_collection?"inline-block":"none",e.lang_collection_shop=bg_i18n("lang_collection_shop"),e.lang_un_collection_shop=bg_i18n("lang_cancel_shop_collection"),uniteInsertMenuContentDom(pub_format(String(function(){
/*!
        <div class="product-part-1 ali-store">
            <div>
                <div class="store-new-div">
                    <a target="_blank" href="#{url}" style="display:inline-block">
                        <div>
                            <img class="topBrand" style="width:20px;" src="#{top_img}" />
                            <div class="inline store-name"> #{store_name} </div>
                        </div>
                    </a>
                    <div>
                        <div style="text-align:center" class="inline">
                            <a target="_blank" href="#{url}">
                                <img style="width:100px;border: 1px solid gray;margin-top:10px" src="#{store_logo}" />
                            </a>
                        </div>
                        <div style="width:73%;margin-left:10px;vertical-align: middle;" class="inline">
                            <div>
                                <div class="inline w3">
                                    <div class="six-div-1" style="text-align:right"> <span style="color:black">#{new_store_age}</span><span>#{lang_shop_age}</span></div>
                                </div>
                                <div class="inline w3" style="margin-left:20px;">
                                    <div class="six-div-1" style="text-align:left"><span style="color:black">#{feedback_positive_rate}%</span> <span>#{lang_goods_rate}</span></div>
                                </div>
                            </div>
                            <div style="margin-top:15px;">
                                <div class="inline w3">
                                    <div class="six-div-1" style="text-align:right"><span style="color:black">#{km_reviews_count}</span> <span>#{lang_reviews}</span></div>
                                </div>

                                <div class="inline w3" style="margin-left:20px;">
                                    <div class="six-div-1" style="text-align:left"><span style="color:black">#{km_wishlist_count}</span> <span>#{lang_wishlist}</span></div>
                                </div>
                            </div>
                            <div style="margin-top:15px;">
                                <div class="inline w3 collect-shop-collection-class" style="display:#{display_collected}">
                                    <div onclick="collectedIdShow(#{admin_seq})" class="six-div-1" style="text-align:right">
                                        <span style="cursor: pointer;text-decoration: underline;">#{lang_collection_shop}</span>
                                    </div>
                                </div>
                                <div class="inline w3 remove-shop-collection-class" style="display:#{display_uncollected}">
                                    <div onclick="removeShop(#{admin_seq})" class="six-div-1" style="text-align:right">
                                        <span style="cursor: pointer;text-decoration: underline;">#{lang_un_collection_shop}</span>
                                    </div>
                                </div>
                                <div  class="inline" style="margin-left:20px;width: 150px;">
                                    <a style="color:black" href="https://ixspy.com/data#/dashboard?id=#{url}&type=aliexpress_store" target="_blank"> <span>#{lang_more_shop}</span> </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div>
               
                <hr class='mg-top-20' />
                <div class='mgtop10'>
                    <div>
                        <h3 class="inline">#{lang_sales_chart}</h3>
                        #{loginDom}
                    </div>
                    <div class="echart_div no_login_none">
                        <div id="store_trade_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('store_trade_chart','store',#{admin_seq})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>

                <hr class='mg-top-20' />
                <div class='mgtop10'>
                    <div>
                        <h3 class="inline">#{lang_favted_chart}</h3>
                        #{loginDom}
                        <div class="btn-style no_login_none h3-button">
                            <button type="button" class="btn btn-default btn-active store_wishlis-total-button" onclick="choseStoreChartType('store_wishlis','total')">#{lang_total}</button>
                            <button type="button" class="btn btn-default  store_wishlis-inc-button" onclick="choseStoreChartType('store_wishlis','inc')">#{lang_inc}</button>
                        </div>
                    </div>
                    <div class="echart_div no_login_none">
                        <div id="store_wishlis_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('store_wishlis_chart','store',#{admin_seq})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>

                <hr class='mg-top-20' />
                <div class='mgtop10'>
                    <div>
                        <h3 class="inline">#{lang_review_chart}</h3>
                        #{loginDom}
                        <div class="btn-style no_login_none h3-button">
                            <button type="button" class="btn btn-default btn-active store_reviews-total-button" onclick="choseStoreChartType('store_reviews','total')">#{lang_total}</button>
                            <button type="button" class="btn btn-default  store_reviews-inc-button" onclick="choseStoreChartType('store_reviews','inc')">#{lang_inc}</button>
                        </div>
                    </div>
                    <div class="echart_div no_login_none">
                        <div id="store_reviews_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('store_reviews_chart','store',#{admin_seq})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)),1!=e.top_brands&&$(".topBrand").css("display","none"),glob_store_chart_info=e.charts_data,helpDrawChart(e.charts_data)))}
//循环遍历多个店铺产品/新品
function cycleHotOreNewProductsDom(e){
////console.log(response)
//var msg = bg_i18n('lang_top_2_products')
//var html = "<hr class='mg-top-20' /><h3>"+msg+"</h3><div class='mg-top-30'><div>"
return pub_isEmpty(e)?"":
//热销产品
// var objTopHotProducts = response.top2Hotproducts
// var msg = bg_i18n('lang_top_2_products')
// var msg1 = bg_i18n('lang_more')
// var html = "<hr class='mg-top-20' /><h3 style='display:inline-block'>"+msg+"</h3><a target='_blank' href='https://ixspy.com/data#/product/product-hot' style='float: right;margin-top: 17px;text-decoration: underline;color: black;font-size: 18px;'>"+msg1+"</a><div class='mg-top-30'><div>"
// if(objTopHotProducts.length > 0){
//     for(var i in objTopHotProducts){
//         var objV = objTopHotProducts[i]
//         var str = pub_format(String(function(){/*!
//             <div class='inline mg-left-40'>
//                 <div class='inline'>
//                     <a target="_blank" href="#{product_url}"> <img class="product-img-2" src="#{product_image}" /></a>
//                 </div>
//                 <div class="valign-b font-13 font-w-600 from-supply">
//                     <div title="#{product_name}">#{product_name}</div>
//                     <div>#{currency} #{min_price}-#{max_price}</div>
//                     <div>#{ratings}<span style="margin-left:3px" class="glyphicon glyphicon-star"></span>(#{reviews_total})</div>
//                 </div>
//             </div>                
//         */ }).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g, ''),objV); 
//         html += str
//     }
// }else{
//     html += dom_no_info()
// }
// 热销店铺
// var objShop = response.category_id_shop
// var msg = bg_i18n("lang_hot_store")
// html += "</div></div>"
// html += "<hr class='mg-top-20' /><h3>"+msg+"</h3><div class='mg-top-30'><div>"
// if(objShop.length > 0){
//     for(var i in objShop){
//         var objV = objShop[i]
//         objV.sales_pic = chrome.runtime.getURL('img/sales.png')
//         var str = pub_format(String(function(){/*!
//             <div class='inline mg-left-40'>
//                 <div class='inline'>
//                    <a target="_blank" href="#{store_url}"> <img class="product-img-2" src="#{store_logo}" /> </a>
//                 </div>
//                 <div class="valign-b font-13 font-w-600 from-supply">
//                     <div title="#{store_name}">#{store_name}</div>
//                     <div><img src="#{sales_pic}" style="width:20px">#{trade_total}</div>
//                 </div>
//             </div>                  
//         */ }).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g, ''),objV); 
//         html += str
//     }
// }else{
//     html += dom_no_info()
// }
// 新品
// var objTopNewProduct = response.top2Newproducts
// html += "</div></div>"
// var msg = bg_i18n('lang_top_2_new_products')
// var loginDomButton = createLoginOrRegDom()
// html += "<hr class='mg-top-20' /><h3>"+msg+"</h3><div class='mg-top-30'><div>"
// if(objTopNewProduct.length > 0){
//     for(var i in objTopNewProduct){
//         var objV = objTopNewProduct[i]
//         var str = pub_format(String(function(){/*!
//             <div class='inline mg-left-40'>
//                 <div class='inline'>
//                    <a target="_blank" href="#{product_url}"> <img class="product-img-2" src="#{product_image}" /> </a>
//                 </div>
//                 <div class="valign-b font-13 font-w-600 from-supply">
//                     <div title="#{product_name}">#{product_name}</div>
//                     <div>#{currency} #{min_price}-#{max_price}</div>
//                     <div>#{ratings}<span style="margin-left:3px" class="glyphicon glyphicon-star"></span>(#{reviews_total})</div>
//                 </div>
//             </div>                  
//         */ }).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g, ''),objV); 
//         html += str
//     }
// }else{
//     html += dom_no_info()
// }
html+="</div></div>"}
//6 产品图表数据概览
function createchartDom(e){$(".smt-loading").remove(),"update_tip"!=e&&(pub_isEmpty(e)?createNoInfoTipDom():(1==e.is_est?e.lang_sales_chart=bg_i18n("lang_est_chart"):e.lang_sales_chart=bg_i18n("lang_sales_chart"),e.lang_est_chart=bg_i18n("lang_est_chart"),e.lang_sales_chart=bg_i18n("lang_sales_chart"),e.lang_review_chart=bg_i18n("lang_review_chart"),e.lang_favted_chart=bg_i18n("lang_favted_chart"),e.lang_price_chart=bg_i18n("lang_price_chart"),e.lang_total=bg_i18n("lang_total"),e.lang_inc=bg_i18n("lang_inc"),e.loginDom=createLoginOrRegDom(),e.lang_year_chart=bg_i18n("lang_year_chart"),uniteInsertMenuContentDom(pub_format(String(function(){
/*!
        <div class="product-part-1">
            <div>

                <div style="margin-top:15px">       
                    <button type="button" class="btn btn-default btn-active -total-button" onclick="choseProductChartType('all-total','total')">#{lang_total}</button>
                    <button type="button" class="btn btn-default -inc-button" onclick="choseProductChartType('all-inc','inc')">#{lang_inc}</button>
                </div>     
               
                
                <div class='mgtop10 est-sales-chart'>
                    <div>
                        <h3 class="inline">#{lang_est_chart}</h3>
                        <div class="btn-style h3-button no_login_none">
                            <button type="button" class="btn btn-default btn-active order-total-button" onclick="choseProductChartType('order','total')">#{lang_total}</button>
                            <button type="button" class="btn btn-default order-inc-button" onclick="choseProductChartType('order','inc')">#{lang_inc}</button>
                        </div>
                    </div>
                    #{loginDom}
                    <div class="echart_div no_login_none">
                        <div id="order_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('order_chart','product','',#{product_id})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>
       
        
                <hr class='mg-top-20 sales-180' />
                <div class='mgtop10 sales-180'>
                    <div>
                        <h3 class="inline">#{lang_sales_chart}</h3>
                        <div class="btn-style h3-button no_login_none">
                            <button type="button" class="btn btn-default btn-active trade-total-button" onclick="choseProductChartType('trade','total')">#{lang_total}</button>
                            <button type="button" class="btn btn-default trade-inc-button" onclick="choseProductChartType('trade','inc')">#{lang_inc}</button>
                        </div>
                    </div>
                    #{loginDom}
                    <div class="echart_div no_login_none">
                        <div id="trade_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('trade_chart','product','',#{product_id})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>

                <hr class='mg-top-20' />
                <div class='mgtop10'>
                    <div>
                        <h3 class="inline">#{lang_review_chart}</h3>
                        <div class="btn-style h3-button no_login_none">
                            <button type="button" class="btn btn-default btn-active reviews-total-button" onclick="choseProductChartType('reviews','total')">#{lang_total}</button>
                            <button type="button" class="btn btn-default reviews-inc-button" onclick="choseProductChartType('reviews','inc')">#{lang_inc}</button>
                        </div>
                    </div>
                    #{loginDom}
                    <div class="echart_div no_login_none">
                        <div id="reviews_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('reviews_chart','product','',#{product_id})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>

                <hr class='mg-top-20' />
                <div class='mgtop10'>
                    <div>
                        <h3 class="inline">#{lang_favted_chart}</h3>
                        <div class="btn-style h3-button no_login_none">
                            <button type="button" class="btn btn-default btn-active wishlis-total-button" onclick="choseProductChartType('wishlis','total')">#{lang_total}</button>
                            <button type="button" class="btn btn-default wishlis-inc-button" onclick="choseProductChartType('wishlis','inc')">#{lang_inc}</button>
                        </div>
                    </div>
                    #{loginDom}
                    <div class="echart_div no_login_none">
                        <div id="wishlis_chart" class="div_ec"></div>
                        <div class='chart-year'>
                            <button type="button" class="btn btn-default" onclick="getYearChart('wishlis_chart','product','',#{product_id})">#{lang_year_chart}</button>
                        </div>
                    </div>
                </div>


            </div>
        </div>  
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)),(null==(glob_product_chart_info=e).order?$(".est-sales-chart"):$(".sales-180")).remove(),helpDrawChart(e)))}
//图表渲染拆分函数1 ， 画出图表
function helpDrawChart(e,t="total",n=null){for(var a in e)if("price"!=a&&("trade"!=a||1!=e.is_est)&&"is_est"!=a&&"product_id"!=a&&!(null!=n&&n!=a||"object"!=typeof e[a]&&"array"!=typeof e[a])){var o=e[a];if(null!=o&&""!=o&&null!=o){var i,r=o[t],l=[],_=[];for(i in r)l.push(i),_.push(r[i]);var s,o="store_trade"==a?"bar":"inc"==t?"bar":"line";
// if(type == 'inc'){
//     if(i == 'store_reviews' || i == 'store_wishlis'){
//         chartType = 'line'
//         pub_drawLineOrBarChart(chartType,i+"_chart",objKeys,objValue)
//         console.log(chartType,i+"_chart",objKeys,objValue)
//     }else{
//         chartType = 'bar'
//     }
// }
// if(type =='line'){
//     chartType = 'bar'
//     console.log(chartType,i+"_chart",objKeys,objValue)
//     pub_drawLineOrBarChart(chartType,i+"_chart",objKeys,objValue)
// }
if("order"==a||"trade"==a){s="order"==a?[bg_i18n("order_sales"),bg_i18n("lang_product_price")]:"total"==t?[bg_i18n("lang_180_sales"),bg_i18n("lang_product_price")]:[bg_i18n("lang_180_day_sales_bh"),bg_i18n("lang_product_price")];var d,c,g=[];for(c in d=e.price[t]){var p=d[c],p=Math.floor(100*p)/100;g.push(p)}var m=[_,g];"inc"==t?pub_drawLineOrBarChart(o,a+"_chart",l,_):drawMixChart(o,a+"_chart",l,m,s,"yes")}else pub_drawLineOrBarChart(o,a+"_chart",l,_)}}}
//7 图片DOM(买家秀)
function createimgDom(e){if($(".smt-loading").remove(),"update_tip"!=e)if(0==e||0==e.length)createNoInfoTipDom();else{var t,n='<div id="big-img"><img src="#"></div> <div class="product-part-1"><ul class="img-ul">';for(t in e){var a={val:e[t]};
// html +=  "<li onmouseover='clickBigImg('"+val+"')'  onmouseout='removeBigImg()'>"
// html +=  '<img class="product-img-3" src=" '+val+' ">'
// html += '</li>'
n+=pub_format(String(function(){
/*!
            <li onmouseover='clickBigImg("#{val}")'  onmouseout='removeBigImg()'>
                <img style="cursor:pointer" class="product-img-3" src="#{val}">
            </li>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),a)}uniteInsertMenuContentDom(n+="</ul></div>")}}
//8. 更多 跳转到 ixspy 的站点
function createmoreDom(){window.open("https://ixspy.com/aliexpress/?from=extension")}
//9. 循环遍历多个dom
function cycleSameProductDom(e){var t,n="<div class='mgtop10'><h4 class='inline'>"+bg_i18n("lang_same_as_product")+"</h4><div><div>";if(0==e||0==e.length)return n=n+dom_no_info()+"</div></div>";for(t in e){var a=e[t];n+=pub_format(String(function(){
/*!
            <div class='inline mg-left-40 same-products'>
                <div style="text-align:center">
                    <a href="#{product_url}" target="_blank"> <img class='product-img-2' src="#{product_image}" /> </a>
                </div>
                <div class="inline valign-b font-13 font-w-600 from-supply mg-left-5">
                    <div title="#{product_name}">#{product_name}</div>
                    <div>$#{min_price}-$#{max_price}</div>
                    <div>#{ratings}<span style='margin-left:3px' class='glyphicon glyphicon-star'></span> | #{reviews_count}</div>
                </div>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),a)}return n+="</div></div>"}
//10 获取 shopify 竞品数据后的DOM 渲染
function createdShopifyGoodsInfoDom(e){var t,n='<hr class="mg-top-20" /><div style="margin-top:20px;"><div><h4>'+bg_i18n("lang_shopify_products")+"</h4>"+createLoginOrRegDom();if(0==e.length)return n=n+("<div class='no_login_none'>"+dom_no_info()+"</div>")+"</div></div>";for(t in e){var a=e[t];n+=pub_format(String(function(){
/*!
            <div class='inline mg-left-40 same-products no_login_none'>
                <div>
                    <a href="#{url}" target="_blank"> <img class='product-img-2' src="#{image}" /> </a>
                </div>
                <div class="inline valign-b font-13 font-w-600 from-supply mg-left-5">
                    <div title="#{product_name}">#{product_name}</div>
                    <div>$#{usd_price}</div>
                    <div>#{domain}</div>
                </div>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),a)}return n+="</div></div>"}
//11. 收藏商品的结果处理
function call_collectionGoods(e){var t=e.msg,e=e.data;if($(".smt-tj-loading").remove(),0<$(".smt_favted_goods_box").length&&$(".smt_favted_goods_box").remove(),"need_login"==t)loginKuang();else if("ok"==t){if(alertInfo(bg_i18n("lang_success"),3e3),$("#collection_goods").remove(),$(".text-favted-product").html(bg_i18n("already_favted_product")),$(".text-favted-product").parent().hasClass("smt_already_collect")||$(".text-favted-product").parent().addClass("smt_already_collect"),e){var n,a=e.data.id,o=chrome.runtime.getURL("img/already_favted.png"),i=bg_i18n("lang_already_favted"),r=($(".favted_"+a).attr("src",o),$(".favted_"+a).attr("title",i),$(".favted_"+a).hasClass("smt_already_collect")||$(".favted_"+a).addClass("smt_already_collect"),e.data.favted_ids);if(r.length<2)return;for(n in r){var l=r[n];$(".favted_"+l).attr("src",o),$(".favted_"+l).attr("title",i),$(".favted_"+l).hasClass("smt_already_collect")||$(".favted_"+l).addClass("smt_already_collect")}}}else"already"==t?($("#collection_goods").remove(),alertInfo(bg_i18n("lang_already_here"),2e3),$(".text-favted-product").html(bg_i18n("already_favted_product")),$(".text-favted-product").parent().hasClass("smt_already_collect")||$(".text-favted-product").parent().addClass("smt_already_collect")):"times_exhausted"==t?($("#collection_goods").remove(),alertInfo(bg_i18n("times_exhausted"),2e3)):"lang_no_lu"==t?($("#collection_goods").remove(),alertInfo(bg_i18n("lang_no_lu"),2e3)):(a=$("#collection_goods").text()+"......",
// text = replace("......","")
$("#collection_goods").html(a),alertInfo(bg_i18n("lang_fail"),3e3))}
//12. 产品图表数据里切换 total / inc
function productChartSwitch(e){var t=e.id,e=e.type;"all-total"==t?helpDrawChart(glob_product_chart_info,"total"):"all-inc"==t?helpDrawChart(glob_product_chart_info,"inc"):helpDrawChart(glob_product_chart_info,e,t)}
//13. 店铺图表数据里切换 total / inc
function storeChartSwitch(e){var t=e.id,e=e.type;helpDrawChart(glob_store_chart_info,e,t)}
//14. 插入loadingDom
function createLoadingDom(e){var t=chrome.runtime.getURL("img/loading.png");e="new"==e?'<div class="smt-loading" style = "margin-top: 45px;margin-bottom:0;position: absolute;width: 100%;height: calc(100% - 45px);left:0;top: 0;background-color: white;display: grid;justify-content: center;align-items: center; " > <img style="width:30%;height:auto;margin: 0 auto;" src="'+t+'"></div>':'<div class="smt-loading"> <img src="'+t+'"></div>',$("#inject-hide").after(e)}
//15. 插入数据不存在/获取信息失败的提示
function createNoInfoTipDom(){var e='<h3 class="no_info_tip">'+bg_i18n("lang_no_info")+"</h3>";$("#inject-hide").after(e)}
//16. 登陆注册可见的dom
function createLoginOrRegDom(e="") {
    return ""; // 返回空字符串，不显示登录提示
}
//17.  大菜单统一插入dom
function uniteInsertMenuContentDom(e,t="",n=""){$("#inject-hide").after(e),checkIsLogin(),"product"==t&&null!=n&&(null!=n.shopify_goods_info&&createdShopifyGoodsInfoDom(n.shopify_goods_info),null!=n.source_product_id&&""!=n.source_product_id||$("#found_shopify").remove())}
//18. 向页面中插入一条保存 store_id 的 input
function createStoreIdDom(e){$("#smt-store-id").remove();e="<input id='smt-store-id' value='"+e+"' type='hidden' >";$("body").append(e)}
//19. 向页面的图片部分插入下载按钮，用来批量下载图片 
function createDownloadButtonDom(e){
//读取json配置
//images-view-list
//video-wrap
//sku-property-list
var t=chrome.runtime.getURL("img/down.png");if(-1!==location.href.indexOf("aliexpress.ru/")){var i="",n='<div id="ixspy_sku_image">';
//左侧的图片下载 有可能有视频
if(0<$(".SnowProductGallery_SnowProductGallery__container__zm9pu").length){if(0<$(".download-div").length)return;var r=bg_i18n("lang_download_main_img");i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-list-ru"})}else if(0<$(".SnowProductGallery_SnowProductGallery__galleryContainer__jy810").length){if(0<$(".download-div").length)return;var r=bg_i18n("lang_download_main_img");i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-list-ru-new"})}else 0<$(".SnowProductGallery_SnowProductGallery__previews__v9f35").length?(r=bg_i18n("lang_download_main_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-list-ru-v9f35"}),
//判断是否有视频
0<$('div[class="gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn"] video').length&&0<$('div[class="gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn"] video').length&&(r=bg_i18n("lang_download_video"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"video-div-list-ru-v9f35"}))):0<$(".SnowProductGallery_SnowProductGallery__previewItem__1ryr7").length?(r=bg_i18n("lang_download_main_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-list-ru-v9f35"}),
//判断是否有视频
0<$('div[class="gallery_Gallery__picListWrapper__crhgwn SnowProductGallery_SnowProductGallery__galleryWrapper__1ryr7"] video').length&&0<$('div[class="gallery_Gallery__picListWrapper__crhgwn SnowProductGallery_SnowProductGallery__galleryWrapper__1ryr7"] video').length&&(r=bg_i18n("lang_download_video"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"video-div-list-ru-v9f35"}))):0<$(".SnowProductGallery_SnowProductGallery__previewItem__z6imb").length?(r=bg_i18n("lang_download_main_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-list-ru-v9f35"}),
//判断是否有视频
0<$('div[class="gallery_Gallery__picture__15bdcj gallery_Gallery__imgIsLoading__15bdcj"] video').length&&(r=bg_i18n("lang_download_video"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"video-div-list-ru-v9f35"}))):0<$(".SnowProductGallery_SnowProductGallery__previewItem__xeihu").length&&(r=bg_i18n("lang_download_main_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-list-ru-v9f35"}),0<$(".SnowProductGallery_SnowProductGallery__previewItem__xeihu video").length)&&(r=bg_i18n("lang_download_video"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"video-div-list-ru-v9f35"}));
//是否有视频要插入
0<$(".SnowProductGallery_SnowProductGallery__container__zm9pu video").length?(r=bg_i18n("lang_download_video"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"video-div-list-ru"})):(0<$(".gallery_Gallery__video__re6q0q video").length||0<$(".gallery_Gallery__video__15bdcj video").length)&&(r=bg_i18n("lang_download_video"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"video-div-list-ru-new"})),
//SKU图片下载 有可能有视频
0<$(".SnowSku_SkuPropertyItem__valuesWrap__15vs2.SnowSku_SkuPropertyItem__imagePreWrap__15vs2").length?($(".SnowSku_SkuPropertyItem__valuesWrap__15vs2.SnowSku_SkuPropertyItem__imagePreWrap__15vs2 img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&(n=n+'<img style="display: none" src="'+e+'">')}),r=bg_i18n("lang_download_sku_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-sku-ru"})):0<$(".SnowSku_SkuPropertyItem__optionList__1en61 li").find("img").length?(
//俄罗斯地区sku图下载
$(".SnowSku_SkuPropertyItem__optionList__1en61 li").find("img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&(n=n+'<img style="display: none" src="'+e+'">')}),r=bg_i18n("lang_download_sku_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-sku-ru"})):0<$(".SnowSku_SkuPropertyItem__optionList__9quwp li").find("img").length?(
//俄罗斯地区sku图下载
$(".SnowSku_SkuPropertyItem__optionList__9quwp li").find("img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&(n=n+'<img style="display: none" src="'+e+'">')}),r=bg_i18n("lang_download_sku_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-sku-ru"})):0<$(".SnowSku_SkuPropertyItem__optionList__1lob1 li").find("img").length?(
//俄罗斯地区sku图下载
$(".SnowSku_SkuPropertyItem__optionList__1lob1 li").find("img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&(n=n+'<img style="display: none" src="'+e+'">')}),r=bg_i18n("lang_download_sku_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-sku-ru"})):0<$(".picture_Picture__container__zl190k.SnowSku_SkuPropertyItem__image__nlqpe").length&&($(".picture_Picture__container__zl190k.SnowSku_SkuPropertyItem__image__nlqpe img").each(function(){var e=$(this).attr("src");-1!==e.indexOf("http")&&(n=n+'<img style="display: none" src="'+e+'">')}),r=bg_i18n("lang_download_sku_img"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-sku-ru-new"})),
//下载scription图片
0<$(".SnowProductContent_SnowProductContent__content__1t9gm").length?(r=bg_i18n("lang_download_img_scription"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"scription-div-list-ru"})):0<$(".SnowProductContent_SnowProductContent__content__1dttw").length?-1===(l=window.location.href).indexOf("/apps/product/publish")&&(r=bg_i18n("lang_download_img_scription"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"scription-div-list-ru-new"})):(0<$(".SnowProductContent_SnowProductContent__content__1thry img").length||0<$("#content_anchor img").length)&&(l=window.location.href,r=bg_i18n("lang_download_img_scription"),i+=imgDownloadDomRu(_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"scription-div-list-ru-new"})),
// if ($(".SnowProductGallery_SnowProductGallery__container__v9f35").length > 0) {
//     var lang = bg_i18n('lang_download_img_scription')
//     var obj = { 'down_video': '', 'download_img': downloadImg, 'download_type': '', 'lang': lang }
//     obj.download_type = 'scription-div-list-ru-new'
//     html = html + imgDownloadDomRu(obj);
// }
n+="</div>";i=i+' <button onclick="startGetProductPriceInfoByApi('+getProductsId()+')" class="country-price" style="margin-right:20px;float:right">\n                        <span style=\'font-size: 12px;font-weight: bold\'>'+bg_i18n("lang_check_country_price")+"</span>\n              </button>";
// $($(".SnowProductGallery_SnowProductGallery__container__zm9pu")[0]).after(html)
$($("#ixspy_download")[0]).after('<hr style="margin-top:10px;margin-bottom: 5px" />'),0==$(".country-price").length&&$($("#ixspy_download")[0]).append(i),$($("#ixspy_download")[0]).append(n),
//左侧的图片下载 有可能有视频
0<$(".SnowProductContent_SnowProductContent__content__1vco7").length&&(r=bg_i18n("lang_download_img"),_={down_video:"",download_img:t,download_type:"",lang:r,download_type:"images-div-desc-ru"},$($(".SnowProductContent_SnowProductContent__content__1vco7")[0]).before(imgDownloadDom(_)))}else
//左侧的图片下载 有可能有视频
if(!(0<$(".download-div").length)){var l=window.location.href;if(-1===l.indexOf("/apps/product/publish")){var _={down_video:"",download_img:t,download_type:"",lang:r=bg_i18n("lang_download_img")};if($("ul").hasClass("images-view-list"))_.download_type="images-view-list",$(".images-view-list li:first").before(imgDownloadDom(_));else if(0<$(".slider--box--TJYmEtw > .slider--img--D7MJNPZ").length)
//新样式下的图片批量下载
_.download_type="slider-div-box-img-list",$(".slider--wrap--PM2ajTZ").before(imgDownloadDom(_));else if(0<$(".slider--item--FefNjlj").length)_.download_type="slider--item--FefNjlj",$(".slider--item--FefNjlj:first").before(imgDownloadDom(_));else if(e){var s=e.download_class.image_dom_class;if(s)for(const g in s){var d=s[g];if(0<$(d).length){_.download_type=d+" img",$(d+":first").before(imgDownloadDom(_));break}}}
//右侧的图片下载
$("div").hasClass("sku-property-image")&&(_.download_type="sku-property-list",$(".sku-property-image").parent().parent().append(imgDownloadDom(_))),$("div").hasClass("sku-item--image--mXsHo3h")?(_.download_type="sku-property-list-new",$(".sku-item--image--mXsHo3h").parent().append(imgDownloadDom(_))):0<$(".sku-item--image--jMUnnGA").length&&(_.download_type="sku-item--image--jMUnnGA",$(".sku-item--image--jMUnnGA").parent().append(imgDownloadDom(_)));
//风格店铺批量下载
// flag = $("div").hasClass('mods--sumImageWrap--FTNV4nk')
// if(flag){
//     obj.download_type = 'mods--sumImageWrap--FTNV4nk'
//     $('.mods--sumImageWrap--FTNV4nk div:first').before(imgDownloadDom(obj))
// }
//产品详情部分的图片下载
i=document.querySelector("#product-detail li[ae_button_type='tab_prodetail']"),l=document.querySelector(".comet-anchor-link-active");
//新版详情初始化,假设有视频加入视频下载按钮
if(i?(_.download_type="over_view",$(i).append(imgDownloadDom(_))):l?(_.download_type="over_view-new",$(l).append(imgDownloadDom(_))):0<$("#nav-description").length&&(
//新版详情页的详情图下载 - 可能是因为详情位置偏下导致这里找不到元素,所以下载标签都显示
_.download_type="descript-img-list-download",$("#nav-description").before(imgDownloadDom(_))),$("div").hasClass("video-wrap")&&(_.download_img=t,_.download_type="video-wrap",_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(".video-wrap").append(imgDownloadDom(_))),$(".image-view--previewBox--FyWaIlU").find("div").hasClass("video--wrap--NfR8r9l"))_.download_img=t,_.download_type="new-class-donwload-video",_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(".video--wrap--NfR8r9l").append(imgDownloadDom(_));else if(0<$(".image-view--previewBox--_oNCSe7").length)if($(".image-view--previewBox--FyWaIlU").find("div").hasClass("video--wrap--EhkqzuR")&&(_.download_img=t,_.download_type="video--wrap--EhkqzuR",_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(".video--wrap--EhkqzuR").append(imgDownloadDom(_))),$(".image-view--previewBox--_oNCSe7").find("div").eq(0).hasClass("magnifier--wrap--cF4cafd")){var c='<div><img onclick="toSearchByImgInDetail(this,1)" title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;"></div>';if($(".magnifier--wrap--cF4cafd").find("img").hasClass("detail_page_search_by_img"))return;$(".magnifier--wrap--cF4cafd").append(c)}
//新版轮播处 以图搜图,视频下载操作 - 增加一个鼠标移入事件监听
$(".slider--box--TJYmEtw > .slider--img--D7MJNPZ").on("mouseenter",function(){setTimeout(function(){var e,t=$(".image-view--previewBox--FyWaIlU").find("div").eq(0);t.hasClass("video--wrap--NfR8r9l")?
//视频
$(".video--wrap--NfR8r9l").find("div").hasClass("download-video")||(e=chrome.runtime.getURL("img/down.png"),_.download_img=e,_.download_type="new-class-donwload-video",_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(".video--wrap--NfR8r9l").append(imgDownloadDom(_))):t.hasClass("magnifier--wrap--hQvf3up")&&!$(".magnifier--wrap--hQvf3up").find("img").hasClass("detail_page_search_by_img")&&(
//以图搜图
e='<div><img onclick="toSearchByImgInDetail(this,1)" title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;"></div>',$(".magnifier--wrap--hQvf3up").append(e))},200)}),$(".slider--slider--uRpGJpg .slider--item--FefNjlj").on("mouseenter",function(){setTimeout(function(){var e,t=$(".image-view--previewBox--_oNCSe7").find("div").eq(0);t.hasClass("video--wrap--EhkqzuR")?
//视频
$(".video--wrap--EhkqzuR").find("div").hasClass("download-video")||(e=chrome.runtime.getURL("img/down.png"),_.download_img=e,_.download_type="video--wrap--EhkqzuR",_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(".video--wrap--EhkqzuR").append(imgDownloadDom(_))):(e='<div><img onclick="toSearchByImgInDetail(this,1)" title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;"></div>',t.hasClass("magnifier--wrap--hQvf3up")?$(".magnifier--wrap--hQvf3up").find("img").hasClass("detail_page_search_by_img")||$(".magnifier--wrap--hQvf3up").append(e):t.hasClass("magnifier--wrap--cF4cafd")&&!$(".magnifier--wrap--cF4cafd").find("img").hasClass("detail_page_search_by_img")&&$(".magnifier--wrap--cF4cafd").append(e))},200)});let n=".video--wrap--PJZMDu2";let a=".magnifier--wrap--qjbuwmt",o=".image-view-v2--previewBox--yPlyD6F";$(o).find("div").hasClass("video--wrap--PJZMDu2")&&(_.download_img=t,_.download_type=n,_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(n).append(imgDownloadDom(_))),0<$(o).length&&0<$(o).find(a).eq(0).find("img").length&&(c='<div><img onclick="toSearchByImgInDetail(this,1)" title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;"></div>',$(a).find("img").hasClass("detail_page_search_by_img")||$(a).append(c)),$(".slider--item--RpyeewA").on("mouseenter",function(){setTimeout(function(){var e,t=$(o).find(a).eq(0);0<t.find("img").length?
//视频
$(n).find("div").hasClass("download-video")||(e=chrome.runtime.getURL("img/down.png"),_.download_img=e,_.download_type=n,_.down_video="download-video",_.lang=bg_i18n("lang_download_video"),$(n).append(imgDownloadDom(_))):(e='<div><img onclick="toSearchByImgInDetail(this,1)" title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;"></div>',t.hasClass("magnifier--wrap--qjbuwmt")&&!$(a).find("img").hasClass("detail_page_search_by_img")&&$(a).append(e))},200)})}}}function PyccknnDownload(){var t,e;return-1===window.location.href.indexOf("/apps/product/publish")&&(t={down_video:"",download_img:chrome.runtime.getURL("img/down.png"),download_type:"",lang:bg_i18n("lang_download_img")},0<$("figure").length)&&0!=(e=$("figure").next()).length&&(e=e[0],t.download_type="Product_GalleryBarItem__barItem__11qng",$($(e)[0]).before(imgDownloadDom(t)),0<$(".Product_SkuValuesBar__container__6ryfe").length&&(t.download_type="Product_SkuValueBaseItem__item__o90dx",$($(".Product_SkuValuesBar__container__6ryfe")[0]).append(imgDownloadDom(t))),void
//详情部分的内容 ，视频部分内容
setInterval(()=>{var e=$(".ali-kit_Tabs__titles__1jwwll");1<e.length&&0==$(".ali-kit_Tabs__titles__1jwwll .download-div").length&&(e=e[1],t.download_type="detailmodule_text-image",$($(e).children()[0]).append(imgDownloadDom(t))),$("video").length?1<$(".has_video_here").length||(e=chrome.runtime.getURL("img/down.png"),t.download_img=e,t.has_video_here="has_video_here",t.download_type="Product_GalleryVideo__video__9fbpv",t.down_video="download-video",t.lang=bg_i18n("lang_download_video"),$("video").after(imgDownloadDom(t))):$(".has_video_here").remove();
//第一张图不是VIDEO 的处理
},2e3));
//左侧图片 //Product_GalleryBarItem__barItem__11qng
}
//20. 返回下载图片的按钮
function imgDownloadDom(e){return pub_format(String(function(){
/*!
        <div onclick="downloadImgOrVideo('#{download_type}')" class='inline #{down_video} download-div-left download-div #{has_video_here}' style="width:fit-content">
            <div style="height:20px;line-height:30px;"> <img class='dimg' src='#{download_img}' /> <span style="font-size: 15px;font-weight: bold;">#{lang}</span></div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)}function imgDownloadDomRu(e){return pub_format(String(function(){
/*!
        <div onclick="downloadImgOrVideo('#{download_type}')" class='inline #{down_video} download-div-left download-div #{has_video_here}' style="width:fit-content;height:26px;padding:0 3px">
            <div style="height:20px;line-height:30px;"> <img class='dimg' style="height:14px;margin-bottom:2px" src='#{download_img}' /> <span style="font-size: 12px;">#{lang}</span></div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)}
//20. 下载资源的回调内容
function downloadSources(e){}
//21. 操作成功或者失败的提示框
function alertInfo(e="",t=3e3){$(".alert").remove();e={msg:e},e=pub_format(String(function(){
/*!
        <div class="alert alert-primary smt-alert" role="alert">
            #{msg}
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);$("body").append(e),setTimeout(()=>{$(".alert").hide()},t)}
//22. Chrome 语言文本返回的信息
function bg_i18n(e){return chrome.i18n.getMessage(e)}
//23. 局部信息没有的提示
function dom_no_info(){return"<div class='no_info_tip'>"+bg_i18n("lang_no_info_tip")+"</div>"}
//24. 提醒更新 、 必须更新
function waringUpdate(e,t){var o;0<$(".update-tip").length?0==e&&$(".update-tip").remove():0!=e&&((t={url:t.extention_url,lang_install_func:bg_i18n("lang_install_func"),lang_download_zip:bg_i18n("lang_download_zip"),lang_open_right:bg_i18n("lang_open_right"),lang_move_to_page:bg_i18n("lang_move_to_page"),lang_install_attention:bg_i18n("lang_install_attention"),lang_plugin_update:bg_i18n("lang_plugin_update"),lang_best_new:bg_i18n("lang_best_new"),lang_video_jc:bg_i18n("lang_video_jc"),lang_set_up:bg_i18n("lang_set_up"),lang_update_up:bg_i18n("lang_update_up"),lang_click_play:bg_i18n("lang_click_play"),set_360:"https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/aliexpress/extention/360_setup.mp4",update_360:"https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/aliexpress/extention/360_update.mp4",set_chrome:"https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/aliexpress/extention/chrome_setup.mp4",update_chrome:"https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/aliexpress/extention/chrome_update.mp4",set_qq:"https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/aliexpress/extention/qq_setup.mp4",update_qq:"https://ixspy-cn.oss-cn-hangzhou.aliyuncs.com/aliexpress/extention/qq_update.mp4",lang_360_set:bg_i18n("lang_360_set"),lang_chrome_set:bg_i18n("lang_chrome_set"),lang_qq_set:bg_i18n("lang_qq_set"),lang_360_update:bg_i18n("lang_360_update"),lang_chrome_update:bg_i18n("lang_chrome_update"),lang_qq_update:bg_i18n("lang_qq_update"),update_content:t.update_content,update_title:bg_i18n("lang_update_title"),lang_no_set_360:bg_i18n("lang_no_set_360"),lang_no_set_qq:bg_i18n("lang_no_set_qq"),lang_remark:bg_i18n("lang_remark")}).close=1==e?"<span onclick='closeUpdateTip()' class='glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>":"",o=pub_format(String(function(){
/*!
        <div class="update-tip">
            <div>#{close}</div>
            <div style="padding:25px">
                <div>
                    <h3 style="text-align:center">#{lang_plugin_update}</h3>
                </div>
                <div style="margin-top:70px">
                    <div style="text-align:center">
                        <a href="#{url}"> <span class="d-button-chrome"> #{lang_best_new} </span> </a>
                    </div>
                </div>
                
                <div>
                    <p style="font-size:20px">#{update_title}</p>
                    <div style="margin-left:25px;">
                        #{update_content}
                    </div>
                </div>

                <div>
                    <p style="font-size:20px">#{lang_remark}</p>
                    <div style="margin-left:25px;">
                        <p>1. <a style="color:white"  href="#">#{lang_no_set_360}</a> </p>
                        <p>2. <a style="color:white"  href="#">#{lang_no_set_qq}</a> </p>
                    </div>
                </div>

                <div style="line-height:2;margin-top:25px">
                    <p style="font-size:20px">#{lang_video_jc}(#{lang_click_play})</p>
                    
                    <div>
                        <p style="margin-left:25px">1.#{lang_set_up}:</p>
                        <div style="margin-left:50px">
                            <p style="text-decoration: underline;">3. <a style="color:white" target='_blank' href="#{set_chrome}">#{lang_chrome_set}</a> </p>
                        </div>
                    </div>

                    <div>
                        <p style="margin-left:25px">2.#{lang_update_up}:</p>
                        <div style="margin-left:50px">
                            <p style="text-decoration: underline;">1. <a style="color:white" target='_blank' href="#{update_chrome}">#{lang_chrome_update}</a> </p>
                        </div>
                    </div>
                </div>



                <div style="line-height:2;margin-top:20px">
                    <p style="font-size:20px">#{lang_install_func}</p>
                    <p>1.#{lang_download_zip}</p>
                    <p>2.#{lang_open_right}</p>
                    <p>3.#{lang_move_to_page}</p>
                    <p>#{lang_install_attention}</p>
                </div>
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t),setTimeout(()=>{uniteInsertMenuContentDom(o);var n=60,a=setInterval(()=>{var e;n<1&&clearInterval(a),790<=window.screen.height?$(".no_info_tip").css("height","650px"):(e=.5*window.screen.height,$(".no_info_tip").css("height",e+"px"));let t=$(".product-part-1").css("height");null!=t&&(t=t.replace("px",""),t=parseInt(t)+20,$(".update-tip").css("height",t+"px"),--n)},2e3)},1e4))}
//25.渲染站点信息
function siteMessageNotify(e){
//渲染站点消息
if(!(0<$(".site-notify").length)){var t,n,a,o="<div class='site-notify'><h3>"+bg_i18n("lang_notify")+"</h3><span onclick='closeNotify()' class='notify-close glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>",i="";for(t in e)isNaN(t)||
////console.log(data[i],data[i].is_new)
1==e[t].is_new&&(i+=(n=e[t]).msg_id+",",n.created_at=pub_timeToMDY(n.created_at),"en"==bg_i18n("lang_a")?(n.title=n.en_title,n.msg_content=n.en_msg_content,n.button_name=n.button_name_en):n.button_name=n.button_name_zh,(a="")!=n.a_href&&null!=n.a_href&&(-1===n.a_href.indexOf("http")&&(n.a_href="https://ixspy.com"+n.a_href),a=pub_format(String(function(){
/*!
                
                <div style="text-align: right;margin-right: 15px;font-size: 20px;font-weight: bold;cursor: pointer;">
                    <a style="color:black" href="#{a_href}" target="_blank"> #{button_name} </a>
                </div>
              
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),n)),n.button_dom=a,o+=pub_format(String(function(){
/*!
            <div class="notify-content">
                <div class="notify-border">
                    <h4>#{title}</h4>
                    <div>#{msg_content}</div>
                    <div>
                        #{button_dom}
                    </div>
                </div>
                
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),n));o=o+"</div>"+("<input type='hidden' value='"+i+"' id='notifyIds' />"),""!=i&&uniteInsertMenuContentDom(o)}}
//26.渲染年图表数据
function updateYearChart(e){if("no_login"==e)loginKuang();else if("no_permissions"==e)alertInfo(bg_i18n("lang_no_permissions"),4e3);else{var t=e.chart_type,n=($("#"+t).removeAttr("_echarts_instance_").empty(),[]);if(glob_store_chart_info.price=e.data.price,glob_product_chart_info.price=e.data.price,"store_trade_chart"==t&&(n=e.data.store_trade.total,glob_store_chart_info.store_trade=e.data.store_trade),"store_wishlis_chart"==t&&(n=e.data.store_wishlis.total,glob_store_chart_info.store_wishlis=e.data.store_wishlis),"store_reviews_chart"==t&&(n=e.data.store_reviews.total,glob_store_chart_info.store_reviews=e.data.store_reviews),"price_chart"==t&&(n=e.data.price.total,glob_product_chart_info.price=e.data.price),"reviews_chart"==t&&(n=e.data.reviews.total,glob_product_chart_info.reviews=e.data.reviews),"trade_chart"==t&&(n=e.data.trade.total,glob_product_chart_info.trade=e.data.trade),"wishlis_chart"==t&&(n=e.data.wishlis.total,glob_product_chart_info.wishlis=e.data.wishlis),"order_chart"==t&&(n=e.data.order.total,glob_product_chart_info.order=e.data.order),"store_trade_chart"==t){var a=[],o=[],i=[],r=[];for(s in"store_trade_chart"==t?_=e.data.store_trade.total:"trade_chart"!=t&&"order_chart"!=t||(_=e.data.trade.total),_)i.push(_[s]),a.push(s);o=[i];var l=[bg_i18n("order_sales")];drawMixChart("bar",t,a,o,l,"yes")}else if("trade_chart"==t||"order_chart"==t){var _,s,a=[],o=[],i=[],r=[],d=e.data.price.total;for(s in _=e.data.trade.total)i.push(_[s]),null==d[s]||""==d[s]?r.push(0):r.push(d[s]),a.push(s);o=[i,r];l=[];l="order_chart"==t?[bg_i18n("order_sales"),bg_i18n("lang_product_price")]:[bg_i18n("lang_180_sales"),bg_i18n("lang_product_price")],drawMixChart("line",t,a,o,l,"yes")}else drawSingleChart(t,n,"line")}}
//27. 渲染单个图，而不是全部渲染
function drawSingleChart(e,t,n){var a,o=[],i=[];for(a in t)o.push(a),i.push(t[a]);pub_drawLineOrBarChart(n,e,o,i)}
//28. 统计数据的渲染
function tjProductData(a){
////console.log('统计产品数据',data)
$(".smt-tj-loading").remove();if("no_login"==a)loginKuang();else if("product_ids_empty"==a||"no_permissions"==a)alertInfo(bg_i18n("lang_"+a),3e3);else{a.trade_total_new=pub_numTransKm(a.trade_total),a.sales_7_new=pub_numTransKm(a.sales_7),a.wishlist_total_new=pub_numTransKm(a.wishlist_total),a.reviews_total_new=pub_numTransKm(a.reviews_total),a.lang_data_tj=bg_i18n("lang_data_tj"),a.lang_180_sales=bg_i18n("lang_180_sales"),a.lang_7_sales=bg_i18n("lang_7_sales"),a.lang_total_wishlist=bg_i18n("lang_total_wishlist"),a.lang_total_reviews=bg_i18n("lang_total_reviews"),a.lang_product_word=bg_i18n("lang_product_word"),a.lang_180_sales_chart=bg_i18n("lang_180_sales_chart"),a.lang_export_data=bg_i18n("lang_export_excel");var e=[];for(t in a.trade_chart_data)e.push({date:t,value:a.trade_chart_data[t]});$(".smt-loading").remove(),a.words.forEach(e=>{e.name=e.name.replaceAll("'","`")}),a.category_list.forEach(e=>{e.store_name=e.store_name.replaceAll("'","`")}),a.export_data=JSON.stringify([[{trade_total:a.trade_total_new,sales_7:a.sales_7_new,wishlist_total:a.wishlist_total_new,reviews_total:a.reviews_total_new}],a.words,e,a.category_list]),a.export_header=JSON.stringify([{trade_total:a.lang_180_sales.replaceAll("'",""),sales_7:a.lang_7_sales.replaceAll("'",""),wishlist_total:a.lang_total_wishlist.replaceAll("'",""),reviews_total:a.lang_total_reviews.replaceAll("'","")},{name:bg_i18n("keywords").replaceAll("'","").replaceAll('"',"`"),value:bg_i18n("lang_total").replaceAll("'","")},null,{store_name:bg_i18n("lang_store_name").replaceAll("'","").replaceAll('"',"`"),store_url:"link",product_count:bg_i18n("lang_products").replaceAll("'",""),sales_7:bg_i18n("lang_7_sales").replaceAll("'",""),trade_total:bg_i18n("lang_180_sales").replaceAll("'","")}]);var t,n=new Date,n=(a.export_name=a.lang_data_tj+"_"+n.getFullYear()+(n.getMonth()+1)+n.getDate(),a.export_sheets=JSON.stringify([a.lang_data_tj.replaceAll("'",""),a.lang_product_word.replaceAll("'",""),a.lang_180_sales_chart.replaceAll("'",""),bg_i18n("lang_store").replaceAll("'","")]),pub_format(String(function(){
/*!
        <div class="dialog_tj">
            <span onclick="closeTj()" class='glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>
            <h3>#{lang_data_tj}</h3>
            <p style="cursor: pointer;
    text-decoration: underline;
    font-size: 20px;
    font-weight: 600;" onclick='exportExcel(#{export_data},#{export_header},"#{export_name}",#{export_sheets})'>#{lang_export_data}</p>
            <div>
                <div class="inline-data">#{lang_180_sales}：#{trade_total_new}</div>
                <div class="inline-data">#{lang_7_sales}：#{sales_7_new}</div>
            </div>
            <div>
                <div class="inline-data">#{lang_total_wishlist}：#{wishlist_total_new}</div>
                <div class="inline-data">#{lang_total_reviews}：#{reviews_total_new}</div>
            </div>
            <div>
                <h4 style="margin-bottom:0px;margin-top:25px;">#{lang_product_word}</h4>
                <div id="words_chart" style="width:650px;height:400px;"></div>
            </div>

            <div style="text-align；center">
                <h4 style="margin-bottom:0px">#{lang_180_sales_chart}</h4>
                <div id="sales_chart" style="width:650px;height:400px;"></div>
            </div>
            
            <div id="tableData">
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),a)),o=($("body").append(n),setTimeout(()=>{$("#words_chart").removeAttr("_echarts_instance_").empty(),$("#sales_chart").removeAttr("_echarts_instance_").empty();var e,t=[],n=[];for(e in a.trade_chart_data)t.push(e),n.push(a.trade_chart_data[e]);pub_drawLineOrBarChart("line","sales_chart",t,n,"no"),drawWords(a.words,"words_chart")},1500),'<table class="plug-table" style="margin:0 auto;margin-top:25px;margin-bottom:20px;" border="1"  cellspacing="0">');for(t in o+="<tr> <th>"+bg_i18n("lang_store_name")+"</th> <th>"+bg_i18n("lang_products")+"</th><th>"+bg_i18n("lang_7_sales")+"</th><th>"+bg_i18n("lang_180_sales")+"</th> </tr>",a.category_list){var i=a.category_list[t];o+=pub_format(String(function(){
/*!
            <tr>
                <td style="text-align:left"> 
                <img src="#{store_logo}" style="width:50px">    
                <a style="color:black" href="#{store_url}" target="_blank"> #{store_name} </a>
                </td>

                <td>#{product_count}</td>

                <td>#{sales_7}</td>
                <td>#{trade_total}</td>
            
            </tr>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),i)}$("#tableData").children().remove(),$("#tableData").append(o)}}
//29. 插入loadingDom
function createTjLoadingDom(){var e='<div class="smt-tj-loading"><img style="width:50px;margin-top: 50px;" src="'+chrome.runtime.getURL("img/loading.png")+'"></div>';$("body").append(e)}
//30.画出小图表
function creatSmallChart(e){var t,n=$("#small-chart-"+e.product_id).find("img"),n=(0<n.length&&$(n[0]).remove(),$("#"+void 0).removeAttr("_echarts_instance_").empty(),$(".smt-tj-loading").remove(),"small-chart-"+e.product_id),a=($("."+n).remove(),[]),o=[],i="";if(1==e.is_est){for(var r in i=bg_i18n("lang_est_chart"),e.order.total)a.push(r),o.push(e.order.total[r]);
//计算七天 和 30 天的 销量
computerDaySales(e.order.inc,30),computerDaySales(e.order.inc,7)}else for(var r in i=bg_i18n("lang_180_sales_chart"),computerDaySales(e.trade.inc,30),computerDaySales(e.trade.inc,7),e.trade.total)a.push(r),o.push(e.trade.total[r]);
// 计算七天销量和三十日销量
0==a.length?(t=bg_i18n("lang_no_data"),$("#"+n).remove(),0==$("."+e.product_id+"_h3").find(".nodata-div").length&&$("."+e.product_id+"_h3").append("<h4 class='nodata-div' style='text-align:center;margin-top:10px'>"+t+"</h4>")):($(".h_"+n).remove(),t=bg_i18n("look_more"),i={id:n,langTemp:i},i=pub_format(String(function(){
/*!
            <div class="#{id}">
                <h5 style='text-align:center;margin-bottom:0px;font-weight:bold' class='h_#{id}'>#{langTemp}</h5>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),i),$("#"+n).before(i),drawSmallBarNew(n,a,o),$("#"+n).after('<div class="look-more '+n+'" onclick="lookMoreChart(this,event)" value="'+n+'">'+t+"</div>")),e.seven_data.seven_country_data=e.seven_country_data,e.seven_data.wishlist_count=e.wishlist_count,e.seven_data.reviews_count=e.reviews_count,appendSevenDataInfo(e.seven_data,e.product_id)}
//插入产品上的七天数据
async function appendSevenDataInfo(e,t){if(null!=e.category_path?(e.category_path_name=await productCategoryNew(e.category_path),void 0===e.category_path_name?e.category_path_name="--":(e.category_name=e.category_path_name.split(" > "),e.category_name=e.category_name[e.category_name.length-1])):(e.category_path_name="--",e.category_name="--"),e.id=t,e.order_avg_7=bg_i18n("order_avg_7"),e.order_rate_7=bg_i18n("order_rate_7"),e.favted_avg_7=bg_i18n("favted_avg_7"),e.favted_rate_7=bg_i18n("favted_rate_7"),e.first_date=bg_i18n("lang_first_discover"),e.lang_last_update=bg_i18n("lang_last_update"),e.lang_180_sales_new=bg_i18n("lang_180_sales_new"),e.lang_product_total_reviews=bg_i18n("lang_product_total_reviews"),e.lang_product_wishlist=bg_i18n("lang_product_wishlist"),e.class_seven_value=bg_i18n("class_seven_value"),e.class_seven_label=bg_i18n("class_seven_label"),e.seven_label_title=bg_i18n("seven_label_title"),e.country_dis=bg_i18n("country_dis"),e.lang_category=bg_i18n("lang_category"),"2019-12-31"==e.date&&(e.date="不详"),strCountrys="","-"!=e.seven_country_data){var n,a=e.seven_country_data.split(",");for(n in strCountrys="<div style='width:85px;text-align:left;margin:0 auto;display: inline-block;vertical-align: text-top;margin-left:10px;'>",a)strCountrys+="<div style='color:red'>"+a[n]+"</div>";strCountrys+="</div><div style='clear:both'></div>"}else strCountrys="-";e.strCountrys=strCountrys,$(".sales-total-"+t).remove();var o=pub_format(String(function(){
/*!
        <span style="position:relative;" class="sales-total-#{id} sales-total-div"  onmouseout="dialoupdateout('#{id}')"  onmouseover="dialogupdateover('#{id}')">
                    <span class="sold-content"> #{trade_count}  sold</span>
                    <div class="endupdate-#{id} endupdate">#{lang_last_update}:#{computer_date}</div>
                    </span> 
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);0==$(".manhattan--trade--2PeJIEB").length&&$(".sales-"+t).prepend(o),0<$(".seven-data-product-"+t).length||(o=pub_format(String(function(){
/*!
        <div id='ixspy-product-id-#{id}' onclick="clickSmallChart(#{id},event)" style="flex-grow:120" class="temp-seven-data seven-data-product-#{id}">
            <div class="class-7-30">
                <div class="mbottom-5">
                    <hr style="margin:5px;" />
                    <div><span class="#{seven_label_title}">7 Days Stat</span></div>
                    <div><span class="#{class_seven_label}">#{order_avg_7}</span>:<span class="#{class_seven_value}"> #{sales_7}</span></div>
                    <div><span class="#{class_seven_label}">#{order_rate_7}</span>:<span class="#{class_seven_value}"> #{sales_inc_rate_7}%</span></div>
                    <div><span class="#{class_seven_label}">#{favted_avg_7}</span>:<span class="#{class_seven_value}"> #{favted_7}</span></div>
                    <div><span class="#{class_seven_label}">#{favted_rate_7}</span>:<span class="#{class_seven_value}"> #{favted_inc_rate_7}%</span></div>
                    <hr style="margin:5px;" /> 
                    <div><span class="#{class_seven_label}">#{lang_product_total_reviews}</span>:<span class="#{class_seven_value}"> #{reviews_count}</span></div>
                    <div><span class="#{class_seven_label}">#{lang_product_wishlist}</span>:<span class="#{class_seven_value}"> #{wishlist_count}</span></div>
                    <div><span class="#{class_seven_label}">#{first_date}</span>:<span class="#{class_seven_value}">#{date}</span></div>
                    <hr style="margin:5px;" />
                    <div><span style="display:inline-block" class="#{class_seven_label}">#{country_dis}:</span>#{strCountrys}</div>
                    <hr style="margin:5px;" />
                    <div title="#{category_path_name}"><span style="display:inline-block" >#{lang_category}:</span>#{category_name}</div>
                    <div style="clear:both"></div>
                </div>
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e),"absolute"===$(".self-my-icon-"+t).css("position")&&(o=pub_format(String(function(){
/*!
        <div id='ixspy-product-id-#{id}' onclick="clickSmallChart(#{id},event)" style="flex-grow:120;height:300px;width: 100%;" class="temp-seven-data seven-data-product-#{id}">
            <div class="class-7-30" style="position: absolute;z-index: 100;width: 100%;">
                <div class="mbottom-5">
                    <hr style="margin:5px;" />
                    <div><span class="#{seven_label_title}">7 Days Stat</span></div>
                    <div><span class="#{class_seven_label}">#{order_avg_7}</span>:<span class="#{class_seven_value}"> #{sales_7}</span></div>
                    <div><span class="#{class_seven_label}">#{order_rate_7}</span>:<span class="#{class_seven_value}"> #{sales_inc_rate_7}%</span></div>
                    <div><span class="#{class_seven_label}">#{favted_avg_7}</span>:<span class="#{class_seven_value}"> #{favted_7}</span></div>
                    <div><span class="#{class_seven_label}">#{favted_rate_7}</span>:<span class="#{class_seven_value}"> #{favted_inc_rate_7}%</span></div>
                    <hr style="margin:5px;" /> 
                    <div><span class="#{class_seven_label}">#{lang_product_total_reviews}</span>:<span class="#{class_seven_value}"> #{reviews_count}</span></div>
                    <div><span class="#{class_seven_label}">#{lang_product_wishlist}</span>:<span class="#{class_seven_value}"> #{wishlist_count}</span></div>
                    <div><span class="#{class_seven_label}">#{first_date}</span>:<span class="#{class_seven_value}">#{date}</span></div>
                    <hr style="margin:5px;" />
                    <div><span style="display:inline-block" class="#{class_seven_label}">#{country_dis}:</span>#{strCountrys}</div>
                    <hr style="margin:5px;" />
                    <div title="#{category_path_name}"><span style="display:inline-block" >#{lang_category}:</span>#{category_name}</div>
                    <div style="clear:both"></div>
                </div>
            </div>

        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)),$(".self-my-icon-"+t).before(o),e=localStorage.getItem("smt_sales_auto"),0==localStorage.getItem("smt_level")&&$(".temp-seven-data").css("display","none"),0==e&&$(".temp-seven-data").css("display","none"))}
//计算每日销量
function computerDaySales(e,t){var n=(a=Object.keys(e)).length;if(n<t)return"--";var a=[];for(r in e){var o=e[r];a.push(o)}for(var i=0,r=1;r<=t;r++)i+=parseInt(a[n-r]);return i}
//31.插入Loading 层
function createSmallLoadingDom(e){var t='<div class="smt-small-loading"><img style="width: 40px;margin-top:5px;margin-left: 60px;" src="'+chrome.runtime.getURL("img/loading.png")+'"></div>';-1!==e.indexOf("store_product_")?$("."+e).before(t):$("#"+e).append(t)}
//32.加入产品收藏之后的回调
function favtedProductCallBack(e){if($(".smt-tj-loading").remove(),0<$(".smt_favted_goods_box").length&&$(".smt_favted_goods_box").remove(),0==e)loginKuang();else if(0!=e.error.code)"product_info_exit"==(t=e.error.message)&&alertInfo(bg_i18n("lang_no_lu"),5e3),"id_is_empty"==t&&alertInfo("Error",3e3),"need_login"==t&&loginKuang();else{alertInfo(bg_i18n("lang_success"));var t=e.data.id,n=chrome.runtime.getURL("img/already_favted.png"),a=bg_i18n("lang_already_favted"),o=($(".favted_"+t).attr("src",n),$(".favted_"+t).attr("title",a),e.data.favted_ids);if(!(o.length<2))for(var i in o){i=o[i];$(".favted_"+i).attr("src",n),$(".favted_"+i).attr("title",a)}}}
//33.渲染已收藏的产品ids
function alreadyFavtedIds(r){$(".smt-tj-loading").remove(),setTimeout(()=>{null!=r&&""!=r&&(r=JSON.parse(r));var e,t=chrome.runtime.getURL("img/already_favted.png"),n=bg_i18n("lang_already_favted"),a=bg_i18n("already_favted_product");for(e in r){var o=r[e],i=($(".favted_"+o).attr("src",t),$(".favted_"+o).attr("title",n),$(".smt_already_collect_"+o));0<i.length&&i.addClass("smt_already_collect"),0<$(".favted_"+o).length&&$(".favted_"+o).addClass("smt_already_collect"),i.html(a)}},1500)}
//34.弹出登陆跳转框
function loginKuang() {
    return false; // 不显示登录框
}
function loginTip() {
    return false; // 不显示登录提示
}
// 7. 修改个人中心显示
function showMyPersion(e) {
    // 创建模拟用户数据
    let userData = {
        user_id: "premium_user",
        email: "<EMAIL>",
        level: "22",
        version: "1.7.9"
    };
    
    // 显示个人中心，使用模拟数据
    let html = `
        <div class="product-part-1">
            <div class="user-pic-div">
                <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                <span class="user-email"><EMAIL></span>
            </div>
            <div class="user-level">
                <span>Premium Level</span>
            </div>
        </div>
    `;
    
    uniteInsertMenuContentDom(html, "my_persion", userData);
}
// 8. 修改登录状态检查
function checkIsLogin() {
    call_isLogin("ok"); // 直接调用成功回调
}
// 9. 修改登录状态回调
function call_isLogin(e) {
    // 始终显示已登录状态
    $(".no_login_block").css("display", "none");
    $(".no_login_none").css("display", "block");
    $(".h3-button").css("display", "block");
}
// 10. 禁用登录框
function loginKuang() {
    return false; // 不显示登录框
}
// 11. 禁用登录提示
function loginTip() {
    return false; // 不显示登录提示
}
// 12. 修改个人中心显示
function showMyPersion(e) {
    // 创建模拟用户数据
    let userData = {
        user_id: "premium_user",
        email: "<EMAIL>",
        level: "22",
        version: "1.7.9"
    };
    
    // 显示个人中心，使用模拟数据
    let html = `
        <div class="product-part-1">
            <div class="user-pic-div">
                <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                <span class="user-email"><EMAIL></span>
            </div>
            <div class="user-level">
                <span>Premium Level</span>
            </div>
        </div>
    `;
    
    uniteInsertMenuContentDom(html, "my_persion", userData);
}
// 13. 禁用登录框
function loginKuang() {
    return false; // 不显示登录框
}
// 14. 禁用登录提示
function loginTip() {
    return false; // 不显示登录提示
}
// 15. 修改个人中心显示
function showMyPersion(e) {
    // 创建模拟用户数据
    let userData = {
        user_id: "premium_user",
        email: "<EMAIL>",
        level: "22",
        version: "1.7.9"
    };
    
    // 显示个人中心，使用模拟数据
    let html = `
        <div class="product-part-1">
            <div class="user-pic-div">
                <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                <span class="user-email"><EMAIL></span>
            </div>
            <div class="user-level">
                <span>Premium Level</span>
            </div>
        </div>
    `;
    
    uniteInsertMenuContentDom(html, "my_persion", userData);
}
// 16. 禁用登录框
function loginKuang() {
    return false; // 不显示登录框
}
// 17. 禁用登录提示
function loginTip() {
    return false; // 不显示登录提示
}
// 18. 修改个人中心显示
function showMyPersion(e) {
    // 创建模拟用户数据
    let userData = {
        user_id: "premium_user",
        email: "<EMAIL>",
        level: "22",
        version: "1.7.9"
    };
    
    // 显示个人中心，使用模拟数据
    let html = `
        <div class="product-part-1">
            <div class="user-pic-div">
                <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                <span class="user-email"><EMAIL></span>
            </div>
            <div class="user-level">
                <span>Premium Level</span>
            </div>
        </div>
    `;
    
    uniteInsertMenuContentDom(html, "my_persion", userData);
}
// 19. 禁用登录框
function loginKuang() {
    return false; // 不显示登录框
}
// 20. 禁用登录提示
function loginTip() {
    return false; // 不显示登录提示
}
// 21. 修改个人中心显示
function showMyPersion(e) {
    // 创建模拟用户数据
    let userData = {
        user_id: "premium_user",
        email: "<EMAIL>",
        level: "22",
        version: "1.7.9"
    };
    
    // 显示个人中心，使用模拟数据
    let html = `
        <div class="product-part-
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e));$("body").append(e)}
//35 初始状态
function initCss(){$(".inject-button .container").addClass("important-none"),$(".inject-button .tools-width").addClass("tools-width-1")}
//36. 设置完国家后刷新页面
function setSmtCountry(){window.location.reload()}function timerI18nTT(){var e=$("[class^='tt-']");if(0!=e.length){var t,n=[];for(t in e)if(!isNaN(t))try{var a=$(e[t]).attr("class");n.push(a)}catch(e){
//console.log(error,name)
}for(t in n){var o,i=(e=(a=n[t]).split("-"))[1],r="";"is_free_carrier"!=i&&(r="lang_sku"==i?(o=$(".tt-lang_sku").html(),(r=bg_i18n(i)).replace("XXX",o)):bg_i18n(i),$("."+a).html(r))}}}function checkPermissionCountryPrice(e){
//console.log('检测权限是否可以采集的回调',res)
$(".alert").hide();var t=e.config_class.config_class,n=e.config_class.config_tj_class;if(localStorage.setItem("smt_config_class",JSON.stringify(t)),localStorage.setItem("smt_config_tj_class",JSON.stringify(n)),0==(e=e.check_country_price))alertInfo(bg_i18n("lang_level_user"),5e3),
//localStorage.setItem('start_collection',0)
window.postMessage({cmd:"remove_product_id"},"*");else{if("no_login"==e)return $(".alert").remove(),
//localStorage.setItem('start_collection',0)
loginKuang(),window.postMessage({cmd:"remove_product_id"},"*"),"no_login";var a,t=typeof e,n="",o=0,i="",r=0;"object"==t||"array"==t?(n=e.res,o=e.nums,i=e.level,r=e.add_nums):n=e,null!=o&&""!=o&&null!=o||(o=1),1==n&&(
//console.log('tip',tip,obj,level)
1==localStorage.getItem("first_tip")&&(localStorage.setItem("first_tip",""),a=pub_compareLevel(i,["2","7","6","11","16","19"])?bg_i18n("lang_begin_collection_tip_no_limit"):(a=(a=bg_i18n("lang_begin_collection_tip")).replace("AAA",o)).replace("BBB",5-o)),1==r?window.postMessage({cmd:"first_start",msg:a},"*"):window.postMessage({cmd:"start_collection",msg:a},"*"))}}
//计算工具栏的高度
//采集进度的插入
function appendCollectProcess(e){e.lang_process=bg_i18n("lang_process"),e.lang_is_collecting=bg_i18n("lang_is_collecting"),e.lang_is_stop=bg_i18n("lang_is_stop");e=pub_format(String(function(){
/*!
        <div class="collection-loading">
            <h4><span class="tt-lang_process">#{lang_process}</span>：#{length2}/#{length1}</h4>
            <h5 class="tt-lang_is_collecting">#{lang_is_collecting}</h5>
            <h5 style="cursor:pointer;" onclick="stopCollectionPrice()"><span style="border-bottom:1px solid" class="tt-lang_is_stop">#{lang_is_stop}</span></h5>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);$("body").append(e)}
//插入国家选择的复选框
function appendCountryChosePrice(e=""){if(null==e.user_id||""==e.user_id||null==e.user_id)loginKuang();else{$(".chose_country_checkboxs").remove(),$(".chose_country_checkboxs_zh").remove();var t,n=localStorage.getItem("country_groups"),a=(""!=n&&null!=n&&"undefined"!=n&&"none"!=n?n=JSON.parse(n):"none"!=n&&(n=""),null==(n=0==n?"":n)&&(n=""),["US","ES","FR","CL","UA","NL","PL","BR","IT","DE","KR","CA","UK","IL","AU","BY","JP","TH","SG","ID","MY","PH","VN","SA","AE","PT","TR","MX","KZ","PE","CO","DZ","MA","NZ","LT","LV","BE","CH","CZ","SK","NO","HU","BG","EE","RO","PK","HR","NG","IE","AT","GR","SE","FI","DK","SI","MT","LK","LU"]),o="",i=!0;for(t in a){var r=a[t],l={country:r,country_name:bg_i18n(r),country_groups_list:bg_i18n("country_groups_list")};
//console.log('countryv',countryv,'aleadyChoseCountry',aleadyChoseCountry)
// if (countryv == 'RU') {
"US"==r?
// checked="true" 
o+=pub_format(String(function(){
/*!
                <div class="#{country_groups_list}"> 
                    <input class="country-chose-input" disabled  id="cc_#{country}" type="checkbox" checked="true"  name="country_groups" value="#{country}"/>
                    <label for="cc_#{country}">#{country_name}</label> 
                </div>           
             */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),l):""!=n&&-1!==n.indexOf(r)&&"none"!=n?o+=pub_format(String(function(){
/*!
                <div class="#{country_groups_list}"> 
                    <input class="country-chose-input"  id="cc_#{country}" checked="true" type="checkbox" name="country_groups" value="#{country}"/>
                    <label for="cc_#{country}" >#{country_name}</label> 
                </div>           
             */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),l):""!=n&&-1===n.indexOf(r)&&"none"!=n?(i=!1,o+=pub_format(String(function(){
/*!
                <div class="#{country_groups_list}"> 
                    <input class="country-chose-input"  id="cc_#{country}"  type="checkbox" name="country_groups" value="#{country}"/>
                    <label for="cc_#{country}">#{country_name}</label> 
                </div>           
             */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),l)):""===n||0==n.length?(t<=9||(i=!1),o+=pub_format(String(function(){
/*!
                    <div class="#{country_groups_list}"> 
                        <input class="country-chose-input"   id="cc_#{country}"  type="checkbox" name="country_groups" value="#{country}"/>
                        <label for="cc_#{country}">#{country_name} </label>    
                    </div>       
                 */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),l)):"none"==n&&(i=!1,o+=pub_format(String(function(){
/*!
                <div class="#{country_groups_list}"> 
                    <input class="country-chose-input"   id="cc_#{country}"  type="checkbox" name="country_groups" value="#{country}"/>
                    <label for="cc_#{country}">#{country_name} </label>    
                </div>       
             */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),l))}
//console.log('domCountry',domCountry)
l={country:o,allcheck:i,lang_all_country:bg_i18n("lang_all_country"),lang_start:bg_i18n("lang_start_collection"),chose_country_checkboxs:bg_i18n("chose_country_checkboxs")},e=pub_format(String(function(){
/*!
        <div class="#{chose_country_checkboxs}">
            <div style="text-align: end;position: absolute;top: -7px;right: 0;">
                <span onclick='removeCountryGroup()' class='glyphicon glyphicon-remove cursor chahca' aria-hidden='true'></span>
            </div>
            <div>
            <input onclick="choseAllCountry(this)" style="opacity: 1 !important;" type="checkbox" id="checkall" value="all"    name="country_all">
                    <label  for="checkall" >#{lang_all_country}</label> 
            </div>
            #{country}

            <div></div>

            <div style="text-align:center">
                <div onclick="getChoseCountryS()" class="start-collection">#{lang_start}</div>
            <div>

            
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),l);
//obj.skus = appendSkuChose()
$("body").append(e);
// if(flag){
//     var dom = $("input[name='country_all']")
//     $(dom).prop('checked', true)
// }else{
//     var dom = $("input[name='country_all']")
//     $(dom).removeAttr('checked')
// }
}}
//插入SKU 的选择属性
function appendSkuChose(){try{var e=localStorage.getItem("smt_config_class");if(!pub_isEmpty(e)){JSON.parse(e);var t=$(".sku-wrap");if(0!=t.length){
//判断每个sku 下有多少个子级
var n,a="",t=t[0],o=$(t).find(".sku-property");for(n in o)if(!isNaN(n)){
//获取属性Title
var i=$(o[n]).find(".sku-title").text(),r=$(o[n]).find(".sku-title-value").text();if(!pub_isEmpty(i)){var l,i=(i=(i=i.replace(r,"")).replace(" ","")).replace(":",""),_=$(o[n]).find("li"),s=$(o[n]).html();
//遍历sku 的选择,添加属性
for(l in _)isNaN(n)||(s=s.replace("sku-property-item",i+" "+i+"_"+l));a+=s=s.replace(r,"")}}return a}}}catch(e){console.log("sku error",a)}}
//点击小图的 look more 显示出工具栏
function showSmallChartMore(e){$(".inject-button").remove(),appendToolDom(e.product_id)}
//显示 七天数据
function showSevenData(e){if($(".smt-small-loading").remove(),localStorage.setItem("get_seven_data",0),0!=e.length)for(var t in e)isNaN(t)||appendSevenDataInfo(e[t],t)}
//显示时间消息内容
function showEventNotify(e){var t;null!=e&&0!=e.length&&0!=e&&null!=(e=e[0])&&(t={},t="zh"==bg_i18n("curr_lang")?{title:e.zh_title,content:e.zh_content}:{title:e.en_title,content:e.en_content},$(".event-notify-extention").remove(),e=pub_format(String(function(){
/*!
        <div class="event-notify-extention">
            <span onclick='closeEventNotify()' class='notify-close glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>
            <h3 style="text-align:center">#{title}</h3>
            <div style="font-size:16px;">#{content}</div>
            <div style="margin-top:40px;text-align:right;font-weight: bold;"><a href="https://ixspy.com" target="_blank">IXSPY.COM</a></div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t),$("body").append(e))}
// function removeCookies(res){
//     console.log(res,111)
//     setTimeout(()=>{
//         window.open("https://www.aliexpress.com")
//     },2000)
// }
//插入产品 和 店铺的收藏按钮, 判断产品,店铺是否搜藏
function appendStoreAndProductFavtedButton(){setInterval(()=>{var e=window.location.pathname;if(-1!==e.indexOf("/item/")){if(0==(n=ixRunParams&&null!=ixRunParams.data.commonModule?ixRunParams.data.commonModule.sellerAdminSeq:getHrefStoreId(t=$(".store-name a").attr("href")))&&0<$(".SnowProductDescription_ExtraInfo__wrap__193uk").length)for(var t,n,a=$(".SnowProductDescription_ExtraInfo__wrap__193uk a"),o=0;o<a.length;o++)-1!==(t=a.eq(o).attr("href")).indexOf("store/")&&(n=getHrefStoreId(t));
// console.log(ixRunParams)
e={lang_check_country_price:bg_i18n("lang_collection_shop"),storeId:n},e=pub_format(String(function(){
/*!
            <div onclick="favtedShop(#{storeId},event)" class="check-country-price extention-store-favted"><span class='text-favted-store'>#{lang_check_country_price}</span></div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);0<$(".store-container").length&&0==$(".extention-store-favted").length?$(".store-container").after(e):0<$(".SnowProductDescription_ExtraInfo__wrap__193uk").length&&0==$(".extention-store-favted").length&&$(".SnowProductDescription_ExtraInfo__wrap__193uk").after(e),
// var obj = {
//     lang_check_country_price:'产品收藏',
//     productId:productId
// }
// var button = pub_format(String(function(){/*!
//     <div onclick="web_favtedGoodsId(#{productId})" class="check-country-price extention-product-favted"><span class="text-favted-product">#{lang_check_country_price}</span></div>
// */ }).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g, ''),obj);
// if($(".check-country-price-tmp").length > 0 && $(".extention-product-favted").length == 0){
//     $(".check-country-price-tmp").after(button)
// }
checkPageProductIsFavted(getProductId(window.location.href),"product"),checkPageProductIsFavted(n,"store")}},3e3)}
//通过href 获取产品id
function getProductId(e){var t,n=e.split("/"),a="";for(t in n){var o=n[t];-1!==o.indexOf("html")&&(a=parseInt(o))}return a}
//通过href 获取店铺id
function getHrefStoreId(e){var t,n;return""!=e&&null!=e&&(
// sellerAdminSe
t=e.indexOf("store/"),t=parseInt(t)+6,-1==(n=e.indexOf("?"))?e.substring(t,e.length):e.substring(t,n))}
//检测产品是否已收藏
function checkPageProductIsFavted(e,t="product"){contentScriptToBackground("is_favted",{id:e,flag:t},function(e){"already"==e&&("product"==t?($(".text-favted-product").html(bg_i18n("already_favted_product")),$(".text-favted-product").parent().hasClass("smt_already_collect")||$(".text-favted-product").parent().addClass("smt_already_collect")):"store"==t&&$(".text-favted-store").html(bg_i18n("already_favted_store")))})}
//收藏店铺id 的回调
function call_collectionStore(e){$(".smt-tj-loading").remove(),"need_login"==e?loginKuang():"ok"==e?(alertInfo(bg_i18n("lang_success"),3e3),$(".text-favted-store").html(bg_i18n("already_favted_store"))):"already"==e?(alertInfo(bg_i18n("lang_already_here"),2e3),$(".text-favted-store").html(bg_i18n("already_favted_store"))):"times_exhausted"==e?alertInfo(bg_i18n("times_exhausted"),2e3):alertInfo(bg_i18n("lang_fail"),3e3)}
//获取产品信息显示在产品页面上
async function appendWebProductInfo(e){var t,n,c,g,p;
// 货币类型 价格 评分 订单数 评论数 收藏数 首次发现时间 最后更新
$(".smt-loading").remove(),"update_tip"!=e&&(0==e?createNoInfoTipDom():(createStoreIdDom(e.admin_seq),localStorage.setItem("same_product",JSON.stringify(e.like_goods)),n=bg_i18n("lang_no_login_button"),e.loginDom=createLoginOrRegDom(n),localStorage.setItem("isIdHere",e.isIdHere),e.km_trade_count=pub_numTransKm(e.trade_count),e.km_reviews_count=pub_numTransKm(e.reviews_count),e.km_wishlist_count=pub_numTransKm(e.wishlist_count),e.shopify_goods_dom=createdShopifyGoodsInfoDom(e.shopify_goods),e.lang_currency=bg_i18n("lang_currey"),e.lang_price=bg_i18n("lang_product_price"),e.lang_rating=bg_i18n("lang_product_rating"),e.lang_total_orders=bg_i18n("lang_product_total_orders"),e.lang_total_reviews=bg_i18n("lang_product_total_reviews"),e.lang_wishlist=bg_i18n("lang_product_wishlist"),e.lang_add_favted=bg_i18n("lang_add_favted"),e.lang_more_product=bg_i18n("lang_more_product_info"),e.pathName=await productCategoryNew(e.category_path),e.category_name=bg_i18n("lang_category"),
// response.add_time_new = pub_timeToMDY(response.add_time)
e.add_time_new=e.add_date,e.update_time_new=pub_timeToMDY(e.update_time),e.lang_first_discover=bg_i18n("lang_first_discover"),e.lang_last_update=bg_i18n("lang_last_update"),
// var href = window.location.href
// var productId = getProductId(href)
// var obj = {
//     lang_check_country_price:'产品收藏',
//     productId:productId
// }
e.lang_favted_product=bg_i18n("click_favted_product"),e.lang_check_country_price=bg_i18n("lang_check_country_price"),e.msg_1688=bg_i18n("lang_1688"),e.same_product=bg_i18n("lang_same_as_product"),e.country_dis=bg_i18n("country_dis"),n=bg_i18n("curr_lang"),e.w7="en"==n?"w7_en":"w7",e.lang_support=bg_i18n("lang_support"),e.lang_analytics=bg_i18n("lang_analytics"),bg_i18n("lang_source"),n="","local"!=(t=localStorage.getItem("set_source"))&&"google"!=t&&"microsoft"!=t||(n=bg_i18n("lang_"+t)),e.lang_words_link=bg_i18n("lang_words_link"),e.set_source=bg_i18n("lang_source")+n,e.product_url="https://www.aliexpress.com"+window.location.pathname,1==e.choice_type?e.lang_choice_type=bg_i18n("half_choice"):2==e.choice_type?e.lang_choice_type=bg_i18n("all_choice"):e.lang_choice_type="POP",c=pub_format(String(function(){
/*!
        <div class='web-page-product-info-int product-main-wrap'>
            <div class='web-page-product-info html-extention-product-info'>
            <div id="ixspy_download">
            </div>
            <div>
                <div>
                    <div style="font-weight: bold;display:inline">
                      #{lang_analytics}
                    </div>
                    <div style="display:inline">
                        <div class="turn-to-ixspy" style="text-decoration: underline;">
                           <a style="color:black" href="https://ixspy.com/data#/dashboard?id=#{product_id}&type=aliexpress_product" target="_blank"> <span>#{lang_more_product}</span> </a>
                        </div>
                        <span onclick="showWordsLinPic()" class='a_1688' style="margin-top:0px;text-decoration: underline;cursor:pointer">
                            <span>#{lang_words_link}</span>
                        </span>
                        <a class='a_1688' style="margin-top:0px;" target="_blank" href='https://global.1688.com/#/search?productUrl=#{product_url}'>
                            <span>#{msg_1688}</span>
                        </a>
                        <span onclick="showSameProduct()" class='a_1688' style="margin-top:0px;display:none;text-decoration: underline;cursor:pointer">
                            <span>#{same_product}</span>
                        </span>
                        <span onclick="web_favtedGoodsId(#{product_id})" class='a_1688 smt_already_collect_#{product_id}' style="margin-top:0px;text-decoration: underline;cursor:pointer">
                            <span class="text-favted-product">#{lang_favted_product}</span>
                        </span>
                        <div style="clear:both"></div>
                    </div>
                </div>
                <hr style="margin-top:5px;" />
                <div>
                    #{category_name}: #{pathName}
                </div>
                
                <div style="margin-top:15px;">
                    #{country_dis}：#{seven_country_data}
                </div>
                <div>
                    
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_currency}</span></div>
                        <div class="six-div-2"><span>#{currency}</span></div>
                    </div>
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span>#{lang_price}</span></div>
                        <div class="six-div-2"><span>#{min_price}-#{max_price}</span></div>
                    </div>

                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_rating}</span></div>
                        <div class="six-div-2"><span>#{ratings}</span></div>
                    </div>
                
                
                
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_total_orders}</span></div>
                        <div class="six-div-2"><span>#{km_trade_count}</span></div>
                    </div>
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_total_reviews}</span></div>
                        <div class="six-div-2"><span>#{km_reviews_count}</span></div>
                    </div>
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_wishlist}</span></div>
                        <div class="six-div-2"><span>#{km_wishlist_count}</span></div>
                    </div>
                
               
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_first_discover}</span></div>
                        <div class="six-div-2"><span>#{add_time_new}</span></div>
                    </div>
                    <div class="inline #{w7}">
                        <div class="six-div-1"><span >#{lang_last_update}</span></div>
                        <div class="six-div-2"><span>#{update_time_new}</span></div>
                    </div>
                </div>
                <div class="web-product-seven-data">
                
                </div>
                <div>

                </div>
            </div>

            <div>
                #{loginDom}
            </div>
            <div>
                <hr style="margin-bottom:5px;" />
                <div style="text-align: right;font-weight: bold;">#{lang_support}
                </div>
            </div>
        </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e),g=e,p=setInterval(()=>{var e=g,t=localStorage.getItem("smt_config_class"),t=JSON.parse(t);if(null!=t){var n=t.product_price;for(o in n){var a=$(n[o]);0<a.length&&0<a.children().length&&0===$(".ixspy_daily_price").length&&($p_html='<div class="ixspy_daily_price" style="font-size: 20px;font-weight:bolder"><span>'+bg_i18n("lang_product_normal_price")+": "+e.currency+"  "+e.min_price+"-"+e.max_price+"</span>",""!=e.lang_choice_type&&($p_html+='<span style="font-size:18px">('+e.lang_choice_type+")</span></div > "),a.before($p_html))}var o,i=t.product_detail;for(o in i){var r=i[o];if(0<$(r).length&&0==$(".html-extention-product-info").length){if(0<$("#ixspy_download").length)return;if(".store-header"==r)0==$(".product-main-wrap").length&&$(r).after(c);else if(".product-main-wrap"==r)$(r).after(c);else if(".ali-kit_Grid__grid__4ttewg"==r)$($(r)[0]).before(c);else if(".SnowProductGallery_SnowProductGallery__container__zm9pu"==r)$($(r)[0]).after(c);else if("#product-detail"==r)$($(r)[0]).before('<div class="bottomWrap--mainWrap--1grGTHK">'+c+"</div>");else if(".title--wrap--wDzGayX"==r)$(r).eq(0).before(c);else if(".title--wrap--NWOaiSp"==r)$(r).eq(0).before(c);else if(0<$("#reviews_anchor").length){$("#reviews_anchor").before(c);let e=$("#reviews_anchor").parent().parent().html(),t=setInterval(()=>{e!=$("#reviews_anchor").parent().parent().html()&&0==$("#ixspy_download").length&&(clearInterval(t),$("#reviews_anchor").before(c),checkIsLogin())},1e3)}else $(r).before(c)}}
// var timerAppendHtml1 = setInterval(()=>{
//     checkIsLogin()
//     timerHtmlNums -= 1
//     if(timerHtmlNums == 0){
//         clearInterval(timerAppendHtml1)
//         return
//     }
//     for(var i in productDetailClass){
//         var detail_div = productDetailClass[i]
//         if($(detail_div).length > 0 ){
//             if(detail_div == '.store-header'){
//                 if($(".product-main-wrap").length == 0){
//                     if($(".html-extention-product-info").length == 0){
//                     $(detail_div).after(html)
//                     }
//                 }
//             }else if(detail_div == '.product-main-wrap'){
//                 if($(".html-extention-product-info").length == 0){
//                 $(detail_div).after(html)
//                 }
//             }else if(detail_div == '.ali-kit_Grid__grid__4ttewg'){
//                 if($(".html-extention-product-info").length == 0){
//                 $($(detail_div)[0]).before(html)
//                 }
//             }else if(detail_div == '.SnowProductGallery_SnowProductGallery__container__zm9pu'){
//                 if($(".html-extention-product-info").length == 0){
//                 $($(detail_div)[0]).after(html)
//                 }
//             }else if(detail_div == '#product-detail'){
//                 if($(".html-extention-product-info").length == 0){
//                 $($(detail_div)[0]).before('<div class="bottomWrap--mainWrap--1grGTHK">'+html+'</div>')
//                 }
//             }else if(detail_div == '.title--wrap--wDzGayX'){
//                 if($(".html-extention-product-info").length == 0){
//                 $(detail_div).eq(0).before(html)
//                 }
//             }else{
//                 if($(".html-extention-product-info").length == 0){
//                 $(detail_div).before(html)
//                 }
//             }
//         }
//     }
//     console.log('重新检测插入产品详情信息')
// },2000)
//七日数据的插入
if(null==e.seven_day_data)return;var t=e.weight+" KG",l=e.width+"*"+e.length+"*"+e.height+" CM",t=((e=e.seven_day_data).order_avg_7=bg_i18n("order_avg_7"),e.order_rate_7=bg_i18n("order_rate_7"),e.favted_avg_7=bg_i18n("favted_avg_7"),e.favted_rate_7=bg_i18n("favted_rate_7"),e.weight_lan=bg_i18n("weight"),e.size_lan=bg_i18n("size"),e.weight=t,e.size=l,e.first_date=bg_i18n("lang_first_discover"),e.class_seven_value=bg_i18n("class_seven_value"),e.class_seven_label=bg_i18n("class_seven_label"),e.seven_label_title=bg_i18n("seven_label_title"),bg_i18n("curr_lang")),_=(e.w7="en"==t?"w7_en":"w7",pub_format(String(function(){
/*!
        <div class="timer-product-detail-info">
            
            <div class="inline #{w7}">
                <div class="six-div-1"><span >#{order_avg_7}</span></div>
                <div class="six-div-2"><span>#{sales_7}</span></div>
            </div>

            <div class="inline #{w7}">
                <div class="six-div-1"><span >#{order_rate_7}</span></div>
                <div class="six-div-2"><span>#{sales_inc_rate_7}%</span></div>
            </div>

            <div class="inline #{w7}">
                <div class="six-div-1"><span >#{favted_avg_7}</span></div>
                <div class="six-div-2"><span>#{favted_7}</span></div>
            </div>

            <div class="inline #{w7}">
                <div class="six-div-1"><span >#{favted_rate_7}</span></div>
                <div class="six-div-2"><span>#{favted_inc_rate_7}%</span></div>
            </div>
            <div class="inline #{w7}">
                <div class="six-div-1"><span >#{weight_lan}</span></div>
                <div class="six-div-2"><span>#{weight}</span></div>
            </div>

            <div class="inline #{w7}">
                <div class="six-div-1"><span >#{size_lan}</span></div>
                <div class="six-div-2"><span>#{size}</span></div> 
            </div>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)),s=10,d=setInterval(()=>{checkIsLogin(),0==s?clearInterval(d):(--s,0<$(".timer-product-detail-info").length||$(".web-product-seven-data").append(_))},2e3)}clearInterval(p)},1e3)))}function showSameProductDom(){var e=localStorage.getItem("same_product");""==e||null==e||0==(e=JSON.parse(e)).length?alertInfo(bg_i18n("lang_no_data")):(e={dom:cycleSameProductDom(e)},e=pub_format(String(function(){
/*!
        <div class="dialog-product-show">
            <div>
                <span onclick='closeSameProduct()' class='glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>
            </div>
            #{dom}
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e),$("body").append(e))}
//显示我的收藏的产品、店铺
function createfavtedDom(n){if($(".smt-loading").remove(),"need_login"==n)loginKuang();else{$(".product-part-1").remove();var a={product_id:"",store_id:""};let e="product";for(l in n){null!=n[l].product_id?(a.product_id="btn-active",e="product"):null!=n[l].store_id&&(a.store_id="btn-active",e="store");break}0<$("#favted_type_value").length&&(e=$("#favted_type_value").val()),""==a.product_id&&""==a.store_id&&("store"==e?a.store_id="btn-active":a.product_id="btn-active"),a.lang_favted_product=bg_i18n("lang_favted_product"),a.lang_favted_store=bg_i18n("lang_favted_store");var o,i=n.arr_tags,r=$("#tag_id_value").val();for(l in o=null==r||""==r||0==r?"<span onclick='clickChoseTag(0,this)' class='tags-span active-tags'>"+bg_i18n("lang_all_tags")+"</span>":"<span onclick='clickChoseTag(0,this)' class='tags-span'>"+bg_i18n("lang_all_tags")+"</span>",i)i[l].id,o+=pub_format(String(function(){
/*!
                <span onclick='clickChoseTag(#{id},this)' class="tags-span active-tags">#{name}</span>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),i[l]);a.tags=o+"<span><a style='cursor:pointer;color:black;text-decoration:underline' href='https://ixspy.com/data#/mycenter/myfavted' target='_blank'>"+bg_i18n("lang_tags_charge")+"</a></span>";var l,_=pub_format(String(function(){
/*!
        <div class="product-part-1">
        <div style="margin-top:10px;">
            <button type="button" class="btn btn-default #{product_id} favted-product" onclick="getMyfavted('product')">#{lang_favted_product}</button>
            <button type="button" class="btn btn-default #{store_id} favted-store" onclick="getMyfavted('store')">#{lang_favted_store}</button>
        </div>
        <hr style="margin:0;margin-top:10px;"/>
        <div style="position:relative">#{tags}</div>
        <ul class="my-favted-ul">
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),a);let t=!0;for(l in n){var s=n[l];if(null!=s){
//处理产品标签
var d,c="";for(d in s.label_names){var g={name:s.label_names[d]};c+=pub_format(String(function(){
/*!
                <span class="tag-span-c">#{name}</span>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),g)}s.tags_name=c,null!=s.store_logo&&""!=s.store_logo&&null!=s.store_logo?(//说明是店铺
t=!1,s.lang_reviews=bg_i18n("lang_reviews"),s.lang_favted=bg_i18n("lang_favted"),s.lang_product_total_orders=bg_i18n("lang_product_total_orders"),s.lang_open_monitor=bg_i18n("lang_open_monitor"),s.lang_check_monitor=bg_i18n("lang_check_monitor"),s.km_reviews_count=pub_numTransKm(s.reviews_count),s.km_wishlist_count=pub_numTransKm(s.wishlist_count),s.km_trade_count=pub_numTransKm(s.trade_count),s.open_monitor=n.open_monitor?"block":"none",s.monitor_id,_+=pub_format(String(function(){
/*!
                    <li>
                        <div class='inline'>
                            <div>
                                <a href="https://aliexpress.com/store/#{store_id}" target="_blank"> <img class="product-img-2" src="#{store_logo}" /> </a>
                            </div>
                            <div class="inline valign-b font-13 font-w-600 from-supply">
                                <div>#{store_name}</div>
                                <div>#{lang_reviews}:#{km_reviews_count}</div>
                                <div>#{lang_favted}:#{km_wishlist_count}</div>
                                <div>#{lang_product_total_orders}:#{km_trade_count}</div>
                            </div>
                            <div>#{tags_name}</div>
                            <div style="display:#{open_monitor}">
                                <button onclick="openMonitor(#{admin_seq})" type="button" class="btn btn-default">#{lang_open_monitor}</button>
                            </div>
                        </div>
                    </li>
                */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),s)):null!=s.product_id&&null!=s.product_id&&""!=s.product_id&&(//说明是产品
t=!1,_+=pub_format(String(function(){
/*!
                <li>
                    <div class='inline'>
                        <div>
                            <a href="#{product_url}" target="_blank"> <img class="product-img-2" src="#{product_image}" /> </a>
                        </div>
                        <div class="inline valign-b font-13 font-w-600 from-supply">
                            <div>#{product_name}</div>
                            <div>#{currency} #{min_price}-#{max_price}</div>
                            <div>#{ratings}(#{feedback_total_valid_num})</div>
                        </div>
                        <div style="width:147px;">#{tags_name}</div>
                    </div>
                </li>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),s))}}r="store"==e?bg_i18n("lang_the_tag_have_not_store"):bg_i18n("lang_the_tag_have_not_goods"),t&&(_=_+`<div style="text-align: center;margin-top: 90px;"> <svg style="width:35%" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 256 256" enable-background="new 0 0 256 256" xml:space="preserve">
<metadata> Svg Vector Icons : http://www.onlinewebfonts.com/icon </metadata>
<g> <path fill="#cdcdcd" d="M12.3,117.3l25.5,12.8l0.1,53c0,1.6,0.9,3.2,2.5,3.8l86,36.9h0.1l0.6,0.2h0.2c0.2,0,0.5,0.1,0.7,0.1 c0.2,0,0.5,0,0.7-0.1h0.2c0.2,0,0.3-0.1,0.6-0.2h0.1l86-36.9c1.5-0.7,2.5-2.1,2.5-3.8l0.1-53l25.5-12.8c1.1-0.5,1.8-1.4,2.1-2.6 c0.5-1.2,0.4-2.5-0.3-3.5l-28.2-46c0-0.1-0.1-0.1-0.1-0.1c-0.1-0.1-0.2-0.3-0.3-0.5l-0.6-0.6H216c-0.1-0.1-0.3-0.2-0.5-0.3 c-0.1-0.1-0.1-0.1-0.2-0.1s-0.1-0.1-0.2-0.1L129.4,32c-0.1,0-0.2-0.1-0.5-0.1c-0.2,0-0.3-0.1-0.5-0.1h-0.9c-0.2,0-0.3,0.1-0.6,0.1 c-0.1,0-0.3,0-0.3,0.1L41,63.6c-0.1,0-0.1,0.1-0.2,0.1s-0.1,0.1-0.2,0.1s-0.3,0.2-0.5,0.3c-0.1,0-0.1,0.1-0.1,0.1l0,0 c-0.2,0.1-0.4,0.3-0.5,0.5l-0.1,0.1c-0.1,0.1-0.3,0.3-0.3,0.5c0,0.1-0.1,0.1-0.1,0.1l0,0l-28.3,46c-0.3,0.5-0.5,1.1-0.6,1.7v1 c0,0.2,0.1,0.4,0.1,0.6C10.5,115.9,11.2,116.8,12.3,117.3z M236.1,111.7l-23.8,12l-57.1,28.6L133.7,115l78.5-42L236.1,111.7z  M132.2,41.9l71.4,26.3l-0.5,0.2l-70.9,38.1L132.2,41.9z M132.2,128.9l17.8,31c0.1,0.2,0.3,0.4,0.5,0.6l0.1,0.1 c0.1,0.1,0.3,0.3,0.5,0.3c0.1,0,0.1,0.1,0.1,0.1l0.6,0.3c0.1,0,0.1,0.1,0.1,0.1c0.1,0.1,0.3,0.1,0.5,0.2h0.1 c0.2,0.1,0.5,0.1,0.7,0.1h0.6c0.2,0,0.4,0,0.6-0.1h0.2c0.2-0.1,0.6-0.1,0.8-0.3l54.7-27.5l-0.1,46.3l-77.7,33.4V128.9z  M124.2,213.6h-0.2l-77.7-33.4l-0.1-46.2l54.7,27.5l0.8,0.3h0.3c0.2,0,0.4,0,0.6,0.1h0.6c0.2,0,0.5-0.1,0.7-0.1h0.1l0.6-0.2 c0.1,0,0.1-0.1,0.1-0.1l0.6-0.3l0.1-0.1c0.1-0.1,0.3-0.2,0.5-0.5l0.1-0.1c0.1-0.2,0.3-0.3,0.5-0.6l17.8-31L124.2,213.6z  M123.9,41.9v64.6L60,72.1l-7.4-4L123.9,41.9z M44,73l78.4,41.9L101,152.3l-57.1-28.6L20,111.8L44,73z"/></g>
</svg ></div>`+"<li style='color:#a2a2a2;width: 100%;text-align: center;margin-left: 0px !important;'>"+r+"</li>"),uniteInsertMenuContentDom(_+="</ul></div>","myfavted",n)}}
//展示监控的数据
function showMonitorData(e){if($(".smt-tj-loading").remove(),"need_login"==e)loginKuang();else if(0==e.length)alertInfo(bg_i18n("lang_no_data"));else{var t,n="";for(t in e){var a=e[t];a.url1=chrome.runtime.getURL("img/monitor_time.png"),a.monitor_time_title=bg_i18n("lang_monitor_time"),n+=pub_format(String(function(){
/*!
            <div class='inline mg-left-40 same-products'>
                <div><img title="#{monitor_time_title}" src="#{url1}" style="width:20px;vertical-align: text-bottom;">#{monitor_date}</div>
                <div style="text-align:center">
                    <a href="#{product_url}" target="_blank"> <img class='product-img-2' src="#{product_image}" /> </a>
                </div>
                <div class="inline valign-b font-13 font-w-600 from-supply mg-left-5">
                    <div title="#{product_name}">#{product_name}</div>
                    <div>$#{min_price}-$#{max_price}</div>
                    
                    <div>#{ratings}<span style='margin-left:3px' class='glyphicon glyphicon-star'></span> | #{reviews_count}</div>
                </div>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),a)}var o={str:n,lang_monitor_res:bg_i18n("lang_monitor_res"),lang_more_monitor_res:bg_i18n("lang_more_monitor_res"),monitor_id:e[0].monitor_id},o=pub_format(String(function(){
/*!
        <div class="dialog-product-show-monitor dialog-show">
            <div>
                <span onclick='closeSameProduct()' class='glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>
            </div>
            <div><h3 style="text-align:center;margin-top:0px;">#{lang_monitor_res}</h3></div>
            #{str}
            <hr style="margin-top:10px;margin-bottom:10px" />
            <div style="color:black;text-align:right;text-decoration: underline;cursor: pointer;">
                <a href="https://ixspy.com/data#/mycenter/myfavted?type=aliexpress_store&monitor_id=#{monitor_id}" target="_blank"> #{lang_more_monitor_res} </a>
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),o);$("body").append(o)}}
//展示我的个人中心
function showMyPersion(e){var t;$(".smt-loading").remove(),"need_login"==e?loginKuang():(localStorage.getItem("version"),uniteInsertMenuContentDom(null==e.user_id?(t={url:window.location.href,lang_wechat:bg_i18n("lang_wechat"),version:e.version,lang_cow:bg_i18n("lang_cow"),lang_login_more:bg_i18n("lang_login_more"),login_url:login_url},pub_format(String(function(){
/*!
            <div class="product-part-1">
   
                <div class="user-pic-div">
                        <a style="color:white;line-height:2" target="_blank" href="https://user.ixspy.com/userLogin?site=7&redirect_url=#{login_url}&to_redirect_url=#{url}&login_type=">
                        <span class="lang_login_more" onclick="loginTip()">
                            #{lang_login_more}
                        </span>
                    </a>
                </div>
    
                <div style="width: 80%;margin: 0 auto;">
                    <div class="inline site two-button"> <a style="color:white" href="https://ixspy.com" target="_blank"> IXSPY.COM </a></div>
                    <div class="inline group two-button"> <a style="color:white" href="https://www.facebook.com/groups/winning.products" target="_blank"> <span class="lang_join_group">Join Facebook Group</span>  </a> </div>
                </div>
                <div style="width: 80%;margin: 0 auto;">
                    <div class="inline site two-button"> <a style="color:white" href="https://ixspy.com" target="_blank"> IXSPY.COM </a></div>
                    <div class="inline group two-button"> <a style="color:white" href="https://www.facebook.com/groups/winning.products" target="_blank"> <span class="lang_join_group">Join Facebook Group</span>  </a> </div>
                </div>
                <div class="maskwechat">
                 <img style="width:30px;height:30px;position:absolute;right:0;top:0px;cursor:pointer;" onclick="cancelMask()" src="#{imgClose}">
                <img style="width:300px;height:300px;position:absolute;left:140px;right:0;top:70px;" src="#{wechatpng}" />
            </div>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),t)):(e.wechatpng=chrome.runtime.getURL("img/wechat.png"),e.imgClose=chrome.runtime.getURL("img/closewechat.png"),e.lang_wechat=bg_i18n("lang_wechat"),e.lang_cow=bg_i18n("lang_cow"),e.my_persion_center=bg_i18n("my_persion_center"),e.my_task_center=bg_i18n("my_task_center"),e.area_pricing=bg_i18n("area_pricing"),e.area_pricing=bg_i18n("area_pricing"),e.traffic_reverse_check=bg_i18n("traffic_reverse_check"),e.keywords_Monitoring=bg_i18n("keywords_Monitoring"),pub_format(String(function(){
/*!
            <div class="product-part-1" style="margin-top:10px;text-align: center">
                <h4>#{my_task_center}</h4>
                 <div style="width: 80%;margin: 0 auto;">
                    <a style="color:white" href="https://ixspy.com/data#/mycenter/task?tab=product_carrier_fee" target="_blank"><div class="inline site two-button"> #{area_pricing}</div></a>
                   <a style="color:white" href="https://ixspy.com/data#/mycenter/task?tab=product_kwd_search" target="_blank"> <div class="inline group two-button">  <span class="lang_join_group">#{traffic_reverse_check}</span></div>  </a>
                </div>
                <hr>
                <h4>#{my_persion_center}</h4>
                <div style="width: 80%;margin: 0 auto;">
                    <div class="inline site two-button">IXSPY: #{user_id}</div>
                    <div class="inline group two-button"> <span class="glyphicon glyphicon-user" aria-hidden="true">#{email} </span>  </div>
                </div>
    
                <div style="width: 80%;margin: 0 auto;">
                    <div class="inline site two-button"> <a style="color:white" href="https://ixspy.com" target="_blank"> IXSPY.COM </a></div>
                    <div class="inline group two-button"> <a style="color:white" href="https://www.facebook.com/groups/winning.products" target="_blank"> <span class="lang_join_group">Join Facebook Group</span>  </a> </div>
                </div>
                <div style="width: 80%;margin: 0 auto;">
                    <div class="inline group two-button" onclick="toWeChatGroup()" style="width:100%;cursor:pointer;"> <span class="lang_join_group">#{lang_wechat}</span>  </div>
                </div>
                <div class="maskwechat">
                 <img style="width:30px;height:30px;position:absolute;right:0;top:0px;cursor:pointer;" onclick="cancelMask()" src="#{imgClose}">
                <img style="width:300px;height:400px;position:absolute;left:140px;right:0;top:10px;" src="#{wechatpng}" />
            </div>
            </div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e)),type="my_persion",e))}
//检测用户是否安装了多个相同的插件(本地加市场)
function checkIsManyExtention(){setTimeout(()=>{1<$(".chrome_source_id").length&&alertInfo(bg_i18n("lang_tip_many_extention"),1e4)},3e3)}setInterval(()=>{var e;$(window).height()<790&&499<$(window).height()?(e=.72*$(window).height(),$(".product-part-1").css("max-height",e+"px")):$(window).height()<500&&(e=.6*$(window).height(),$(".product-part-1").css("max-height",e+"px"))},1500),checkIsManyExtention();var wordsTimer="";
//将长尾词信息存入到localstorage
function longWordsSet(e){var t=[],n=!1,a=e.res,e=e.config_class.longWordsSet;let o=-1!==location.href.indexOf("aliexpress.ru/");if(-1!==a[0].indexOf("mtopjsonp")&&-1!==a[0].indexOf("mtopjsonp")?t=longWordsMerage(a):(t=a,n=1),0==t)$(".input-words-loading").remove(),$(".smt-tj-loading").remove();else{if(o){var i=$("#searchInput").val();if(void 0===(i=void 0===i?$("#SearchText").val():i)&&e)for(item of e.ru.search_input)if(0<$(item).length){i=$(item).val();break}}else{i=$(".search-key").val();if(void 0===i&&e)for(item of e.normal.search_input)if(0<$(item).length){i=$(item).val();break}}if(null!=i&&""!=i||(i=$('[name="SearchText"]').attr("value"),i=$('[name="SearchText"]').val()),null!=(i=null!=i&&""!=i?i:$("#search-words").val())){i=i.replaceAll(" ","-");var r,l={},_=(n?l=t[0]:l[i]=t,localStorage.setItem("long_words_like",JSON.stringify(l)),"");for(r in l){t=l[r];if(1==n){var s={display_keyword:t,index:r};_+=pub_format(String(function(){
/*!
                <li onclick="clickSearchWords('#{display_keyword}')" class="self-li search-bar_SearchBar__suggestion__11586a"  id="downshift-0-item-#{index}"  role="option" aria-selected="false">
                    <span class="ali-kit_Base__base__104pa1 ali-kit_Base__default__104pa1 ali-kit_Base__strong__104pa1 ali-kit_Label__label__1n9sab ali-kit_Label__size-s__1n9sab">
                        #{display_keyword}
                    </span>
                
                </li>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),s)}else for(var d in t)isNaN(d)||null!=(s=t[d].trace.utLogMap).display_keyword&&(s.rank_score=Number(s.rank_score).toFixed(2),"10000.00"!=s.rank_score)&&(
// objV.lang_rank_score = bg_i18n('lang_rank_score')
// <span style="float:right">#{lang_rank_score}:#{rank_score}</span>
// key_word_arr.push(objV.display_keyword)
_+=pub_format(String(function(){
/*!
                    <li onclick="clickSearchWords('#{display_keyword}')" data-role="item" class="self-li ui-autocomplete-item clickwords" data-value="#{display_keyword}">
                        <span style="width:100%"   class="suggest_key">
                            <span class="ui-autocomplete-item-hl">#{display_keyword}</span><br>
                        </span>
                    </li>
                */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),s))}if(""==_)$(".input-words-loading").remove(),$(".smt-tj-loading").remove();else{
// if (localStorage.getItem('site_country')){
_+="<li class='download-txt'><span lass='self-li more_search_long_words' style='margin-right:15px;'  onclick='downloadWords()'>"+bg_i18n("lang_download_words")+"</span><span onclick='getKeywordsInfo()'>"+bg_i18n("analysis_lang_words")+"</span></li>",
// }else{
//     tempHtml += "<li class='download-txt'>" +
//         // "<span class='self-li more_search_long_words' style='margin-right:15px;' onclick='lookMoreWords(event)'>"+bg_i18n("lang_more")+"</span>" +
//         "<span lass='self-li more_search_long_words' style='margin-right:15px;'  onclick='downloadWords()'>"+bg_i18n('lang_download_words')+"</span></li>"
// }
localStorage.setItem("text-2",bg_i18n("lang_rank_score")),localStorage.setItem("text-1",bg_i18n("lang_long_words"));var c=".ui-autocomplete-ctn";if(0<$(".ui-autocomplete-ctn").length)c=".ui-autocomplete-ctn";else if(0<$(".SnowCommonHeader_Dropdown__dropdown__iblav ul").length)c=".SnowCommonHeader_Dropdown__dropdown__iblav";else if(0<$('[aria-autocomplete="list"]').length)c='[aria-autocomplete="list"]';else if(0<$(".src--auto--2EvPUd1").length)c=".src--auto--2EvPUd1";else if(0<$(".RedSearchBar_ItemList__container__xv7zv").length)c=".RedSearchBar_ItemList__container__xv7zv";else if(0<$(".RedSearchBar_ItemList__container__rhlyf").length)c=".RedSearchBar_ItemList__container__rhlyf";else if(o){if(e)for(item of e.ru.search_body)if(0<$(item).length){c=$(item).val();break}}else if(e)for(item of e.normal.search_body)if(0<$(item).length){c=$(item).val();break}$(".self-li").remove(),$(".download-txt").attr("timerv",""),setTimeout(()=>{var e;
// $("body").click()
$(".input-words-loading").remove(),$(".smt-tj-loading").remove(),(".ui-autocomplete-ctn"==c||".src--auto--2EvPUd1"==c?$(c+" li"):$(".download-txt")).remove(),o?(e="<li class='download-txt snow-ali-kit_Typography__base__1shggo snow-ali-kit_Typography__base__1shggo snow-ali-kit_Typography__strong__1shggo snow-ali-kit_Typography__sizeParagraphM__1shggo'><span lass='self-li more_search_long_words'  style='margin-right:15px;'  onclick='downloadWords()'>"+bg_i18n("lang_download_words")+"</span><span onclick='getKeywordsInfo()'>"+bg_i18n("analysis_lang_words")+"</span></li>",(0<$(c+" ul").length?$(c+" ul"):$(c)).append(e)):$(c).append(_),$(c).css("display","block"),$(".ui-autocomplete").css("display","block")},1e3)}}}}
//多个长尾词的返回结果处理
function longWordsMerage(e){if(pub_isEmpty(e))return!1;var t,n=[];for(t in e){var a=e[t];if(-1!==a.indexOf("mtopjsonp"))try{var o,a=getParenthesesStr(a);""!=e&&(o=JSON.parse(a),n.push(o.data.mods.asQueryList.content))}catch(e){continue}}return pub_arrayMerage(n)}function getParenthesesStr(e){let t="";return t=pub_isEmpty(e)||(e=e.match(/\((.+?)\)/g),pub_isEmpty(e))||(e=e[0],pub_isEmpty(e))?t:e.substring(1,e.length-1)}
//提交长尾词到后台的提交
// function longWordsAddback(res){
//     localStorage.setItem('long_words_like','')
// }
//切换到速卖通站点
function changeAliexpressCom(){$(".tip_price_collect").remove();var e={lang_tip_no_ru:bg_i18n("lang_tip_no_ru"),lang_tip_no:bg_i18n("lang_tip_no"),lang_go_no_aliexpress:bg_i18n("lang_go_no_aliexpress")},e=pub_format(String(function(){
/*!
        <div class="tip_price_collect">
            <div>
                #{lang_tip_no_ru}
            </div>
            <div style="text-align:center;margin-top:20px">
                <span class="tip_price_collect_button" onclick="goToAliexpress('no')">#{lang_tip_no}</span>
                <span class="tip_price_collect_button" onclick="goToAliexpress('yes')">#{lang_go_no_aliexpress}</span>
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);$("body").append(e)}
//更多的搜索词展示
function moreLongWordsShow(e){if("need_login"==e)$(".input-words-loading").remove(),$(".smt-tj-loading").remove(),loginKuang();else if("need_permissions"==e)$(".input-words-loading").remove(),$(".smt-tj-loading").remove(),alertInfo(bg_i18n("lang_no_permissions"),5e3);else{var t=!1,n=[];for(d in e){var a=e[d],o=a;if(-1===o.indexOf("mtopjsonp")&&"object"==typeof a){var
//合并之前的搜索词
n=n.concat(a),i=localStorage.getItem("long_words_like"),r=JSON.parse(i);n=n.concat(r),n=Array.from(new Set(n)),localStorage.setItem("long_words_like",JSON.stringify(n)),t=!0}else{try{if(o=getParenthesesStr(o),""==a)continue;var l=(l=JSON.parse(o)).data.mods.asQueryList.content}catch(e){console.log(e);continue}
//合并之前的搜索词
var i=localStorage.getItem("long_words_like"),r=JSON.parse(i),_=$("#search-key").val();for(d in r=r[nameNew=_.replaceAll(" ","-")],l)r.push(l[d]);
//合并数组
var s=[];for(d in r)s.push(r[d]);var d,c={},g=[];
//去重
for(d in s)null==c[_=(_=s[d].keywords).replaceAll(" ","-")]&&(c[_]=1,g.push(s[d]));
//保存最新数据
a={};a[nameNew]=g,localStorage.setItem("long_words_like",JSON.stringify(a)),g.length<=$(".ui-autocomplete-ctn li").length||(t=!0,appendWordsTipPanel(g))}}0<n.length&&appendWordsTipPanel(n),$(".smt-tj-loading").remove(),$(".input-words-loading").remove(),0==t&&alertInfo(bg_i18n("lang_no_data"))}}
//重写提示信息
function appendWordsTipPanel(e){
//重写提示框信息
var t,n,a,o="";for(t in e){var i,r=e[t];"string"==typeof r?(i={display_keyword:r,index:t},o+=pub_format(String(function(){
/*!
                <li onclick="clickSearchWords('#{display_keyword}')" class="search-bar_SearchBar__suggestion__11586a"  id="downshift-0-item-#{index}"  role="option" aria-selected="false">
                    <span class="ali-kit_Base__base__104pa1 ali-kit_Base__default__104pa1 ali-kit_Base__strong__104pa1 ali-kit_Label__label__1n9sab ali-kit_Label__size-s__1n9sab">
                        #{display_keyword}
                    </span>
                
                </li>
            */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),i)):isNaN(t)||null!=(i=r.trace.utLogMap).display_keyword&&(i.rank_score=i.rank_score.toFixed(2),"10000.00"!=i.rank_score)&&(i.lang_rank_score=bg_i18n("lang_rank_score"),
//<span style="float:right">#{lang_rank_score}:#{rank_score}</span>
o+=pub_format(String(function(){
/*!
            <li onclick="clickSearchWords('#{display_keyword}')" data-role="item" class="ui-autocomplete-item clickwords" data-value="#{display_keyword}">
                <span style="width:100%"   class="suggest_key">
                    <span class="ui-autocomplete-item-hl">#{display_keyword}</span>
                </span>
            </li>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),i))}""==o?$(".smt-tj-loading").remove():(
//<span class='more_search_long_words' style='margin-right:15px;' onclick='lookMoreWords(event)'>"+bg_i18n("lang_more")+"</span>
o+="<li class='download-txt'><span onclick='downloadWords()'>"+bg_i18n("lang_download_words")+"</span></li>",localStorage.setItem("text-2",bg_i18n("lang_rank_score")),localStorage.setItem("text-1",bg_i18n("lang_long_words")),clearInterval(wordsTimer),n=".ui-autocomplete",a=".ui-autocomplete-ctn",0==$(".ui-autocomplete").length&&(a=n='[role="listbox"]'),$(n).attr("timerv",""),wordsTimer=setInterval(()=>{$(".input-words-loading").remove(),"words"!=$(n).attr("timerv")&&($(".smt-tj-loading").remove(),$(n+" li").css("display","none"),$(a).append(o),$("body").click(),$(n).attr("timerv","words"),$(n).css("display","block"))},1e3))}
//在输入框插入loading 
function createInputLoadingDom(){var e='<img class="input-words-loading" style="width:50px;margin-top: 50px; position:absolute;top:25px;left:40%" src="'+chrome.runtime.getURL("img/loading.png")+'">';setTimeout(()=>{-1!==location.href.indexOf("aliexpress.ru/")?0<$("[name='SearchText']").parent().children("div").length&&$("[name='SearchText']").parent().children("div").append(e):(0<$(".ui-autocomplete").length?$(".ui-autocomplete"):0<$("[name='SearchText']").next().length?$("[name='SearchText']").next():$(".search--active--20ny0_q")).append(e)},1e3)}
//loading 产品运费加入采集队列
function createCarrierFeeLoadingDom(){var e='<img class="laoding-carrier" style="width:25px;" src="'+chrome.runtime.getURL("img/loading_gif.gif")+'">';$(".check-country-price").append(e)}
//展开产品相关词流量步骤图
function showKeywordsPic(){var e="",t="",t="zh"==bg_i18n("lang_a")?(e=chrome.runtime.getURL("img/step_1.png"),chrome.runtime.getURL("img/step_2.png")):(e=chrome.runtime.getURL("img/step_1_en.png"),chrome.runtime.getURL("img/step_2_en.png")),e={img1:e,img2:t},t=pub_format(String(function(){
/*!
    <div class="show-keywords-pic">
            <span style="font-size:25px" onclick='closeShowWordsLinPic()' class='notify-close glyphicon glyphicon-remove float-right cursor chahca' aria-hidden='true'></span>
            <div style="text-align:center">
               <img style="display:block" src="#{img1}">
               <img src="#{img2}" />
            </div>
        </div>
    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);$("body").append(t)}
//采集产品价格运费的加入队列的回调
function callProductCarrier(e){var t;$(".laoding-carrier").remove(),null!=e.error&&0==e.error.code?(alertInfo(t=bg_i18n("lang_task_success"),6e3),t=bg_i18n("lang_already_task"),$(".check-product-carrier-msg").html(t)):"5018"==e.error.code?loginKuang():"2001"==e.error.code?alertInfo(t=bg_i18n("lang_carrier_permissions"),4e3):alertInfo(t=(t=bg_i18n("lang_"+e.error.code))&&""!=t?t:bg_i18n("lang_task_fail"),4e3)}
//采集关键词分析
async function callKeywordInfo(e){
// console.log(response);
if("no_login"==e.data)loginKuang();else if("no_permissions"==e.data)alertInfo(bg_i18n("lang_no_permissions"),5e3);else if(0==e.error.code){info=(info=JSON.stringify(e.data.keywords)).replace(/'/g,"aaaaaabbcccccc"),title={keywords:bg_i18n("keywords"),lang_category:bg_i18n("lang_category"),search_popularity:bg_i18n("search_popularity"),search_index:bg_i18n("search_index"),click_rate:bg_i18n("click_rate"),order_conversion_rate:bg_i18n("order_conversion_rate"),competition_index:bg_i18n("competition_index"),supply_index:bg_i18n("supply_index"),analysis_lang_words:bg_i18n("analysis_lang_words")},title=JSON.stringify(title);var t,n='<div id="ixspy-keyword-table" style="display: block;"><div style="position: fixed; inset: 0px; display: flex; flex-direction: column; align-items: center; justify-content: center;direction: ltr;z-index: 9000011;"><div style="display: flex; border: 2px solid;flex-direction: column; width: 880px; min-height: 300px; position: relative; border-radius: 20px;background-color: #fdfdfd"><img onclick="keyword_table_colse()" src="https://ae01.alicdn.com/kf/H5545b1dae97243fba06c33bd33104b84G.png" style="width: 32px; height: 32px; position: absolute; top: -16px; right: -14px;"><div style="width: 100%;"><span style="margin-top:15px;margin-left: 15px;float: left;font-size:18px;font-weight: bold">'+bg_i18n("lang_country")+":"+e.data.country+'</span><button style="margin-top:10px;margin-right: 15px;float: right" type="button" class="w-button btn btn-default" onclick=\'todoDownKeywords('+info+","+title+')\'>下载</button></div><table style="margin: 10px;line-height: 2"><tr style="line-height: 2;"><th style="max-width: 150px">'+bg_i18n("keywords")+"/"+bg_i18n("lang_category")+
// '<th>'+bg_i18n('lang_category')+'</th>' +
"</th><th>"+bg_i18n("search_popularity")+"</th><th>"+bg_i18n("search_index")+"</th><th>"+bg_i18n("click_rate")+"</th><th>"+bg_i18n("order_conversion_rate")+"</th><th>"+bg_i18n("competition_index")+"</th><th>"+bg_i18n("supply_index")+"</th></tr>",a="";for(t in e.data.keywords)e.data.keywords[t].category_id=await productCategoryPath(e.data.keywords[t].category_id),e.data.keywords[t]?n=(
//如果有数据
n=n+'<tr><td style="font-weight:bolder;border-bottom: 0px !important;">'+t+
// '<td>'+response.data.keywords[i]['category_id']+'</td>' +
'</td><td  rowspan="2">'+e.data.keywords[t].search_cookie_cnt+'</td><td  rowspan="2">'+e.data.keywords[t].search_cnt+'</td><td rowspan="2">'+(100*e.data.keywords[t].click_rate).toFixed(2)+'%</td><td rowspan="2">'+(100*e.data.keywords[t].trans_rate).toFixed(2)+'%</td><td rowspan="2">'+e.data.keywords[t].sply_dem_rate+'</td><td rowspan="2">'+e.data.keywords[t].supply_rate+"</td></tr>")+'<tr><td style="max-width:230px;color:#7c7a7acc;border-top: 0px !important">'+e.data.keywords[t].category_id+"</td></tr>":
//如果没有数据
a=a+"<tr><td>"+t+"</td><td>---</td><td>---</td><td>---</td><td>---</td><td>---</td><td>---</td></tr>";n=n+a+"</table></div></div></div>",$("body").append(n)}}
//搜图的回调，在没有权限的时候提示
function callSearchByImg(e){"no_permission"==e&&alertInfo(bg_i18n("lang_is_vip"),4e3)}
//以图搜图的  在.com 域名下
function createSearchImgButtonDom(){
//判断是否为俄罗斯
var e;-1!==location.href.indexOf("aliexpress.ru/")?0<$(".SnowProductGallery_SnowProductGallery__container__jy810").length?($(".SnowSku_SkuPropertyItem__option__15vs2.SnowSku_SkuPropertyItem__imageWrap__15vs2").click(function(){$(".ixspy_search_by_image").remove(),setTimeout(function(){var e;0==$(".ixspy_search_by_image").length&&(e='<div class="ixspy_search_by_image"><img onclick="toSearchByImgInDetailRU(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:30px;right: 10px;width: 35px;z-index: 100;cursor:pointer"></div>',$(".SnowProductGallery_SnowProductGallery__container__jy810").after(e))},1e3)}),0==$(".ixspy_search_by_image").length&&(e='<div class="ixspy_search_by_image"><img onclick="toSearchByImgInDetailRU(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:30px;right: 10px;width: 35px;z-index: 100;cursor:pointer"></div>',$(".SnowProductGallery_SnowProductGallery__container__jy810").after(e))):0<$(".SnowProductGallery_SnowProductGallery__container__v9f35").length&&($(".picture_Picture__container__uabr65.SnowSku_SkuPropertyItem__image__9quwp").click(function(){$(".ixspy_search_by_image").remove(),setTimeout(function(){var e;0==$(".ixspy_search_by_image").length&&(e='<div class="ixspy_search_by_image"><img onclick="toSearchByImgInDetailRU(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:30px;right: 10px;width: 35px;z-index: 100;cursor:pointer"></div>',$(".SnowProductGallery_SnowProductGallery__container__v9f35").after(e))},1e3)}),0==$(".ixspy_search_by_image").length)&&(e='<div class="ixspy_search_by_image"><img onclick="toSearchByImgInDetailRU(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:30px;right: 10px;width: 35px;z-index: 100;cursor:pointer"></div>',$(".SnowProductGallery_SnowProductGallery__container__v9f35").after(e)):0==$(".image-view-magnifier-wrap").length?0==$(".mods--mainImage--vurNZwA").length?
//页面初始化时增加以图搜图按钮
0<$(".magnifier--wrap--hQvf3up").length&&(e='<img onclick="toSearchByImgInDetail(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;">',$(".magnifier--wrap--hQvf3up").append(e)):(e='<img onclick="toSearchByImgInDetail(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;top:40px;right: 20px;width: 50px;z-index: 100;">',$(".mods--mainImage--vurNZwA").append(e)):(e='<img onclick="toSearchByImgInDetail(this,1)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100;">',$(".image-view-magnifier-wrap").append(e))}
//以图搜图 在.ru 域名下
function createSearchImgButtonDomRu(){var e;0<$("figure").length?(e='<img onclick="toSearchByImgInDetail(this,0)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:150px;right: 20px;width: 50px;z-index: 100000;">',$("figure").after(e)):-1!==location.href.indexOf("aliexpress.ru/")&&(e='<img onclick="toSearchByImgInDetail(this,0)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100000;cursor: pointer;">',
// $(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn .gallery_Gallery__picture__crhgwn picture").prepend(temp)
// $("div[class='gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn']").find('picture').prepend(temp)
0<$("div[class='gallery_Gallery__picListWrapper__15bdcj SnowProductGallery_SnowProductGallery__galleryWrapper__xeihu']").length?$("div[class='gallery_Gallery__picListWrapper__15bdcj SnowProductGallery_SnowProductGallery__galleryWrapper__xeihu']").find("picture").prepend(e):0<$("div[class='gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn']").length?$("div[class='gallery_Gallery__gallery__crhgwn gallery_Gallery__aspectFill__crhgwn']").find("picture").prepend(e):0<$(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn .gallery_Gallery__picture__crhgwn picture").length&&$(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn .gallery_Gallery__picture__crhgwn picture").prepend(e))}function createSearchImgButtonDomRuSingle(){if(-1!==location.href.indexOf("aliexpress.ru/")){var n='<img onclick="toSearchByImgInDetail(this,0)"  title="'+tipImg+' By IXSPY" class="detail_page_search_by_img" src="'+similarPic+'" style="position: absolute;bottom:50px;right: 20px;width: 50px;z-index: 100000;cursor: pointer;">';let e=[];0<$(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn").length?e=$(".gallery_Gallery__gallery__crhgwn.gallery_Gallery__aspectFill__crhgwn").find("picture"):0<$(".gallery_Gallery__picListWrapper__15bdcj.SnowProductGallery_SnowProductGallery__galleryWrapper__z6imb").length&&(e=$(".gallery_Gallery__picListWrapper__15bdcj.SnowProductGallery_SnowProductGallery__galleryWrapper__z6imb").find("picture")),0<e.length&&e.each((e,t)=>{0==$(t).find(".detail_page_search_by_img").length&&$(t).prepend(n)})}}
//收藏按钮的显示控制
function collectedShopState(e){$(".smt-tj-loading").remove(),0<$(".smt_favted_goods_box").length&&$(".smt_favted_goods_box").remove(),"ok"!==e&&"already"!==e||(alertInfo(bg_i18n("lang_already_favted"),2e3),0<$(".collect-shop-collection-class").length&&($(".collect-shop-collection-class").css("display","none"),$(".remove-shop-collection-class").css("display","inline-block")))}function removeShopState(e){"ok"===e&&(alertInfo(bg_i18n("lang_cancel_shop_already"),2e3),0<$(".remove-shop-collection-class").length)&&($(".remove-shop-collection-class").css("display","none"),$(".collect-shop-collection-class").css("display","inline-block"))}
// 开始下载关键词数据到EXCEL
function exportProductKeyword(e){var t,n,a;if(0===e.code)return t=e.data,n=e.exportFileName,a=e.searchType,e=e.title,!1!==(a=dealExportProductKeywordData(a,t.data))&&void tableToExcel(a.jsonData,e,n)}
//选词专家关键词导出
function toExportExpertWordExcel(e){0!=e.data.length&&tableToExcel(JSON.parse(e.data),e.title,e.name)}
//根据类型处理数据,并返回排序,表头数据
function dealExportProductKeywordData(e,t){let a=[];if("hot-words"===e){const o=sycmCountryCodeMap();t.forEach(e=>{var t=e.top3Country.value.split(",");let n="";t.forEach(e=>{void 0!==o[e]&&(n+=o[e]+";")}),pushData={keyword:e.keyword.value,//搜索词
isBrandWord:e.isBrandWord.value,//是否是品牌原词
searchCookieCnt:e.searchCookieCnt.value,//搜索人气
searchCnt:e.searchCnt.value,//搜索指数
clickRate:(100*e.clickRate.value).toFixed(2)+"%",//点击率
transRate:(100*e.transRate.value).toFixed(2)+"%",//支付转化率
splyDemRate:e.splyDemRate.value.toFixed(2),//竞争指数
top3Country:n},a.push(pushData)});
// title = '搜索词,是否是品牌原词,搜索人气,搜索指数,点击率,支付转化率,竞争指数,Top3热搜国家\r\n'
}else if("soar-words"===e)t.forEach(e=>{pushData={keyword:e.keyword.value,//搜索词
isBrandWord:e.isBrandWord.value,//是否是品牌原词
searchCnt:e.searchCnt.value,//搜索指数
searchCntRiseIndex:(100*e.searchCntRiseIndex.value).toFixed(2)+"%",//搜索指数飙升度
exposeProdRate:(100*e.exposeProdRate.value).toFixed(2)+"%",//曝光商品数增长幅度
exposeSellerRate:(100*e.exposeSellerRate.value).toFixed(2)+"%"},a.push(pushData)});
// title = '搜索词,是否是品牌原词,搜索指数,搜索指数飙升度,曝光商品数增长幅度,曝光商家数增长幅度\r\n'
else{if("less-words"!==e)return!1;t.forEach(e=>{pushData={keyword:e.keyword.value,//搜索词
isBrandWord:e.isBrandWord.value,//是否是品牌原词
exposeProdRate:(100*e.exposeProdRate.value).toFixed(2)+"%",//曝光商品数增长幅度
searchCnt:e.searchCnt.value,//搜索指数
searchCookieCnt:e.searchCookieCnt.value},a.push(pushData)});
// title = '搜索词,是否是品牌原词,曝光商品数增长幅度,搜索指数,搜索人气\r\n'
}return{jsonData:a,title:""}}
//sycm速卖通后台 编码-国家
function sycmCountryCodeMap(){return{ALL:"全部国家",RU:"俄罗斯",US:"美国",ES:"西班牙",FR:"法国",BR:"巴西",GB:"英国",IT:"意大利",IL:"以色列",CA:"加拿大",UA:"乌克兰",NL:"荷兰",PL:"波兰",CL:"智利",DE:"德国",AU:"澳大利亚",BY:"白俄罗斯",TR:"土耳其",MX:"墨西哥",BE:"比利时",CH:"瑞士",KR:"大韩民国",CZ:"捷克共和国",SE:"瑞典",JP:"日本",SA:"沙特阿拉伯",NZ:"新西兰",HU:"匈牙利",SK:"斯洛伐克共和国",TH:"泰国",IE:"爱尔兰"}}
// 导出excel
function tableToExcel(t,n,e){
// 增加	为了不让表格显示科学计数法或者其他格式
for(let e=0;e<t.length;e++){for(const r in t[e])n+=t[e][r]+",";n+="\r\n"}var a=new Date,a=a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate()+"_"+a.getHours()+"时"+a.getMinutes()+"分",o="data:text/csv;charset=utf-8,\ufeff "+encodeURIComponent(n),i=document.createElement("a");i.href=o,
// 对下载的文件命名 显示国家,分类,日期信息
i.download=e+"_"+a+".csv",i.click()}
//页面插入导出按钮
function appendSycmSearchWordExcelButton(){if(-1!==window.location.href.indexOf("sycm.aliexpress.com/mc")||-1!==window.location.href.indexOf("csp.aliexpress.com/m_apps/csp-sycm-op/searchAnalysis")||-1!==window.location.href.indexOf("gsp.aliexpress.com/apps/sycm/keyword/analysis")){let e={export_button_value:bg_i18n("download_product_keyword")};var n=pub_format(String(function(){
/*!
            <div style="
                display: inline-block;
                margin: 0 15px;
                font-size: 20px;
                text-decoration: underline;
                cursor:pointer;
                z-index: 99999;
                position: relative;" class="smt-append-sycm-export-keyword-button" onclick="downloadProductKeyword()"><span>#{export_button_value}</span></div>
        */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e);let t=setInterval(function(){0==$('div[class="smt-append-sycm-export-keyword-button"]').length&&(-1!==window.location.href.indexOf("mc/SearchAnalysis")?0<$('div[class="sycm-title"]').length?(0==$('div[class="smt-append-sycm-export-keyword-button"]').length&&$('div[class="sycm-title"]').after(n),clearInterval(t)):0<$('h1[class="sycm-ae-page-title-text"]').length&&(0==$('div[class="smt-append-sycm-export-keyword-button"]').length&&$('h1[class="sycm-ae-page-title-text"]').after(n),clearInterval(t)):-1!==window.location.href.indexOf("keyword/analysis")&&0<$('div[class="dc-page-header-title"]').length&&(n=pub_format(String(function(){
/*!
                        <div style="
                            display: inline-block;
                            margin: 0 15px;
                            font-size: 20px;
                            text-decoration: underline;
                            cursor:pointer;
                            z-index: 99999;
                            position: relative;" class="smt-append-sycm-export-keyword-button" onclick="downloadExpertProductKeyword()"><span>#{export_button_value}</span></div>
                    */}).replace(/^[^\{]*\{\s*\/\*!?|\*\/[;|\s]*\}$/g,""),e),0==$('div[class="smt-append-sycm-export-keyword-button"]').length&&$('div[class="dc-page-header-title"]').after(n),clearInterval(t)))},1e3)}}
/**
 * 商品收藏
 * @param {*} response 
 * @returns 
 */function show_collection_box(t){$(".smt-tj-loading").remove();try{var n=bg_i18n("lang_add_favted"),a=bg_i18n("lang_tip_no"),o=bg_i18n("lang_tag"),i=bg_i18n("lang_choice");if(t)if(0==t.error.code){
//展示标签页,再用户确认后保存标签
let e='<div class="smt_favted_goods_box"><h4>'+o+"("+i+")</h4>";for(item of t.data){var r='<span class="smt_favted_goods_box_span tags-span" data-value="'+item.id+'" onclick="clickChoseTagByCollect('+item.id+',this)" class="tags-span">'+item.name+"</span>";e+=r}e+='<div class="smt_favted_goods_box_button_box"><input onclick="web_doFavtedGoodsId('+t.productId+')" class="smt_favted_goods_box_button" type="button" value="'+n+`"></input>
                <input onclick="cancelCollect()" class="smt_favted_goods_box_button" type="button" value="`+a+'"></input></div></div>',$("body").append(e)}else window.postMessage({type:"web_menu",data:{productId:t.productId},cmd:"collection_goods"},"*")}catch(e){
//直接请求收藏标签的接口
//{ type: type, data: data, cmd: cmd, chosed_item: chosed_item }
window.postMessage({type:"web_menu",data:{productId:t.productId},cmd:"collection_goods"},"*")}}
/**
 * 店铺收藏
 * @param {*} response 
 * @returns 
 */function show_collection_box_store(t){$(".smt-tj-loading").remove();try{var n=bg_i18n("lang_add_favted"),a=bg_i18n("lang_tip_no"),o=bg_i18n("lang_tag"),i=bg_i18n("lang_choice");if(t)if(0==t.error.code){
//展示标签页,再用户确认后保存标签
let e='<div class="smt_favted_goods_box"><h4>'+o+"("+i+")</h4>";for(item of t.data){var r='<span class="smt_favted_goods_box_span tags-span" data-value="'+item.id+'" onclick="clickChoseTagByCollect('+item.id+',this)" class="tags-span">'+item.name+"</span>";e+=r}e+='<div class="smt_favted_goods_box_button_box"><input onclick="collectedId('+t.productId+')" class="smt_favted_goods_box_button" type="button" value="'+n+`"></input>
                <input onclick="cancelCollect()" class="smt_favted_goods_box_button" type="button" value="`+a+'"></input></div></div>',$("body").append(e)}else window.postMessage({type:"web_menu",data:{store_id:t.productId},cmd:"collectedShop"},"*")}catch(e){
//直接请求收藏标签的接口
//{ type: type, data: data, cmd: cmd, chosed_item: chosed_item }
window.postMessage({type:"web_menu",data:{store_id:t.productId},cmd:"collectedShop"},"*")}}
/**
 * 取消收藏商品
 * @param {*} response 
 */function removeProduct(e){console.log(e);try{var t;0==e.res.error.code?(alertInfo(bg_i18n("lang_cancel_shop_already"),3e3),t=e.params.goods_id,0<$(".text-favted-product").length&&($(".text-favted-product").parent().removeClass("smt_already_collect"),$(".text-favted-product").html(bg_i18n("click_favted_product"))),console.log($(".favted_"+t).length),0<$(".favted_"+t).length&&($(".favted_"+t).removeClass("smt_already_collect"),$(".favted_"+t).attr("src",chrome.runtime.getURL("img/no_favted.png")))):alertInfo(bg_i18n("lang_collect_cancel_fail"),3e3)}catch(e){alertInfo(bg_i18n("lang_collect_cancel_fail"),3e3)}}


