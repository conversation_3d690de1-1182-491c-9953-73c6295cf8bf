/*
 Copyright (C) <PERSON> 2017
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.Popper=t()}(this,function(){"use strict";function s(e){return e&&"[object Function]"==={}.toString.call(e)}function d(e,t){return 1!==e.nodeType?[]:(e=window.getComputedStyle(e,null),t?e[t]:e)}function c(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function u(e){var t,n,o;return e&&-1===["HTML","BODY","#document"].indexOf(e.nodeName)?(t=(o=d(e)).overflow,n=o.overflowX,o=o.overflowY,/(auto|scroll)/.test(t+o+n)?e:u(c(e))):window.document.body}function p(e){var e=e&&e.offsetParent,t=e&&e.nodeName;return t&&"BODY"!==t&&"HTML"!==t?-1!==["TD","TABLE"].indexOf(e.nodeName)&&"static"===d(e,"position")?p(e):e:window.document.documentElement}function i(e){return null===e.parentNode?e:i(e.parentNode)}function h(e,t){var n,o,r;return e&&e.nodeType&&t&&t.nodeType?(o=(r=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING)?e:t,r=r?t:e,(n=document.createRange()).setStart(o,0),n.setEnd(r,0),e!==(n=n.commonAncestorContainer)&&t!==n||o.contains(r)?"BODY"===(r=(o=n).nodeName)||"HTML"!==r&&p(o.firstElementChild)!==o?p(n):n:(r=i(e)).host?h(r.host,t):h(e,i(t).host)):window.document.documentElement}function m(e,t){var t="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",n=e.nodeName;return("BODY"===n||"HTML"===n?(n=window.document.documentElement,window.document.scrollingElement||n):e)[t]}function f(e,t){var t="x"===t?"Left":"Top",n="Left"==t?"Right":"Bottom";return+e["border"+t+"Width"].split("px")[0]+ +e["border"+n+"Width"].split("px")[0]}function o(e,t,n,o){return C(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],H()?n["offset"+e]+o["margin"+("Height"===e?"Top":"Left")]+o["margin"+("Height"===e?"Bottom":"Right")]:0)}function g(){var e=window.document.body,t=window.document.documentElement,n=H()&&window.getComputedStyle(t);return{height:o("Height",e,t,n),width:o("Width",e,t,n)}}function b(e){return M({},e,{right:e.left+e.width,bottom:e.top+e.height})}function l(e){var t={};if(H())try{var t=e.getBoundingClientRect(),n=m(e,"top"),o=m(e,"left");t.top+=n,t.left+=o,t.bottom+=n,t.right+=o}catch(e){}else t=e.getBoundingClientRect();var r,n={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},o="HTML"===e.nodeName?g():{},t=o.width||e.clientWidth||n.right-n.left,o=o.height||e.clientHeight||n.bottom-n.top,t=e.offsetWidth-t,o=e.offsetHeight-o;return(t||o)&&(t-=f(r=d(e),"x"),o-=f(r,"y"),n.width-=t,n.height-=o),b(n)}function w(e,t){var n=H(),o="HTML"===t.nodeName,r=l(e),i=l(t),e=u(e),s=d(t),f=+s.borderTopWidth.split("px")[0],a=+s.borderLeftWidth.split("px")[0],i=b({top:r.top-i.top-f,left:r.left-i.left-a,width:r.width,height:r.height});return i.marginTop=0,i.marginLeft=0,!n&&o&&(r=+s.marginTop.split("px")[0],o=+s.marginLeft.split("px")[0],i.top-=f-r,i.bottom-=f-r,i.left-=a-o,i.right-=a-o,i.marginTop=r,i.marginLeft=o),i=(n?t.contains(e):t===e&&"BODY"!==e.nodeName)?function(e,t,n){var n=2<arguments.length&&void 0!==n&&n,o=m(t,"top"),t=m(t,"left"),n=n?-1:1;return e.top+=o*n,e.bottom+=o*n,e.left+=t*n,e.right+=t*n,e}(i,t):i}function v(e,t,n,o){var r,i,s,f,a,p,l={top:0,left:0},t=h(e,t);return"viewport"===o?(i=t,s=window.document.documentElement,i=w(i,s),f=C(s.clientWidth,window.innerWidth||0),a=C(s.clientHeight,window.innerHeight||0),p=m(s),s=m(s,"left"),l=b({top:p-i.top+i.marginTop,left:s-i.left+i.marginLeft,width:f,height:a})):("scrollParent"===o?"BODY"===(r=u(c(e))).nodeName&&(r=window.document.documentElement):r="window"===o?window.document.documentElement:o,p=w(r,t),"HTML"!==r.nodeName||function e(t){var n=t.nodeName;return"BODY"!==n&&"HTML"!==n&&("fixed"===d(t,"position")||e(c(t)))}(t)?l=p:(i=(s=g()).height,f=s.width,l.top+=p.top-p.marginTop,l.bottom=i+p.top,l.left+=p.left-p.marginLeft,l.right=f+p.left)),l.left+=n,l.top+=n,l.right-=n,l.bottom-=n,l}function a(e,t,n,o,r,i){var s,i=5<arguments.length&&void 0!==i?i:0;return-1===e.indexOf("auto")?e:(o=v(n,o,i,r),s={top:{width:o.width,height:t.top-o.top},right:{width:o.right-t.right,height:o.height},bottom:{width:o.width,height:o.bottom-t.bottom},left:{width:t.left-o.left,height:o.height}},(0<(r=(i=Object.keys(s).map(function(e){return M({key:e},s[e],{area:(e=s[e]).width*e.height})}).sort(function(e,t){return t.area-e.area})).filter(function(e){var t=e.width,e=e.height;return t>=n.clientWidth&&e>=n.clientHeight})).length?r:i)[0].key+((t=e.split("-")[1])?"-"+t:""))}function y(e,t,n){return w(n,h(t,n))}function O(e){var t=window.getComputedStyle(e),n=parseFloat(t.marginTop)+parseFloat(t.marginBottom),t=parseFloat(t.marginLeft)+parseFloat(t.marginRight);return{width:e.offsetWidth+t,height:e.offsetHeight+n}}function E(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function x(e,t,n){n=n.split("-")[0];var e=O(e),o={width:e.width,height:e.height},r=-1!==["right","left"].indexOf(n),i=r?"top":"left",s=r?"left":"top",f=r?"height":"width",r=r?"width":"height";return o[i]=t[i]+t[f]/2-e[f]/2,o[s]=n===s?t[s]-e[r]:t[E(s)],o}function L(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function T(e,n,t){var o,r;return(void 0===t?e:e.slice(0,(e=e,o="name",r=t,Array.prototype.findIndex?e.findIndex(function(e){return e[o]===r}):(t=L(e,function(e){return e[o]===r}),e.indexOf(t))))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var t=e.function||e.fn;e.enabled&&s(t)&&(n.offsets.popper=b(n.offsets.popper),n.offsets.reference=b(n.offsets.reference),n=t(n,e))}),n}function e(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function N(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),o=0;o<t.length-1;o++){var r=t[o],r=r?""+r+n:e;if(void 0!==window.document.body.style[r])return r}return null}function P(e,t,n,o){n.updateBound=o,window.addEventListener("resize",n.updateBound,{passive:!0});o=u(e);return function e(t,n,o,r){var i="BODY"===t.nodeName,t=i?window:t;t.addEventListener(n,o,{passive:!0}),i||e(u(t.parentNode),n,o,r),r.push(t)}(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function j(){var t;this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=(this.reference,t=this.state,window.removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function k(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function A(n,o){Object.keys(o).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&k(o[e])&&(t="px"),n.style[e]=o[e]+t})}function F(e,t,n){var o,r=L(e,function(e){return e.name===t}),e=!!r&&e.some(function(e){return e.name===n&&e.enabled&&e.order<r.order});return e||(o="`"+t+"`",console.warn("`"+n+"` modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")),e}function R(e,t){t=1<arguments.length&&void 0!==t&&t,e=S.indexOf(e),e=S.slice(e+1).concat(S.slice(0,e));return t?e.reverse():e}function U(e,f,a,t){var r=[0,0],o=-1!==["right","left"].indexOf(t),t=e.split(/(\+|\-)/).map(function(e){return e.trim()}),e=t.indexOf(L(t,function(e){return-1!==e.search(/,|\s/)})),n=(t[e]&&-1===t[e].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead."),/\s*,\s*|\s+/);return(-1===e?[t]:[t.slice(0,e).concat([t[e].split(n)[0]]),[t[e].split(n)[1]].concat(t.slice(e+1))]).map(function(e,t){var s=(1===t?!o:o)?"height":"width",n=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,n=!0,e):n?(e[e.length-1]+=t,n=!1,e):e.concat(t)},[]).map(function(e){return t=s,n=f,o=a,r=+(i=(e=e).match(/((?:\-|\+)?\d*\.?\d*)(.*)/))[1],i=i[2],r?0===i.indexOf("%")?b("%p"===i?n:o)[t]/100*r:"vh"===i||"vw"===i?("vh"===i?C(document.documentElement.clientHeight,window.innerHeight||0):C(document.documentElement.clientWidth,window.innerWidth||0))/100*r:r:e;var t,n,o,r,i})}).forEach(function(n,o){n.forEach(function(e,t){k(e)&&(r[o]+=e*("-"===n[t-1]?-1:1))})}),r}for(var Y=Math.min,B=Math.floor,C=Math.max,t=["native code","[object MutationObserverConstructor]"],I="undefined"!=typeof window,q=["Edge","Trident","Firefox"],z=0,n=0;n<q.length;n+=1)if(I&&0<=navigator.userAgent.indexOf(q[n])){z=1;break}function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var r,G,V=I&&(G=window.MutationObserver,t.some(function(e){return-1<(G||"").toString().indexOf(e)}))?function(e){var t=!1,n=0,o=document.createElement("span");return new MutationObserver(function(){e(),t=!1}).observe(o,{attributes:!0}),function(){t||(t=!0,o.setAttribute("x-index",n),++n)}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},z))}},H=function(){return r=null==r?-1!==navigator.appVersion.indexOf("MSIE 10"):r},t=function(e,t,n){return t&&Q(e.prototype,t),n&&Q(e,n),e},M=Object.assign||function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},_=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],S=_.slice(3),X="flip",J="clockwise",K="counterclockwise",t=(t(W,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=y(this.state,this.popper,this.reference),e.placement=a(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.offsets.popper=x(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position="absolute",e=T(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,e(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.left="",this.popper.style.position="",this.popper.style.top="",this.popper.style[N("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=P(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return j.call(this)}}]),W);function W(e,t){var n=this,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=this,i=W;if(!(r instanceof i))throw new TypeError("Cannot call a class as a function");this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=V(this.update.bind(this)),this.options=M({},W.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e.jquery?e[0]:e,this.popper=t.jquery?t[0]:t,this.options.modifiers={},Object.keys(M({},W.Defaults.modifiers,o.modifiers)).forEach(function(e){n.options.modifiers[e]=M({},W.Defaults.modifiers[e]||{},o.modifiers?o.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return M({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&s(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}function Q(e,t){for(var n,o=0;o<t.length;o++)(n=t[o]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return t.Utils=("undefined"==typeof window?global:window).PopperUtils,t.placements=_,t.Defaults={placement:"bottom",eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,n,o,r=e.placement,i=r.split("-")[0],r=r.split("-")[1];return r&&(t=(n=e.offsets).reference,n=n.popper,o=(i=-1!==["bottom","top"].indexOf(i))?"width":"height",i={start:D({},i=i?"left":"top",t[i]),end:D({},i,t[i]+t[o]-n[o])},e.offsets.popper=M({},n,i[r])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var t=t.offset,n=e.placement,o=(r=e.offsets).popper,r=r.reference,n=n.split("-")[0],t=k(+t)?[+t,0]:U(t,o,r,n);return"left"===n?(o.top+=t[0],o.left-=t[1]):"right"===n?(o.top+=t[0],o.left+=t[1]):"top"===n?(o.left+=t[0],o.top-=t[1]):"bottom"===n&&(o.left+=t[0],o.top+=t[1]),e.popper=o,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,o){var t=o.boundariesElement||p(e.instance.popper),r=(e.instance.reference===t&&(t=p(t)),v(e.instance.popper,e.instance.reference,o.padding,t)),t=(o.boundaries=r,o.priority),i=e.offsets.popper,n={primary:function(e){var t=i[e];return i[e]<r[e]&&!o.escapeWithReference&&(t=C(i[e],r[e])),D({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=i[t];return i[e]>r[e]&&!o.escapeWithReference&&(n=Y(i[t],r[e]-("right"===e?i.width:i.height))),D({},t,n)}};return t.forEach(function(e){var t=-1===["left","top"].indexOf(e)?"secondary":"primary";i=M({},i,n[t](e))}),e.offsets.popper=i,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,t=t.reference,o=e.placement.split("-")[0],r=B,o=-1!==["top","bottom"].indexOf(o),i=o?"right":"bottom",s=o?"left":"top",o=o?"width":"height";return n[i]<r(t[s])&&(e.offsets.popper[s]=r(t[s])-n[o]),n[s]>r(t[i])&&(e.offsets.popper[s]=r(t[i])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){if(F(e.instance.modifiers,"arrow","keepTogether")){t=t.element;if("string"==typeof t){if(!(t=e.instance.popper.querySelector(t)))return e}else if(!e.instance.popper.contains(t))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var n=e.placement.split("-")[0],o=e.offsets,r=o.popper,o=o.reference,n=-1!==["left","right"].indexOf(n),i=n?"height":"width",s=n?"Top":"Left",f=s.toLowerCase(),a=n?"left":"top",n=n?"bottom":"right",p=O(t)[i],n=(o[n]-p<r[f]&&(e.offsets.popper[f]-=r[f]-(o[n]-p)),o[f]+p>r[n]&&(e.offsets.popper[f]+=o[f]+p-r[n]),o[f]+o[i]/2-p/2),o=d(e.instance.popper,"margin"+s).replace("px",""),s=n-b(e.offsets.popper)[f]-o,s=C(Y(r[i]-p,s),0);e.arrowElement=t,e.offsets.arrow={},e.offsets.arrow[f]=Math.round(s),e.offsets.arrow[a]=""}return e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(a,p){if(!(e(a.instance.modifiers,"inner")||a.flipped&&a.placement===a.originalPlacement)){var l=v(a.instance.popper,a.instance.reference,p.padding,p.boundariesElement),d=a.placement.split("-")[0],c=E(d),u=a.placement.split("-")[1]||"",h=[];switch(p.behavior){case X:h=[d,c];break;case J:h=R(d);break;case K:h=R(d,!0);break;default:h=p.behavior}h.forEach(function(e,t){if(d!==e||h.length===t+1)return a;d=a.placement.split("-")[0],c=E(d);var e=a.offsets.popper,n=a.offsets.reference,o=B,n="left"===d&&o(e.right)>o(n.left)||"right"===d&&o(e.left)<o(n.right)||"top"===d&&o(e.bottom)>o(n.top)||"bottom"===d&&o(e.top)<o(n.bottom),r=o(e.left)<o(l.left),i=o(e.right)>o(l.right),s=o(e.top)<o(l.top),e=o(e.bottom)>o(l.bottom),o="left"===d&&r||"right"===d&&i||"top"===d&&s||"bottom"===d&&e,f=-1!==["top","bottom"].indexOf(d),r=!!p.flipVariations&&(f&&"start"===u&&r||f&&"end"===u&&i||!f&&"start"===u&&s||!f&&"end"===u&&e);(n||o||r)&&(a.flipped=!0,(n||o)&&(d=h[t+1]),r&&(u="end"===(i=u)?"start":"start"===i?"end":i),a.placement=d+(u?"-"+u:""),a.offsets.popper=M({},a.offsets.popper,x(a.instance.popper,a.offsets.reference,a.placement)),a=T(a.instance.modifiers,a,"flip"))})}return a},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],o=e.offsets,r=o.popper,o=o.reference,i=-1!==["left","right"].indexOf(n),s=-1===["top","left"].indexOf(n);return r[i?"left":"top"]=o[n]-(s?r[i?"width":"height"]:0),e.placement=E(t),e.offsets.popper=b(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(F(e.instance.modifiers,"hide","preventOverflow")){var t=e.offsets.reference,n=L(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,o=t.y,r=e.offsets.popper,i=L(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration;void 0!==i&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var t=void 0===i?t.gpuAcceleration:i,i=l(p(e.instance.popper)),s={position:r.position},r={left:B(r.left),top:B(r.top),bottom:B(r.bottom),right:B(r.right)},n="bottom"===n?"top":"bottom",o="right"===o?"left":"right",f=N("transform"),a="bottom"==n?-i.height+r.bottom:r.top,i="right"==o?-i.width+r.right:r.left,t=(t&&f?(s[f]="translate3d("+i+"px, "+a+"px, 0)",s[n]=0,s[o]=0,s.willChange="transform"):(r="right"==o?-1:1,s[n]=a*("bottom"==n?-1:1),s[o]=i*r,s.willChange=n+", "+o),{"x-placement":e.placement});return e.attributes=M({},t,e.attributes),e.styles=M({},s,e.styles),e.arrowStyles=M({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){return A(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1===n[e]?t.removeAttribute(e):t.setAttribute(e,n[e])}),e.arrowElement&&Object.keys(e.arrowStyles).length&&A(e.arrowElement,e.arrowStyles),e;var t,n},onLoad:function(e,t,n,o,r){var i=y(0,t,e),i=a(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",i),A(t,{position:"absolute"}),n},gpuAcceleration:void 0}}},t});
//# sourceMappingURL=popper.min.js.map